"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const two_factor_dto_1 = require("../dto/auth/two-factor.dto");
const speakeasy = __importStar(require("speakeasy"));
const qrcode = __importStar(require("qrcode"));
const bcrypt = __importStar(require("bcryptjs"));
const mailer_1 = require("@nestjs-modules/mailer");
const path_1 = require("path");
const app_module_1 = require("../app.module");
let AuthService = AuthService_1 = class AuthService {
    usersService;
    jwtService;
    mailerService;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(usersService, jwtService, mailerService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
        this.mailerService = mailerService;
    }
    async validateUser(email, password) {
        this.logger.log(`Validating user: ${email}`);
        const user = await this.usersService.findByEmail(email);
        this.logger.log(`User found: ${user ? 'YES' : 'NO'}`);
        if (user) {
            const isPasswordValid = await this.usersService.validatePassword(password, user.password);
            this.logger.log(`Password valid: ${isPasswordValid ? 'YES' : 'NO'}`);
            if (isPasswordValid) {
                return user;
            }
        }
        return null;
    }
    async login(loginDto) {
        try {
            this.logger.log(`Login attempt for email: ${loginDto.email}`);
            const user = await this.validateUser(loginDto.email, loginDto.password);
            this.logger.log(`Validation result for ${loginDto.email}: ${user ? 'SUCCESS' : 'FAILED'}`);
            if (!user) {
                this.logger.warn(`Invalid credentials for email: ${loginDto.email}`);
                throw new common_1.UnauthorizedException('Invalid email or password');
            }
            if (user.status !== 'active') {
                this.logger.warn(`Inactive account login attempt: ${loginDto.email}`);
                throw new common_1.UnauthorizedException('Account is not active');
            }
            await this.usersService.updateLastLogin(user.user_id);
            const payload = {
                email: user.email,
                sub: user.user_id,
                roles: user.roles?.map(role => role.name) || [],
            };
            return {
                access_token: this.jwtService.sign(payload),
                user: {
                    user_id: user.user_id,
                    email: user.email,
                    first_name: user.first_name,
                    last_name: user.last_name,
                    two_factor_enabled: user.two_factor_enabled,
                    roles: user.roles?.map(role => role.name) || [],
                },
            };
        }
        catch (error) {
            this.logger.error(`Login failed for ${loginDto.email}: ${error.message}`);
            throw error;
        }
    }
    async register(registerDto) {
        try {
            const user = await this.usersService.create(registerDto);
            const payload = {
                email: user.email,
                sub: user.user_id,
                roles: user.roles?.map(role => role.name) || [],
            };
            return {
                access_token: this.jwtService.sign(payload),
                user: {
                    user_id: user.user_id,
                    email: user.email,
                    first_name: user.first_name,
                    last_name: user.last_name,
                    two_factor_enabled: user.two_factor_enabled,
                    roles: user.roles?.map(role => role.name) || [],
                },
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async validateJwtPayload(payload) {
        return this.usersService.findByEmail(payload.email);
    }
    async forgotPassword(forgotPasswordDto) {
        const user = await this.usersService.findByEmail(forgotPasswordDto.email);
        if (!user) {
            return { message: 'If the email exists, a password reset link has been sent.' };
        }
        await this.generateTwoFactorCode(user.user_id, 'reset');
        return { message: 'If the email exists, a password reset link has been sent.' };
    }
    async resetPassword(resetPasswordDto) {
        const user = await this.usersService.findById(resetPasswordDto.user_id);
        if (!user) {
            throw new common_1.BadRequestException('Invalid reset code');
        }
        if (await this.usersService.validatePassword(resetPasswordDto.new_password, user.password)) {
            throw new common_1.BadRequestException("New password cannot be the same as the current password");
        }
        await this.usersService.updatePassword(user.user_id, resetPasswordDto.new_password);
        await this.usersService.clearTempTwoFactorCode(user.user_id);
        await this.usersService.clearTwoFactorCode(user.user_id);
        this.mailerService.sendMail({
            to: user.email,
            subject: 'Password Reset - MACRA Digital Portal',
            template: 'reset',
            context: {
                userName: user.first_name,
                year: new Date().getFullYear(),
                loginUrl: `${process.env.FRONTEND_URL}/auth/login`
            },
            attachments: [
                {
                    filename: 'macra-logo.png',
                    path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                    cid: 'logo@macra'
                }
            ]
        }).catch((error) => {
            this.logger.error('An error occurred while mailing successful password reset', error);
        });
        return { message: `Password reset successfully for ${user.email}! Please login with your new password.` };
    }
    async setupTwoFactorAuth(requestTwoFactorDto) {
        const userId = requestTwoFactorDto.user_id;
        const user = await this.usersService.findById(userId);
        if (!user) {
            this.logger.warn(`First time 2FA setup failed: User ID ${userId} not found!`);
            throw new common_1.BadRequestException('User not found');
        }
        if (user.two_factor_enabled) {
            return {
                otpAuthUrl: '',
                qrCodeDataUrl: '',
                secret: '',
                message: `Two-factor authentication is already enabled for ${user.email}.\n\n Redirecting to login..`,
            };
        }
        const generateCode = await this.generateTwoFactorCode(requestTwoFactorDto.user_id, 'verify');
        const otpAuthUrl = generateCode.otpAuthUrl;
        const secret = generateCode.secret;
        const qrCodeDataUrl = await qrcode.toDataURL(otpAuthUrl);
        return {
            otpAuthUrl,
            qrCodeDataUrl,
            secret: secret,
            message: `Two factor authentication initiation for ${user.email} successful! Please check your email for the verification link.`
        };
    }
    async generateTwoFactorCode(userId, action) {
        const user = await this.usersService.findById(userId);
        if (!user) {
            this.logger.warn(`2FA attempt failed: User ID ${userId} not found!`);
            throw new common_1.BadRequestException('User not found');
        }
        const secret = speakeasy.generateSecret({
            name: 'MACRA Digital Portal',
            length: 16,
        });
        const token = speakeasy.totp({
            secret: secret.base32,
            encoding: 'base32'
        });
        const hashedToken = await bcrypt.hash(token, 8);
        const expiresAt = new Date(Date.now() + 5 * 60 * 1000);
        const url_redirect = (action === 'reset') ? 'reset-password' : ((action === 'login') ? 'login' : 'verify-2fa');
        const msg = (action === 'reset') ? `You are receiving this email because we received a password reset request for your account.\nIgnore if received in error` : ((action === 'login') ? `You are receiving this email because we received a login request for your account.\nIgnore if received in error` : `You are receiving this email because we received a 2FA verification request for your account.\nIgnore if received in error`);
        const template = (action === 'reset') ? 'reset' : ((action === 'login') ? '2fa' : '2fa');
        if (!secret.otpauth_url) {
            throw new common_1.InternalServerErrorException('Failed to generate OTP URL');
        }
        try {
            if (action === 'login') {
                await this.usersService.setTwoFactorCode(userId, hashedToken, expiresAt);
            }
            else {
                await this.usersService.setTwoFactorCodeTempReset(userId, secret.base32, hashedToken, expiresAt);
            }
        }
        catch (error) {
            this.logger.error('An error occurred while setting the 2FA Code', error);
        }
        this.mailerService.sendMail({
            to: user.email,
            subject: 'Verify OTP - MACRA Digital Portal',
            template: template,
            context: {
                name: user.first_name,
                message: msg,
                year: new Date().getFullYear(),
                verifyUrl: `${process.env.FRONTEND_URL}/auth/${url_redirect}?i=${encodeURIComponent(user.user_id)}&unique=${encodeURIComponent(secret.base32)}&c=${encodeURIComponent(token)}`
            },
            attachments: [
                {
                    filename: 'macra-logo.png',
                    path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                    cid: 'logo@macra'
                }
            ]
        }).catch(error => {
            this.logger.error('An error occurred while mailing OTP', error);
        });
        return { message: '2FA code has been sent', otpAuthUrl: secret.otpauth_url, hashedToken: hashedToken, secret: secret.base32 };
    }
    async verifyTwoFactorCode(twoFactorDto) {
        const user = await this.usersService.findById(twoFactorDto.user_id);
        if (!user) {
            throw new common_1.BadRequestException('Invalid user account details!');
        }
        if (!user.two_factor_code || !user.two_factor_temp || user.two_factor_temp !== twoFactorDto.unique) {
            this.logger.warn(`Invalid unique code for user ${user.email}`);
            throw new common_1.BadRequestException('Invalid verification link!');
        }
        if (!user.two_factor_next_verification || user.two_factor_next_verification < new Date()) {
            if (user.two_factor_next_verification) {
                this.logger.warn(`Expired code. Expired on: `, user.two_factor_next_verification);
            }
            throw new common_1.BadRequestException('Expired verification link!');
        }
        const compareToken = await bcrypt.compare(twoFactorDto.code, user.two_factor_code);
        if (!compareToken) {
            throw new common_1.BadRequestException('Invalid verification code');
        }
        try {
            await this.usersService.clearTwoFactorCode(user.user_id);
        }
        catch (error) {
            this.logger.error('Failed to clear two factor code', error);
        }
        const payload = { sub: user.user_id, email: user.email, roles: user.roles?.map(r => r.name) };
        return {
            access_token: (user.two_factor_enabled) ? this.jwtService.sign(payload) : '',
            user: {
                user_id: user.user_id, email: user.email, first_name: user.first_name, last_name: user.last_name,
                two_factor_enabled: user.two_factor_enabled, roles: payload.roles
            },
            message: (user.two_factor_enabled) ? 'OTP verified successfully' : `Two-factor authentication enabled for ${user.email}!`
        };
    }
};
exports.AuthService = AuthService;
__decorate([
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [two_factor_dto_1.RequestTwoFactorDto]),
    __metadata("design:returntype", Promise)
], AuthService.prototype, "setupTwoFactorAuth", null);
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService,
        mailer_1.MailerService])
], AuthService);
//# sourceMappingURL=auth.service.js.map