'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import { validateSection } from '@/utils/formValidation';
import { IndependentStepProps } from '../types';
import { applicationFormDataService } from '@/services/applicationFormDataService';

// Stakeholder data structure matching backend entity
interface StakeholderData {
  stakeholder_id?: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  nationality: string;
  position: 'CEO' | 'SHAREHOLDER' | 'AUDITOR' | 'LAWYER';
  profile: string;
  contact_id?: string;
  cv_document_id?: string;
}

interface ManagementProps extends IndependentStepProps {}

const Management: React.FC<ManagementProps> = ({
  applicationId,
  licenseTypeId,
  licenseCategoryId,
  isEditMode = false,
  onStepComplete,
  onStepError,
  onNavigate
}) => {
  const [localData, setLocalData] = useState({
    stakeholders: [] as StakeholderData[],
    organizational_structure: '',
    key_personnel: '',
    management_experience: '',
    leadership_approach: '',
    succession_planning: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load existing data when component mounts (for edit mode)
  useEffect(() => {
    const loadExistingData = async () => {
      // Add more robust validation for applicationId
      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {
        console.log('Loading existing stakeholders data for application:', applicationId);
        setIsLoading(true);
        try {
          // TODO: Replace with stakeholders API call when available
          // For now, use the form data service as fallback
          const existingData = await applicationFormDataService.getFormSection(applicationId, 'management');
          if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {
            console.log('Loaded existing management data:', existingData.section_data);
            setLocalData(prev => ({ ...prev, ...existingData.section_data }));
          } else {
            console.log('No existing management data found for application:', applicationId);
          }
        } catch (error) {
          console.error('Error loading existing management data:', error);
          // Don't call onStepError as it would hide the form
          // Just log the error and continue with empty form
          console.log('Continuing with empty form due to load error');
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('Skipping management data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });
      }
    };

    loadExistingData();
  }, [applicationId, isEditMode]);

  // Handle local data changes
  const handleLocalChange = (field: string, value: any) => {
    setLocalData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validation function
  const validateForm = () => {
    const validation = validateSection(localData, 'management');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  // Handle save
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      // TODO: Replace with stakeholders API call when available
      // For now, use the form data service as fallback
      await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'management', localData);
      setHasUnsavedChanges(false);
      console.log('Management data saved successfully');

      // Notify parent component of successful save
      if (onStepComplete) {
        onStepComplete('management', localData);
      }
    } catch (error) {
      console.error('Error saving management data:', error);
      if (onStepError) {
        onStepError('management', { save: 'Failed to save management data. Please try again.' });
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Stakeholder management functions
  const addStakeholder = () => {
    const newStakeholder: StakeholderData = {
      first_name: '',
      last_name: '',
      middle_name: '',
      nationality: '',
      position: 'CEO',
      profile: ''
    };
    const updatedStakeholders = [...localData.stakeholders, newStakeholder];
    handleLocalChange('stakeholders', updatedStakeholders);
  };

  const updateStakeholder = (index: number, field: keyof StakeholderData, value: string) => {
    const updatedStakeholders = localData.stakeholders.map((stakeholder, i) =>
      i === index ? { ...stakeholder, [field]: value } : stakeholder
    );
    handleLocalChange('stakeholders', updatedStakeholders);
  };

  const removeStakeholder = (index: number) => {
    const updatedStakeholders = localData.stakeholders.filter((_, i) => i !== index);
    handleLocalChange('stakeholders', updatedStakeholders);
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Management Information
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide details about your organization's management structure and key personnel.
        </p>
      </div>

      {/* Stakeholders */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Key Stakeholders
          </label>
          <button
            type="button"
            onClick={addStakeholder}
            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-add-line mr-1"></i>
            Add Stakeholder
          </button>
        </div>

        {localData.stakeholders.map((stakeholder, index) => (
          <div key={index} className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Stakeholder {index + 1}
              </h4>
              <button
                type="button"
                onClick={() => removeStakeholder(index)}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              >
                <i className="ri-delete-bin-line"></i>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                label="First Name"
                value={stakeholder.first_name}
                onChange={(e) => updateStakeholder(index, 'first_name', e.target.value)}
                required
              />

              <TextInput
                label="Last Name"
                value={stakeholder.last_name}
                onChange={(e) => updateStakeholder(index, 'last_name', e.target.value)}
                required
              />

              <TextInput
                label="Middle Name"
                value={stakeholder.middle_name || ''}
                onChange={(e) => updateStakeholder(index, 'middle_name', e.target.value)}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Position *
                </label>
                <select
                  value={stakeholder.position}
                  onChange={(e) => updateStakeholder(index, 'position', e.target.value as 'CEO' | 'SHAREHOLDER' | 'AUDITOR' | 'LAWYER')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  required
                >
                  <option value="CEO">CEO</option>
                  <option value="SHAREHOLDER">Shareholder</option>
                  <option value="AUDITOR">Auditor</option>
                  <option value="LAWYER">Lawyer</option>
                </select>
              </div>

              <TextInput
                label="Nationality"
                value={stakeholder.nationality}
                onChange={(e) => updateStakeholder(index, 'nationality', e.target.value)}
                required
              />

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Profile/Background *
                </label>
                <textarea
                  value={stakeholder.profile}
                  onChange={(e) => updateStakeholder(index, 'profile', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Professional background, qualifications, and experience..."
                  required
                />
              </div>
            </div>
          </div>
        ))}

        {localData.stakeholders.length === 0 && (
          <div className="text-center py-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">No stakeholders added yet.</p>
            <button
              type="button"
              onClick={addStakeholder}
              className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-add-line mr-1"></i>
              Add First Stakeholder
            </button>
          </div>
        )}
      </div>

      {/* Organizational Structure */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Organizational Structure *
        </label>
        <textarea
          value={localData.organizational_structure || ''}
          onChange={(e) => handleLocalChange('organizational_structure', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe your organizational structure, reporting lines, and governance framework..."
        />
        {validationErrors.organizational_structure && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.organizational_structure}
          </p>
        )}
      </div>

      {/* Key Personnel */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Key Personnel *
        </label>
        <textarea
          value={localData.key_personnel || ''}
          onChange={(e) => handleLocalChange('key_personnel', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="List key personnel and their roles in the organization..."
        />
        {validationErrors.key_personnel && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.key_personnel}
          </p>
        )}
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-4">
        <button
          type="button"
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Management Info
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default Management;
