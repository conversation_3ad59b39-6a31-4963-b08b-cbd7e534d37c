import { LicenseTypesService } from './license-types.service';
import { CreateLicenseTypeDto } from '../dto/license-types/create-license-type.dto';
import { UpdateLicenseTypeDto } from '../dto/license-types/update-license-type.dto';
import { LicenseTypes } from '../entities/license-types.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class LicenseTypesController {
    private readonly licenseTypesService;
    constructor(licenseTypesService: LicenseTypesService);
    findAll(query: PaginateQuery): Promise<PaginatedResult<LicenseTypes>>;
    findOne(id: string): Promise<LicenseTypes>;
    create(createLicenseTypeDto: CreateLicenseTypeDto, req: any): Promise<LicenseTypes>;
    update(id: string, updateLicenseTypeDto: UpdateLicenseTypeDto, req: any): Promise<LicenseTypes>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
