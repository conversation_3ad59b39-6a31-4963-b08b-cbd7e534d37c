{"version": 3, "file": "documents.service.js", "sourceRoot": "", "sources": ["../../src/documents/documents.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,mEAAuE;AAGvE,qDAAqF;AAG9E,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAGjB;IAFV,YAEU,mBAA0C;QAA1C,wBAAmB,GAAnB,mBAAmB,CAAuB;IACjD,CAAC;IAEa,cAAc,GAA8B;QAC3D,eAAe,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,CAAC;QAC3E,iBAAiB,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,aAAa,CAAC;QAChE,aAAa,EAAE,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACvC,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;KACjD,CAAC;IAEF,KAAK,CAAC,MAAM,CAAC,iBAAoC,EAAE,SAAiB;QAClE,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC/C,GAAG,iBAAiB;YACpB,WAAW,EAAE,iBAAiB,CAAC,WAAW,IAAI,KAAK;YACnD,UAAU,EAAE,SAAS;SACtB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,KAAoB;QAChC,OAAO,IAAA,0BAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YACtD,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE;YAC1B,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;SACjD,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,aAAqB;QAC3C,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACnC,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE;YACxC,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;YAChD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,QAAgB;QACrD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACnC,KAAK,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE;YACvD,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;YAChD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,YAA0B;QACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACnC,KAAK,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE;YACtC,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;YAChD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACnC,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;YAC5B,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;YAChD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,iBAAoC,EAAE,SAAiB;QAC9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAExC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACxC,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB;aACzC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,MAAM,CAAC,wBAAwB,EAAE,eAAe,CAAC;aACjD,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,wBAAwB,CAAC;aACjC,UAAU,EAAE,CAAC;QAEhB,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAChC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,QAAgB;QAC3C,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACnC,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE;YAC9B,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;YAChD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB;aAC1C,kBAAkB,CAAC,UAAU,CAAC;aAC9B,MAAM,CAAC,yBAAyB,EAAE,YAAY,CAAC;aAC/C,SAAS,EAAE,CAAC;QAEf,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;CACF,CAAA;AApHY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,4BAAS,CAAC,CAAA;qCACC,oBAAU;GAH9B,gBAAgB,CAoH5B"}