"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const applications_service_1 = require("./applications.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_application_dto_1 = require("../dto/application/create-application.dto");
const update_application_dto_1 = require("../dto/application/update-application.dto");
const applications_entity_1 = require("../entities/applications.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let ApplicationsController = class ApplicationsController {
    applicationsService;
    constructor(applicationsService) {
        this.applicationsService = applicationsService;
    }
    async create(createApplicationDto, req) {
        return this.applicationsService.create(createApplicationDto, req.user.userId);
    }
    async findAll(query, req) {
        const userRoles = req.user?.roles || [];
        const result = await this.applicationsService.findAll(query, userRoles);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async getStats() {
        return this.applicationsService.getApplicationStats();
    }
    async findByApplicant(applicantId) {
        return this.applicationsService.findByApplicant(applicantId);
    }
    async findByStatus(status) {
        return this.applicationsService.findByStatus(status);
    }
    async findOne(id) {
        return this.applicationsService.findOne(id);
    }
    async update(id, updateApplicationDto, req) {
        return this.applicationsService.update(id, updateApplicationDto, req.user.userId);
    }
    async updateStatus(id, status, req) {
        return this.applicationsService.updateStatus(id, status, req.user.userId);
    }
    async updateProgress(id, currentStep, progressPercentage, req) {
        return this.applicationsService.updateProgress(id, currentStep, progressPercentage, req.user.userId);
    }
    async remove(id) {
        await this.applicationsService.remove(id);
        return { message: 'Application deleted successfully' };
    }
};
exports.ApplicationsController = ApplicationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new application' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Application created successfully',
        type: applications_entity_1.Applications,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        resourceType: 'Application',
        description: 'Created new application',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_application_dto_1.CreateApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all applications with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applications retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Viewed applications list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get application statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application statistics retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Viewed application statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('by-applicant/:applicantId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get applications by applicant' }),
    (0, swagger_1.ApiParam)({ name: 'applicantId', description: 'Applicant UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applications retrieved successfully',
        type: [applications_entity_1.Applications],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Viewed applications by applicant',
    }),
    __param(0, (0, common_1.Param)('applicantId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "findByApplicant", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get applications by status' }),
    (0, swagger_1.ApiParam)({ name: 'status', description: 'Application status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applications retrieved successfully',
        type: [applications_entity_1.Applications],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Viewed applications by status',
    }),
    __param(0, (0, common_1.Param)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get application by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application retrieved successfully',
        type: applications_entity_1.Applications,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Viewed application details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update application' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application updated successfully',
        type: applications_entity_1.Applications,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Updated application',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_application_dto_1.UpdateApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update application status' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application UUID' }),
    (0, swagger_1.ApiQuery)({ name: 'status', description: 'New status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application status updated successfully',
        type: applications_entity_1.Applications,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Updated application status',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Put)(':id/progress'),
    (0, swagger_1.ApiOperation)({ summary: 'Update application progress' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application UUID' }),
    (0, swagger_1.ApiQuery)({ name: 'currentStep', description: 'Current step (1-6)' }),
    (0, swagger_1.ApiQuery)({ name: 'progressPercentage', description: 'Progress percentage (0-100)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application progress updated successfully',
        type: applications_entity_1.Applications,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Updated application progress',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('currentStep', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('progressPercentage', common_1.ParseIntPipe)),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "updateProgress", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete application' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Application',
        description: 'Deleted application',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "remove", null);
exports.ApplicationsController = ApplicationsController = __decorate([
    (0, swagger_1.ApiTags)('applications'),
    (0, common_1.Controller)('applications'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [applications_service_1.ApplicationsService])
], ApplicationsController);
//# sourceMappingURL=applications.controller.js.map