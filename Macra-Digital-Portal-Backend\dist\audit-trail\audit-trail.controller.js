"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditTrailController = void 0;
const common_1 = require("@nestjs/common");
const audit_trail_service_1 = require("./audit-trail.service");
const audit_trail_query_dto_1 = require("../dto/audit-trail/audit-trail-query.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const nestjs_paginate_1 = require("nestjs-paginate");
const swagger_1 = require("@nestjs/swagger");
let AuditTrailController = class AuditTrailController {
    auditTrailService;
    constructor(auditTrailService) {
        this.auditTrailService = auditTrailService;
    }
    async findAll(query, filters) {
        return this.auditTrailService.findAll(query, filters);
    }
    async findOne(id) {
        return this.auditTrailService.findOne(id);
    }
};
exports.AuditTrailController = AuditTrailController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get paginated audit trail entries with filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns paginated audit trail entries' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, audit_trail_query_dto_1.AuditTrailQueryDto]),
    __metadata("design:returntype", Promise)
], AuditTrailController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get audit trail entry by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns audit trail entry details' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Audit trail entry not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuditTrailController.prototype, "findOne", null);
exports.AuditTrailController = AuditTrailController = __decorate([
    (0, swagger_1.ApiTags)('audit-trail'),
    (0, common_1.Controller)('audit-trail'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [audit_trail_service_1.AuditTrailService])
], AuditTrailController);
//# sourceMappingURL=audit-trail.controller.js.map