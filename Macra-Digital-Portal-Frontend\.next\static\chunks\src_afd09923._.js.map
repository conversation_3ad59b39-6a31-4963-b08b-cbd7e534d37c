{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,sBAAsB;gBACtB,0BAA0B;gBAC1B,oCAAoC;gBACpC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,8BAA8B;gBAC9B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;YAC1D,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,6LAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlVM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACC,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,aAAU;;;KAN7B;uCAoVS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\n\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheItem<any>>();\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\n\n  /**\n   * Set cache item with TTL\n   */\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    const item: CacheItem<T> = {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    };\n    \n    this.cache.set(key, item);\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\n  }\n\n  /**\n   * Get cache item if not expired\n   */\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    \n    if (!item) {\n      console.log(`Cache MISS: ${key}`);\n      return null;\n    }\n\n    const now = Date.now();\n    if (now > item.expiresAt) {\n      console.log(`Cache EXPIRED: ${key}`);\n      this.cache.delete(key);\n      return null;\n    }\n\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\n    return item.data as T;\n  }\n\n  /**\n   * Check if cache has valid item\n   */\n  has(key: string): boolean {\n    return this.get(key) !== null;\n  }\n\n  /**\n   * Delete cache item\n   */\n  delete(key: string): boolean {\n    console.log(`Cache DELETE: ${key}`);\n    return this.cache.delete(key);\n  }\n\n  /**\n   * Clear all cache\n   */\n  clear(): void {\n    console.log('Cache CLEAR: All items');\n    this.cache.clear();\n  }\n\n  /**\n   * Get cache stats\n   */\n  getStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Clean expired items\n   */\n  cleanup(): void {\n    const now = Date.now();\n    let cleaned = 0;\n\n    for (const [key, item] of this.cache.entries()) {\n      if (now > item.expiresAt) {\n        this.cache.delete(key);\n        cleaned++;\n      }\n    }\n\n    if (cleaned > 0) {\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\n    }\n  }\n\n  /**\n   * Get or set pattern - fetch data if not cached\n   */\n  async getOrSet<T>(\n    key: string,\n    fetcher: () => Promise<T>,\n    ttl: number = this.defaultTTL\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    console.log(`Cache FETCH: ${key}`);\n    const data = await fetcher();\n    \n    // Store in cache\n    this.set(key, data, ttl);\n    \n    return data;\n  }\n\n  /**\n   * Invalidate cache by pattern\n   */\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    let invalidated = 0;\n\n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n        invalidated++;\n      }\n    }\n\n    if (invalidated > 0) {\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\n    }\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Cache keys constants\nexport const CACHE_KEYS = {\n  LICENSE_TYPES: 'license-types',\n  LICENSE_CATEGORIES: 'license-categories',\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\n  USER_APPLICATIONS: 'user-applications',\n  APPLICATION: (id: string) => `application-${id}`,\n} as const;\n\n// Cache TTL constants (in milliseconds)\nexport const CACHE_TTL = {\n  SHORT: 2 * 60 * 1000,      // 2 minutes\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\n  LONG: 15 * 60 * 1000,      // 15 minutes\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\n} as const;\n\n// Auto cleanup every 5 minutes\nsetInterval(() => {\n  cacheService.cleanup();\n}, 5 * 60 * 1000);\n\nexport default cacheService;\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Types\nexport interface LicenseType {\n  license_type_id: string;\n  name: string;\n  code?: string;\n  description: string;\n  validity: number;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n}\n\nexport interface CreateLicenseTypeDto {\n  name: string;\n  description: string;\n  validity: number;\n}\n\nexport interface UpdateLicenseTypeDto {\n  name?: string;\n  description?: string;\n  validity?: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseTypesResponse = PaginatedResponse<LicenseType>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseTypeService = {\n  // Get all license types with pagination\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license type by ID\n  async getLicenseType(id: string): Promise<LicenseType> {\n    const response = await apiClient.get(`/license-types/${id}`);\n    return response.data;\n  },\n\n  // Create new license type\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\n    const response = await apiClient.post('/license-types', licenseTypeData);\n    return response.data;\n  },\n\n  // Update license type\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\n    return response.data;\n  },\n\n  // Delete license type\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-types/${id}`);\n    return response.data;\n  },\n\n  // Get all license types (simple list for dropdowns) with caching\n  async getAllLicenseTypes(): Promise<LicenseType[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_TYPES,\n      async () => {\n        console.log('Fetching license types from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseTypes({ limit: 100 });\n        return response.data;\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAwEO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;AACF", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { LicenseType } from './licenseTypeService';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Utility functions for category codes\nexport const generateCategoryCode = (name: string): string => {\n  return name\n    .toLowerCase()\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    .substring(0, 50); // Limit length\n};\n\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\n  return categories.map(category => ({\n    ...category,\n    code: generateCategoryCode(category.name),\n    children: category.children ? addCodesToCategories(category.children) : undefined\n  }));\n};\n\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\n  for (const category of categories) {\n    if (category.code === code) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryByCode(category.children, code);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\n  for (const category of categories) {\n    if (category.license_category_id === id) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryById(category.children, id);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\n// Types\nexport interface LicenseCategory {\n  license_category_id: string;\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  license_type?: LicenseType;\n  parent?: LicenseCategory;\n  children?: LicenseCategory[];\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  // Generated code for URL-friendly routing\n  code?: string;\n}\n\nexport interface CreateLicenseCategoryDto {\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n}\n\nexport interface UpdateLicenseCategoryDto {\n  license_type_id?: string;\n  parent_id?: string;\n  name?: string;\n  fee?: string;\n  description?: string;\n  authorizes?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseCategoryService = {\n  // Get all license categories with pagination\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license category by ID\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\n    const response = await apiClient.get(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get license categories by license type\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<LicenseCategory[]> {\n    const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n    return response.data;\n  },\n\n  // Create new license category\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\n    return response.data;\n  },\n\n  // Update license category\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\n    return response.data;\n  },\n\n  // Delete license category\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get all license categories (simple list for dropdowns) with caching\n  async getAllLicenseCategories(): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_CATEGORIES,\n      async () => {\n        console.log('Fetching license categories from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseCategories({ limit: 100 });\n        return addCodesToCategories(response.data);\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n\n  // Get hierarchical tree of categories for a license type with caching\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `category-tree-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\n        return addCodesToCategories(response.data);\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get root categories (no parent) for a license type with caching\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `root-categories-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\n        return response.data;\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get license categories for parent selection dropdown\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    try {\n      const params = excludeId ? { excludeId } : {};\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\n\n      // Try the new endpoint first\n      try {\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\n\n\n        if (response.data && Array.isArray(response.data.data)) {\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\n          return response.data.data;\n        } else {\n          console.warn('⚠️ API returned non-array data:', response.data);\n          return [];\n        }\n      } catch (newEndpointError) {\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\n\n        // Fallback to existing endpoint\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n        console.log('🔄 Fallback response:', response.data);\n\n        if (response.data && Array.isArray(response.data)) {\n          // Filter out the excluded category if specified\n          let categories = response.data;\n          if (excludeId) {\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\n          }\n          console.log('✅ Fallback successful with', categories.length, 'items');\n          return categories;\n        } else {\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\n          return [];\n        }\n      }\n    } catch (error) {\n\n      return [];\n    }\n  },\n\n  // Get potential parent categories for a license type\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    const params = excludeId ? { excludeId } : {};\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,IAAI,KAAK,MAAM;YAC1B,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAqFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,oBAAmB,EAAU;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,yCAAyC;IACzC,MAAM,4BAA2B,aAAqB;QACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC3F,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useLicenseData.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\n\r\nimport { licenseTypeService, LicenseType } from '@/services/licenseTypeService';\r\nimport { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';\r\nimport { setLicenseTypeUUIDToCodeMap } from '@/config/licenseTypeStepConfig';\r\n\r\ninterface UseLicenseTypesReturn {\r\n  licenseTypes: LicenseType[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  refetch: () => void;\r\n}\r\n\r\ninterface UseLicenseCategoriesReturn {\r\n  categories: LicenseCategory[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  refetch: () => void;\r\n  getCategoriesByType: (licenseTypeId: string) => LicenseCategory[];\r\n}\r\n\r\nexport const useLicenseTypes = (): UseLicenseTypesReturn => {\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchLicenseTypes = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await licenseTypeService.getAllLicenseTypes();\r\n      setLicenseTypes(data);\r\n\r\n      // Create UUID to code mapping for step configuration\r\n      const uuidToCodeMap: Record<string, string> = {};\r\n      console.log('License types received:', data);\r\n      data.forEach(licenseType => {\r\n        console.log('Processing license type:', licenseType.name, 'code:', licenseType.code, 'id:', licenseType.license_type_id);\r\n        if (licenseType.code) {\r\n          uuidToCodeMap[licenseType.license_type_id] = licenseType.code;\r\n        }\r\n      });\r\n      console.log('Setting UUID to code map:', uuidToCodeMap);\r\n      setLicenseTypeUUIDToCodeMap(uuidToCodeMap);\r\n    } catch (err: any) {\r\n      let errorMessage = 'Failed to fetch license types';\r\n\r\n      // Handle rate limiting specifically\r\n      if (err.response?.status === 429) {\r\n        errorMessage = 'Too many requests. Please wait a moment and try again.';\r\n        console.warn('Rate limit hit for license types, using cached data if available');\r\n\r\n        // Try to use any cached data as fallback\r\n        try {\r\n          const cachedData = await licenseTypeService.getAllLicenseTypes();\r\n          if (cachedData && cachedData.length > 0) {\r\n            setLicenseTypes(cachedData);\r\n            setError(null);\r\n            return;\r\n          }\r\n        } catch (cacheErr) {\r\n          console.error('No cached data available:', cacheErr);\r\n        }\r\n      } else {\r\n        errorMessage = err.response?.data?.message || errorMessage;\r\n      }\r\n\r\n      setError(errorMessage);\r\n      console.error('Error fetching license types:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchLicenseTypes();\r\n  }, []);\r\n\r\n  return {\r\n    licenseTypes,\r\n    loading,\r\n    error,\r\n    refetch: fetchLicenseTypes,\r\n  };\r\n};\r\n\r\nexport const useLicenseCategories = (): UseLicenseCategoriesReturn => {\r\n  const [categories, setCategories] = useState<LicenseCategory[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchCategories = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await licenseCategoryService.getAllLicenseCategories();\r\n      setCategories(data);\r\n    } catch (err: any) {\r\n      let errorMessage = 'Failed to fetch license categories';\r\n\r\n      // Handle rate limiting specifically\r\n      if (err.response?.status === 429) {\r\n        errorMessage = 'Too many requests. Please wait a moment and try again.';\r\n        console.warn('Rate limit hit for license categories, using cached data if available');\r\n\r\n        // Try to use any cached data as fallback\r\n        try {\r\n          const cachedData = await licenseCategoryService.getAllLicenseCategories();\r\n          if (cachedData && cachedData.length > 0) {\r\n            setCategories(cachedData);\r\n            setError(null);\r\n            return;\r\n          }\r\n        } catch (cacheErr) {\r\n          console.error('No cached data available:', cacheErr);\r\n        }\r\n      } else {\r\n        errorMessage = err.response?.data?.message || errorMessage;\r\n      }\r\n\r\n      setError(errorMessage);\r\n      console.error('Error fetching license categories:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getCategoriesByType = useCallback((licenseTypeId: string): LicenseCategory[] => {\r\n    return categories.filter(category => category.license_type_id === licenseTypeId);\r\n  }, [categories]);\r\n\r\n  useEffect(() => {\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  return {\r\n    categories,\r\n    loading,\r\n    error,\r\n    refetch: fetchCategories,\r\n    getCategoriesByType,\r\n  };\r\n};\r\n\r\n// Combined hook for both license types and categories\r\nexport const useLicenseData = () => {\r\n  const licenseTypesData = useLicenseTypes();\r\n  const categoriesData = useLicenseCategories();\r\n\r\n  const loading = licenseTypesData.loading || categoriesData.loading;\r\n  const error = licenseTypesData.error || categoriesData.error;\r\n\r\n  const getLicenseTypeWithCategories = (licenseTypeId: string) => {\r\n    const licenseType = licenseTypesData.licenseTypes.find(lt => lt.license_type_id === licenseTypeId);\r\n    const categories = categoriesData.getCategoriesByType(licenseTypeId);\r\n\r\n    return {\r\n      licenseType,\r\n      categories,\r\n    };\r\n  };\r\n\r\n  const refetch = () => {\r\n    licenseTypesData.refetch();\r\n    categoriesData.refetch();\r\n  };\r\n\r\n  return {\r\n    licenseTypes: licenseTypesData.licenseTypes,\r\n    categories: categoriesData.categories,\r\n    loading,\r\n    error,\r\n    refetch,\r\n    getLicenseTypeWithCategories,\r\n    getCategoriesByType: categoriesData.getCategoriesByType,\r\n  };\r\n};\r\n\r\n// Hook specifically for postal/courier license data\r\nexport const usePostalCourierLicenses = () => {\r\n  const { licenseTypes, categories, loading, error, refetch } = useLicenseData();\r\n\r\n  // Find postal/courier license types\r\n  const postalCourierTypes = licenseTypes.filter(lt =>\r\n    lt.name.toLowerCase().includes('postal') ||\r\n    lt.name.toLowerCase().includes('courier') ||\r\n    lt.name.toLowerCase().includes('mail')\r\n  );\r\n\r\n  // Get categories for postal/courier license types\r\n  const postalCourierCategories = categories.filter(cat =>\r\n    postalCourierTypes.some(lt => lt.license_type_id === cat.license_type_id)\r\n  );\r\n\r\n  return {\r\n    licenseTypes: postalCourierTypes,\r\n    categories: postalCourierCategories,\r\n    loading,\r\n    error,\r\n    refetch,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;;AANA;;;;;AAuBO,MAAM,kBAAkB;;IAC7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB;QACxB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YACxD,gBAAgB;YAEhB,qDAAqD;YACrD,MAAM,gBAAwC,CAAC;YAC/C,QAAQ,GAAG,CAAC,2BAA2B;YACvC,KAAK,OAAO,CAAC,CAAA;gBACX,QAAQ,GAAG,CAAC,4BAA4B,YAAY,IAAI,EAAE,SAAS,YAAY,IAAI,EAAE,OAAO,YAAY,eAAe;gBACvH,IAAI,YAAY,IAAI,EAAE;oBACpB,aAAa,CAAC,YAAY,eAAe,CAAC,GAAG,YAAY,IAAI;gBAC/D;YACF;YACA,QAAQ,GAAG,CAAC,6BAA6B;YACzC,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD,EAAE;QAC9B,EAAE,OAAO,KAAU;YACjB,IAAI,eAAe;YAEnB,oCAAoC;YACpC,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChC,eAAe;gBACf,QAAQ,IAAI,CAAC;gBAEb,yCAAyC;gBACzC,IAAI;oBACF,MAAM,aAAa,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;oBAC9D,IAAI,cAAc,WAAW,MAAM,GAAG,GAAG;wBACvC,gBAAgB;wBAChB,SAAS;wBACT;oBACF;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;YACF,OAAO;gBACL,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAChD;YAEA,SAAS;YACT,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA,SAAS;IACX;AACF;GA/Da;AAiEN,MAAM,uBAAuB;;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,4IAAA,CAAA,yBAAsB,CAAC,uBAAuB;YACjE,cAAc;QAChB,EAAE,OAAO,KAAU;YACjB,IAAI,eAAe;YAEnB,oCAAoC;YACpC,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChC,eAAe;gBACf,QAAQ,IAAI,CAAC;gBAEb,yCAAyC;gBACzC,IAAI;oBACF,MAAM,aAAa,MAAM,4IAAA,CAAA,yBAAsB,CAAC,uBAAuB;oBACvE,IAAI,cAAc,WAAW,MAAM,GAAG,GAAG;wBACvC,cAAc;wBACd,SAAS;wBACT;oBACF;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;YACF,OAAO;gBACL,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAChD;YAEA,SAAS;YACT,QAAQ,KAAK,CAAC,sCAAsC;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YACvC,OAAO,WAAW,MAAM;yEAAC,CAAA,WAAY,SAAS,eAAe,KAAK;;QACpE;gEAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA,SAAS;QACT;IACF;AACF;IAxDa;AA2DN,MAAM,iBAAiB;;IAC5B,MAAM,mBAAmB;IACzB,MAAM,iBAAiB;IAEvB,MAAM,UAAU,iBAAiB,OAAO,IAAI,eAAe,OAAO;IAClE,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe,KAAK;IAE5D,MAAM,+BAA+B,CAAC;QACpC,MAAM,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,eAAe,KAAK;QACpF,MAAM,aAAa,eAAe,mBAAmB,CAAC;QAEtD,OAAO;YACL;YACA;QACF;IACF;IAEA,MAAM,UAAU;QACd,iBAAiB,OAAO;QACxB,eAAe,OAAO;IACxB;IAEA,OAAO;QACL,cAAc,iBAAiB,YAAY;QAC3C,YAAY,eAAe,UAAU;QACrC;QACA;QACA;QACA;QACA,qBAAqB,eAAe,mBAAmB;IACzD;AACF;IA/Ba;;QACc;QACF;;;AAgClB,MAAM,2BAA2B;;IACtC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG;IAE9D,oCAAoC;IACpC,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAA,KAC7C,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAC/B,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,cAC/B,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAGjC,kDAAkD;IAClD,MAAM,0BAA0B,WAAW,MAAM,CAAC,CAAA,MAChD,mBAAmB,IAAI,CAAC,CAAA,KAAM,GAAG,eAAe,KAAK,IAAI,eAAe;IAG1E,OAAO;QACL,cAAc;QACd,YAAY;QACZ;QACA;QACA;IACF;AACF;IAtBa;;QACmD", "debugId": null}}, {"offset": {"line": 1525, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info\r\n      const applicantRequiredFields = [\r\n        'applicant_type', 'first_name', 'last_name', 'email', 'phone',\r\n        'national_id', 'date_of_birth', 'nationality', 'gender',\r\n        'postal_address', 'physical_address', 'city', 'district'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Phone validation\r\n      if (data.phone && !/^(\\+265|0)?[1-9]\\d{7,8}$/.test(data.phone)) {\r\n        errors.phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,qCAAqC;YACrC,MAAM,0BAA0B;gBAC9B;gBAAkB;gBAAc;gBAAa;gBAAS;gBACtD;gBAAe;gBAAiB;gBAAe;gBAC/C;gBAAkB;gBAAoB;gBAAQ;aAC/C;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1830, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/LegalHistory.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { validateSection } from '@/utils/formValidation';\r\n\r\ninterface LegalHistoryProps {\r\n  formData: any;\r\n  onChange: (field: string, value: any) => void;\r\n  onSave: (data: any) => Promise<string>;\r\n  errors: Record<string, string>;\r\n  applicationId?: string;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst LegalHistory: React.FC<LegalHistoryProps> = ({\r\n  formData,\r\n  onChange,\r\n  onSave,\r\n  errors,\r\n  applicationId,\r\n  isLoading = false\r\n}) => {\r\n  const [localData, setLocalData] = useState({\r\n    criminal_history: false,\r\n    criminal_details: '',\r\n    bankruptcy_history: false,\r\n    bankruptcy_details: '',\r\n    regulatory_actions: false,\r\n    regulatory_details: '',\r\n    litigation_history: false,\r\n    litigation_details: '',\r\n    compliance_record: '',\r\n    previous_licenses: '',\r\n    declaration_accepted: false,\r\n    ...formData\r\n  });\r\n\r\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  // Sync formData to localData only when formData changes and is different\r\n  useEffect(() => {\r\n    if (formData && Object.keys(formData).length > 0) {\r\n      setLocalData((prev: any) => {\r\n        const newData = { ...prev, ...formData };\r\n        // Only update if there are actual changes to prevent infinite loops\r\n        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);\r\n        return hasChanges ? newData : prev;\r\n      });\r\n    }\r\n  }, [formData]);\r\n\r\n  const handleLocalChange = useCallback((field: string, value: any) => {\r\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\r\n\r\n    // Call onChange with the field and value\r\n    if (onChange) {\r\n      onChange(field, value);\r\n    }\r\n\r\n    // Clear validation error for this field if it exists\r\n    if (validationErrors && validationErrors[field]) {\r\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\r\n    }\r\n  }, [onChange, validationErrors]);\r\n\r\n  const validateForm = () => {\r\n    const validation = validateSection(localData, 'legalHistory');\r\n    setValidationErrors(validation.errors);\r\n    return validation.isValid;\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSaving(true);\r\n    try {\r\n      await onSave(localData);\r\n      console.log('Legal history saved');\r\n    } catch (error) {\r\n      console.error('Error saving legal history:', error);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Legal History & Compliance\r\n        </h3>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n          Please provide information about your legal and compliance history.\r\n        </p>\r\n      </div>\r\n\r\n      {/* Criminal History */}\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label className=\"flex items-center\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={localData.criminal_history || false}\r\n              onChange={(e) => handleLocalChange('criminal_history', e.target.checked)}\r\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\r\n            />\r\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\r\n              I have a criminal history\r\n            </span>\r\n          </label>\r\n        </div>\r\n\r\n        {localData.criminal_history && (\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Criminal History Details *\r\n            </label>\r\n            <textarea\r\n              value={localData.criminal_details || ''}\r\n              onChange={(e) => handleLocalChange('criminal_details', e.target.value)}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              placeholder=\"Please provide details of your criminal history...\"\r\n            />\r\n            {(validationErrors.criminal_details || errors.criminal_details) && (\r\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n                {validationErrors.criminal_details || errors.criminal_details}\r\n              </p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Bankruptcy History */}\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label className=\"flex items-center\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={localData.bankruptcy_history || false}\r\n              onChange={(e) => handleLocalChange('bankruptcy_history', e.target.checked)}\r\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\r\n            />\r\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\r\n              I have a bankruptcy history\r\n            </span>\r\n          </label>\r\n        </div>\r\n\r\n        {localData.bankruptcy_history && (\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Bankruptcy History Details *\r\n            </label>\r\n            <textarea\r\n              value={localData.bankruptcy_details || ''}\r\n              onChange={(e) => handleLocalChange('bankruptcy_details', e.target.value)}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              placeholder=\"Please provide details of your bankruptcy history...\"\r\n            />\r\n            {(validationErrors.bankruptcy_details || errors.bankruptcy_details) && (\r\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n                {validationErrors.bankruptcy_details || errors.bankruptcy_details}\r\n              </p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Regulatory Actions */}\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label className=\"flex items-center\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={localData.regulatory_actions || false}\r\n              onChange={(e) => handleLocalChange('regulatory_actions', e.target.checked)}\r\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\r\n            />\r\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\r\n              I have been subject to regulatory actions\r\n            </span>\r\n          </label>\r\n        </div>\r\n\r\n        {localData.regulatory_actions && (\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Regulatory Actions Details *\r\n            </label>\r\n            <textarea\r\n              value={localData.regulatory_details || ''}\r\n              onChange={(e) => handleLocalChange('regulatory_details', e.target.value)}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              placeholder=\"Please provide details of regulatory actions...\"\r\n            />\r\n            {(validationErrors.regulatory_details || errors.regulatory_details) && (\r\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n                {validationErrors.regulatory_details || errors.regulatory_details}\r\n              </p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Litigation History */}\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label className=\"flex items-center\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={localData.litigation_history || false}\r\n              onChange={(e) => handleLocalChange('litigation_history', e.target.checked)}\r\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\r\n            />\r\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\r\n              I have been involved in litigation\r\n            </span>\r\n          </label>\r\n        </div>\r\n\r\n        {localData.litigation_history && (\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Litigation History Details *\r\n            </label>\r\n            <textarea\r\n              value={localData.litigation_details || ''}\r\n              onChange={(e) => handleLocalChange('litigation_details', e.target.value)}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              placeholder=\"Please provide details of litigation history...\"\r\n            />\r\n            {(validationErrors.litigation_details || errors.litigation_details) && (\r\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n                {validationErrors.litigation_details || errors.litigation_details}\r\n              </p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Compliance Record */}\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Compliance Record *\r\n        </label>\r\n        <textarea\r\n          value={localData.compliance_record || ''}\r\n          onChange={(e) => handleLocalChange('compliance_record', e.target.value)}\r\n          rows={3}\r\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n          placeholder=\"Describe your compliance record and any relevant certifications...\"\r\n        />\r\n        {(validationErrors.compliance_record || errors.compliance_record) && (\r\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n            {validationErrors.compliance_record || errors.compliance_record}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Previous Licenses */}\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Previous Licenses\r\n        </label>\r\n        <textarea\r\n          value={localData.previous_licenses || ''}\r\n          onChange={(e) => handleLocalChange('previous_licenses', e.target.value)}\r\n          rows={3}\r\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n          placeholder=\"List any previous licenses held or applied for...\"\r\n        />\r\n        {(validationErrors.previous_licenses || errors.previous_licenses) && (\r\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n            {validationErrors.previous_licenses || errors.previous_licenses}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Declaration */}\r\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\r\n        <div className=\"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\">\r\n          <label className=\"flex items-start\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={localData.declaration_accepted || false}\r\n              onChange={(e) => handleLocalChange('declaration_accepted', e.target.checked)}\r\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1\"\r\n            />\r\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\r\n              I declare that all information provided is true and accurate to the best of my knowledge. \r\n              I understand that providing false information may result in the rejection of my application \r\n              or revocation of any license granted. *\r\n            </span>\r\n          </label>\r\n          {(validationErrors.declaration_accepted || errors.declaration_accepted) && (\r\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n              {validationErrors.declaration_accepted || errors.declaration_accepted}\r\n            </p>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Save Button */}\r\n      <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n        <button\r\n          onClick={handleSave}\r\n          disabled={isSaving || isLoading}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n        >\r\n          {isSaving || isLoading ? (\r\n            <>\r\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n              Saving...\r\n            </>\r\n          ) : (\r\n            <>\r\n              <i className=\"ri-save-line mr-2\"></i>\r\n              Save Legal History\r\n            </>\r\n          )}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LegalHistory;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAcA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,YAAY,KAAK,EAClB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,sBAAsB;QACtB,GAAG,QAAQ;IACb;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yEAAyE;IACzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;gBAChD;8CAAa,CAAC;wBACZ,MAAM,UAAU;4BAAE,GAAG,IAAI;4BAAE,GAAG,QAAQ;wBAAC;wBACvC,oEAAoE;wBACpE,MAAM,aAAa,OAAO,IAAI,CAAC,UAAU,IAAI;iEAAC,CAAA,MAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;;wBAChF,OAAO,aAAa,UAAU;oBAChC;;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,OAAe;YACpD;+DAAa,CAAC,OAAc,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,CAAC;;YAExD,yCAAyC;YACzC,IAAI,UAAU;gBACZ,SAAS,OAAO;YAClB;YAEA,qDAAqD;YACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;gBAC/C;mEAAoB,CAAC,OAAiC,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,MAAM,EAAE;wBAAG,CAAC;;YACjF;QACF;sDAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;YACb,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCACC,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,UAAU,gBAAgB,IAAI;oCACvC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,OAAO;oCACvE,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,gBAAgB,kBACzB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,UAAU,gBAAgB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACrE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB,mBAC5D,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;0BAQvE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCACC,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,UAAU,kBAAkB,IAAI;oCACzC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;oCACzE,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,kBAAkB,kBAC3B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;0BAQ3E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCACC,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,UAAU,kBAAkB,IAAI;oCACzC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;oCACzE,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,kBAAkB,kBAC3B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;0BAQ3E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCACC,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,UAAU,kBAAkB,IAAI;oCACzC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;oCACzE,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,kBAAkB,kBAC3B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;0BAQ3E,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,6LAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,6LAAC;wBAAE,WAAU;kCACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0BAMrE,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,6LAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,6LAAC;wBAAE,WAAU;kCACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0BAMrE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,UAAU,oBAAoB,IAAI;oCAC3C,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,OAAO;oCAC3E,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;wBAMjE,CAAC,iBAAiB,oBAAoB,IAAI,OAAO,oBAAoB,mBACpE,6LAAC;4BAAE,WAAU;sCACV,iBAAiB,oBAAoB,IAAI,OAAO,oBAAoB;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,6LAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,6LAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;GA7TM;KAAA;uCA+TS", "debugId": null}}, {"offset": {"line": 2443, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationFormDataService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { processApiResponse } from '@/lib/authUtils';\n\nexport interface ApplicationFormData {\n  form_data_id?: string;\n  application_id: string;\n  section_name: string;\n  section_data: Record<string, any>;\n  completed: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport const applicationFormDataService = {\n  // Save form section data\n  async saveFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.post('/application-form-data', {\n        application_id: applicationId,\n        section_name: sectionName,\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} saved successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error saving form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Update existing form section data\n  async updateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to updateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to updateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Updating form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.put(`/application-form-data/${applicationId}/${sectionName}`, {\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} updated successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Get form section data\n  async getFormSection(applicationId: string, sectionName: string): Promise<ApplicationFormData | null> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      console.warn('Invalid applicationId provided to getFormSection:', applicationId);\n      return null;\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      console.warn('Invalid sectionName provided to getFormSection:', sectionName);\n      return null;\n    }\n\n    try {\n      console.log(`Fetching form section ${sectionName} for application ${applicationId}`);\n      const response = await apiClient.get(`/application-form-data/${applicationId}/${sectionName}`);\n      return processApiResponse(response);\n    } catch (error) {\n      if ((error as any)?.response?.status === 404) {\n        console.log(`Form section ${sectionName} not found for application ${applicationId} - this is normal for new applications`);\n        return null; // Section doesn't exist yet\n      }\n      console.error(`Error fetching form section ${sectionName} for application ${applicationId}:`, error);\n      throw error;\n    }\n  },\n\n  // Get all form data for an application\n  async getApplicationFormData(applicationId: string): Promise<Record<string, any>> {\n    try {\n      const response = await apiClient.get(`/application-form-data/${applicationId}`);\n      const processedResponse = processApiResponse(response);\n\n      // Convert array of sections to object\n      const formData: Record<string, any> = {};\n      if (Array.isArray(processedResponse)) {\n        processedResponse.forEach((section: ApplicationFormData) => {\n          formData[section.section_name] = section.section_data;\n        });\n      }\n\n      return formData;\n    } catch (error) {\n      console.error('Error fetching application form data:', error);\n      return {}; // Return empty object if no data found\n    }\n  },\n\n  // Delete form section\n  async deleteFormSection(applicationId: string, sectionName: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/application-form-data/${applicationId}/${sectionName}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error deleting form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Save or update form section (upsert)\n  async saveOrUpdateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before proceeding\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveOrUpdateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveOrUpdateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving/updating form section ${sectionName} for application ${applicationId}`);\n\n      // Try to get existing section first\n      const existingSection = await this.getFormSection(applicationId, sectionName);\n\n      if (existingSection) {\n        // Update existing section\n        console.log(`Updating existing section ${sectionName}`);\n        return await this.updateFormSection(applicationId, sectionName, sectionData);\n      } else {\n        // Create new section\n        console.log(`Creating new section ${sectionName}`);\n        return await this.saveFormSection(applicationId, sectionName, sectionData);\n      }\n    } catch (error) {\n      console.error(`Error saving/updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAYO,MAAM,6BAA6B;IACxC,yBAAyB;IACzB,MAAM,iBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,eAAe;QACvF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,iDAAiD,EAAE,aAAa;QACnF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEpF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,0BAA0B;gBAC9D,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,oBAAoB,CAAC,EAAE,SAAS,IAAI;YAC5E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,MAAM,mBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,qDAAqD,EAAE,eAAe;QACzF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,aAAa;QACrF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEtF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa,EAAE;gBAC7F,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,sBAAsB,CAAC,EAAE,SAAS,IAAI;YAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAe,aAAqB,EAAE,WAAmB;QAC7D,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,QAAQ,IAAI,CAAC,qDAAqD;YAClE,OAAO;QACT;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,QAAQ,IAAI,CAAC,mDAAmD;YAChE,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,eAAe;YACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,AAAC,OAAe,UAAU,WAAW,KAAK;gBAC5C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,2BAA2B,EAAE,cAAc,sCAAsC,CAAC;gBAC1H,OAAO,MAAM,4BAA4B;YAC3C;YACA,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAC9F,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,wBAAuB,aAAqB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe;YAC9E,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,MAAM,WAAgC,CAAC;YACvC,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBACpC,kBAAkB,OAAO,CAAC,CAAC;oBACzB,QAAQ,CAAC,QAAQ,YAAY,CAAC,GAAG,QAAQ,YAAY;gBACvD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO,CAAC,GAAG,uCAAuC;QACpD;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,aAAqB,EAAE,WAAmB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAChG,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,yBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,oCAAoC;QACpC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,2DAA2D,EAAE,eAAe;QAC/F;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,aAAa;QAC3F;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,iBAAiB,EAAE,eAAe;YAE1F,oCAAoC;YACpC,MAAM,kBAAkB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe;YAEjE,IAAI,iBAAiB;gBACnB,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,aAAa;gBACtD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,aAAa;YAClE,OAAO;gBACL,qBAAqB;gBACrB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,aAAa;gBACjD,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,aAAa;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,YAAY,CAAC,CAAC,EAAE;YACpE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationProgressService.ts"], "sourcesContent": ["/**\n * Application Progress Service\n * Manages step completion tracking and progress calculation for license applications\n */\n\nimport { getLicenseTypeStepConfig, calculateProgress } from '@/config/licenseTypeStepConfig';\n\nexport interface StepProgress {\n  stepId: string;\n  stepName: string;\n  completed: boolean;\n  completedAt?: Date;\n  data?: any;\n}\n\nexport interface ApplicationProgress {\n  applicationId: string;\n  licenseTypeId: string;\n  totalSteps: number;\n  completedSteps: number;\n  progressPercentage: number;\n  steps: StepProgress[];\n  lastUpdated: Date;\n}\n\nclass ApplicationProgressService {\n  private progressCache = new Map<string, ApplicationProgress>();\n\n  /**\n   * Initialize progress tracking for a new application\n   */\n  async initializeProgress(applicationId: string, licenseTypeId: string): Promise<ApplicationProgress> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      throw new Error(`Invalid license type: ${licenseTypeId}`);\n    }\n\n    const steps: StepProgress[] = licenseConfig.steps.map(step => ({\n      stepId: step.id,\n      stepName: step.name,\n      completed: false\n    }));\n\n    const progress: ApplicationProgress = {\n      applicationId,\n      licenseTypeId,\n      totalSteps: steps.length,\n      completedSteps: 0,\n      progressPercentage: 0,\n      steps,\n      lastUpdated: new Date()\n    };\n\n    this.progressCache.set(applicationId, progress);\n    await this.saveProgressToStorage(progress);\n    \n    return progress;\n  }\n\n  /**\n   * Mark a step as completed\n   */\n  async markStepCompleted(applicationId: string, stepId: string, data?: any): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (!progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = true;\n      progress.steps[stepIndex].completedAt = new Date();\n      progress.steps[stepIndex].data = data;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Mark a step as incomplete (for editing)\n   */\n  async markStepIncomplete(applicationId: string, stepId: string): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = false;\n      progress.steps[stepIndex].completedAt = undefined;\n      progress.steps[stepIndex].data = undefined;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get current progress for an application\n   */\n  async getProgress(applicationId: string): Promise<ApplicationProgress | null> {\n    // Check cache first\n    if (this.progressCache.has(applicationId)) {\n      return this.progressCache.get(applicationId)!;\n    }\n\n    // Load from storage\n    const progress = await this.loadProgressFromStorage(applicationId);\n    if (progress) {\n      this.progressCache.set(applicationId, progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get completed step IDs for an application\n   */\n  async getCompletedStepIds(applicationId: string): Promise<string[]> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return [];\n\n    return progress.steps\n      .filter(step => step.completed)\n      .map(step => step.stepId);\n  }\n\n  /**\n   * Check if a specific step is completed\n   */\n  async isStepCompleted(applicationId: string, stepId: string): Promise<boolean> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return false;\n\n    const step = progress.steps.find(s => s.stepId === stepId);\n    return step?.completed || false;\n  }\n\n  /**\n   * Get next incomplete step\n   */\n  async getNextIncompleteStep(applicationId: string): Promise<StepProgress | null> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return null;\n\n    return progress.steps.find(step => !step.completed) || null;\n  }\n\n  /**\n   * Calculate overall application completion status\n   */\n  async getApplicationStatus(applicationId: string): Promise<'not_started' | 'in_progress' | 'completed'> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return 'not_started';\n\n    if (progress.completedSteps === 0) return 'not_started';\n    if (progress.completedSteps === progress.totalSteps) return 'completed';\n    return 'in_progress';\n  }\n\n  /**\n   * Save progress to localStorage (in a real app, this would be an API call)\n   */\n  private async saveProgressToStorage(progress: ApplicationProgress): Promise<void> {\n    try {\n      const key = `application_progress_${progress.applicationId}`;\n      localStorage.setItem(key, JSON.stringify({\n        ...progress,\n        lastUpdated: progress.lastUpdated.toISOString()\n      }));\n    } catch (error) {\n      console.error('Error saving progress to storage:', error);\n    }\n  }\n\n  /**\n   * Load progress from localStorage (in a real app, this would be an API call)\n   */\n  private async loadProgressFromStorage(applicationId: string): Promise<ApplicationProgress | null> {\n    try {\n      const key = `application_progress_${applicationId}`;\n      const stored = localStorage.getItem(key);\n      \n      if (!stored) return null;\n\n      const parsed = JSON.parse(stored);\n      return {\n        ...parsed,\n        lastUpdated: new Date(parsed.lastUpdated),\n        steps: parsed.steps.map((step: any) => ({\n          ...step,\n          completedAt: step.completedAt ? new Date(step.completedAt) : undefined\n        }))\n      };\n    } catch (error) {\n      console.error('Error loading progress from storage:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Clear progress cache (useful for testing or when switching applications)\n   */\n  clearCache(): void {\n    this.progressCache.clear();\n  }\n\n  /**\n   * Delete progress for an application\n   */\n  async deleteProgress(applicationId: string): Promise<void> {\n    this.progressCache.delete(applicationId);\n    \n    try {\n      const key = `application_progress_${applicationId}`;\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error deleting progress from storage:', error);\n    }\n  }\n}\n\n// Export singleton instance\nexport const applicationProgressService = new ApplicationProgressService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAoBA,MAAM;IACI,gBAAgB,IAAI,MAAmC;IAE/D;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,aAAqB,EAAgC;QACnG,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,eAAe;QAC1D;QAEA,MAAM,QAAwB,cAAc,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7D,QAAQ,KAAK,EAAE;gBACf,UAAU,KAAK,IAAI;gBACnB,WAAW;YACb,CAAC;QAED,MAAM,WAAgC;YACpC;YACA;YACA,YAAY,MAAM,MAAM;YACxB,gBAAgB;YAChB,oBAAoB;YACpB;YACA,aAAa,IAAI;QACnB;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,kBAAkB,aAAqB,EAAE,MAAc,EAAE,IAAU,EAAgC;QACvG,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;YAC5C,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,MAAc,EAAgC;QAC5F,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACvC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,YAAY,aAAqB,EAAuC;QAC5E,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB;YACzC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAChC;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,IAAI,CAAC,uBAAuB,CAAC;QACpD,IAAI,UAAU;YACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACxC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBAAoB,aAAqB,EAAqB;QAClE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,OAAO,SAAS,KAAK,CAClB,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAC7B,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC5B;IAEA;;GAEC,GACD,MAAM,gBAAgB,aAAqB,EAAE,MAAc,EAAoB;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACnD,OAAO,MAAM,aAAa;IAC5B;IAEA;;GAEC,GACD,MAAM,sBAAsB,aAAqB,EAAgC;QAC/E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,KAAK;IACzD;IAEA;;GAEC,GACD,MAAM,qBAAqB,aAAqB,EAAwD;QACtG,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,IAAI,SAAS,cAAc,KAAK,GAAG,OAAO;QAC1C,IAAI,SAAS,cAAc,KAAK,SAAS,UAAU,EAAE,OAAO;QAC5D,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,sBAAsB,QAA6B,EAAiB;QAChF,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,SAAS,aAAa,EAAE;YAC5D,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBACvC,GAAG,QAAQ;gBACX,aAAa,SAAS,WAAW,CAAC,WAAW;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD,MAAc,wBAAwB,aAAqB,EAAuC;QAChG,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO;gBACL,GAAG,MAAM;gBACT,aAAa,IAAI,KAAK,OAAO,WAAW;gBACxC,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBACtC,GAAG,IAAI;wBACP,aAAa,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI;oBAC/D,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA;;GAEC,GACD,MAAM,eAAe,aAAqB,EAAiB;QACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAE1B,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;AACF;AAGO,MAAM,6BAA6B,IAAI", "debugId": null}}, {"offset": {"line": 2779, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/stepValidationService.ts"], "sourcesContent": ["/**\n * Step Validation Service\n * Handles step completion validation and navigation rules for license applications\n */\n\nimport { getLicenseTypeStepConfig, getStepIndex } from '@/config/licenseTypeStepConfig';\nimport { applicationProgressService } from './applicationProgressService';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n}\n\nexport interface NavigationValidation {\n  canNavigateToStep: boolean;\n  reason?: string;\n  requiredSteps: string[];\n}\n\nclass StepValidationService {\n  /**\n   * Validate if user can navigate to a specific step\n   */\n  async validateStepNavigation(\n    applicationId: string,\n    licenseTypeId: string,\n    targetStepId: string\n  ): Promise<NavigationValidation> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid license type',\n        requiredSteps: []\n      };\n    }\n\n    const targetStepIndex = getStepIndex(licenseTypeId, targetStepId);\n    if (targetStepIndex === -1) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid step',\n        requiredSteps: []\n      };\n    }\n\n    // Get completed steps\n    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n    \n    // Check if all required previous steps are completed\n    const requiredPreviousSteps = licenseConfig.steps\n      .slice(0, targetStepIndex)\n      .filter(step => step.required)\n      .map(step => step.id);\n\n    const missingRequiredSteps = requiredPreviousSteps.filter(\n      stepId => !completedStepIds.includes(stepId)\n    );\n\n    if (missingRequiredSteps.length > 0) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Required previous steps must be completed first',\n        requiredSteps: missingRequiredSteps\n      };\n    }\n\n    return {\n      canNavigateToStep: true,\n      requiredSteps: []\n    };\n  }\n\n  /**\n   * Validate if user can proceed to next step\n   */\n  async validateNextStepNavigation(\n    applicationId: string,\n    licenseTypeId: string,\n    currentStepId: string\n  ): Promise<NavigationValidation> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid license type',\n        requiredSteps: []\n      };\n    }\n\n    const currentStepIndex = getStepIndex(licenseTypeId, currentStepId);\n    if (currentStepIndex === -1) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid current step',\n        requiredSteps: []\n      };\n    }\n\n    // Check if current step is completed (if required)\n    const currentStep = licenseConfig.steps[currentStepIndex];\n    if (currentStep.required) {\n      const isCompleted = await applicationProgressService.isStepCompleted(applicationId, currentStepId);\n      if (!isCompleted) {\n        return {\n          canNavigateToStep: false,\n          reason: 'Current step must be completed before proceeding',\n          requiredSteps: [currentStepId]\n        };\n      }\n    }\n\n    // Check if there is a next step\n    if (currentStepIndex >= licenseConfig.steps.length - 1) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Already at the last step',\n        requiredSteps: []\n      };\n    }\n\n    return {\n      canNavigateToStep: true,\n      requiredSteps: []\n    };\n  }\n\n  /**\n   * Validate if user can go back to previous step\n   */\n  async validatePreviousStepNavigation(\n    applicationId: string,\n    licenseTypeId: string,\n    currentStepId: string\n  ): Promise<NavigationValidation> {\n    const currentStepIndex = getStepIndex(licenseTypeId, currentStepId);\n    \n    if (currentStepIndex <= 0) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Already at the first step',\n        requiredSteps: []\n      };\n    }\n\n    return {\n      canNavigateToStep: true,\n      requiredSteps: []\n    };\n  }\n\n  /**\n   * Get the next available step that the user can navigate to\n   */\n  async getNextAvailableStep(\n    applicationId: string,\n    licenseTypeId: string\n  ): Promise<string | null> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) return null;\n\n    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n    \n    // Find the first incomplete required step\n    for (const step of licenseConfig.steps) {\n      if (step.required && !completedStepIds.includes(step.id)) {\n        return step.id;\n      }\n    }\n\n    // If all required steps are completed, find the first incomplete optional step\n    for (const step of licenseConfig.steps) {\n      if (!step.required && !completedStepIds.includes(step.id)) {\n        return step.id;\n      }\n    }\n\n    // All steps completed\n    return null;\n  }\n\n  /**\n   * Validate application completion\n   */\n  async validateApplicationCompletion(\n    applicationId: string,\n    licenseTypeId: string\n  ): Promise<ValidationResult> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      return {\n        isValid: false,\n        errors: ['Invalid license type'],\n        warnings: []\n      };\n    }\n\n    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n    const requiredSteps = licenseConfig.steps.filter(step => step.required);\n    const missingRequiredSteps = requiredSteps.filter(step => !completedStepIds.includes(step.id));\n\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    if (missingRequiredSteps.length > 0) {\n      errors.push(`Missing required steps: ${missingRequiredSteps.map(s => s.name).join(', ')}`);\n    }\n\n    const optionalSteps = licenseConfig.steps.filter(step => !step.required);\n    const missingOptionalSteps = optionalSteps.filter(step => !completedStepIds.includes(step.id));\n\n    if (missingOptionalSteps.length > 0) {\n      warnings.push(`Optional steps not completed: ${missingOptionalSteps.map(s => s.name).join(', ')}`);\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * Get step completion requirements\n   */\n  async getStepRequirements(\n    licenseTypeId: string,\n    stepId: string\n  ): Promise<{\n    isRequired: boolean;\n    dependencies: string[];\n    description: string;\n    estimatedTime: string;\n  } | null> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) return null;\n\n    const step = licenseConfig.steps.find(s => s.id === stepId);\n    if (!step) return null;\n\n    const stepIndex = getStepIndex(licenseTypeId, stepId);\n    const dependencies = licenseConfig.steps\n      .slice(0, stepIndex)\n      .filter(s => s.required)\n      .map(s => s.id);\n\n    return {\n      isRequired: step.required,\n      dependencies,\n      description: step.description,\n      estimatedTime: step.estimatedTime\n    };\n  }\n\n  /**\n   * Check if application is ready for submission\n   */\n  async isReadyForSubmission(\n    applicationId: string,\n    licenseTypeId: string\n  ): Promise<boolean> {\n    const validation = await this.validateApplicationCompletion(applicationId, licenseTypeId);\n    return validation.isValid;\n  }\n}\n\n// Export singleton instance\nexport const stepValidationService = new StepValidationService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAcA,MAAM;IACJ;;GAEC,GACD,MAAM,uBACJ,aAAqB,EACrB,aAAqB,EACrB,YAAoB,EACW;QAC/B,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,MAAM,kBAAkB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QACpD,IAAI,oBAAoB,CAAC,GAAG;YAC1B,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,sBAAsB;QACtB,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;QAE9E,qDAAqD;QACrD,MAAM,wBAAwB,cAAc,KAAK,CAC9C,KAAK,CAAC,GAAG,iBACT,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAC5B,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;QAEtB,MAAM,uBAAuB,sBAAsB,MAAM,CACvD,CAAA,SAAU,CAAC,iBAAiB,QAAQ,CAAC;QAGvC,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,2BACJ,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACU;QAC/B,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,MAAM,mBAAmB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QACrD,IAAI,qBAAqB,CAAC,GAAG;YAC3B,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,mDAAmD;QACnD,MAAM,cAAc,cAAc,KAAK,CAAC,iBAAiB;QACzD,IAAI,YAAY,QAAQ,EAAE;YACxB,MAAM,cAAc,MAAM,gJAAA,CAAA,6BAA0B,CAAC,eAAe,CAAC,eAAe;YACpF,IAAI,CAAC,aAAa;gBAChB,OAAO;oBACL,mBAAmB;oBACnB,QAAQ;oBACR,eAAe;wBAAC;qBAAc;gBAChC;YACF;QACF;QAEA,gCAAgC;QAChC,IAAI,oBAAoB,cAAc,KAAK,CAAC,MAAM,GAAG,GAAG;YACtD,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,+BACJ,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACU;QAC/B,MAAM,mBAAmB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QAErD,IAAI,oBAAoB,GAAG;YACzB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,aAAqB,EACrB,aAAqB,EACG;QACxB,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;QAE9E,0CAA0C;QAC1C,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAE;YACtC,IAAI,KAAK,QAAQ,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE,GAAG;gBACxD,OAAO,KAAK,EAAE;YAChB;QACF;QAEA,+EAA+E;QAC/E,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAE;YACtC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE,GAAG;gBACzD,OAAO,KAAK,EAAE;YAChB;QACF;QAEA,sBAAsB;QACtB,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,8BACJ,aAAqB,EACrB,aAAqB,EACM;QAC3B,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC;iBAAuB;gBAChC,UAAU,EAAE;YACd;QACF;QAEA,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;QAC9E,MAAM,gBAAgB,cAAc,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;QACtE,MAAM,uBAAuB,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE;QAE5F,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,qBAAqB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;QAC3F;QAEA,MAAM,gBAAgB,cAAc,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;QACvE,MAAM,uBAAuB,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE;QAE5F,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,SAAS,IAAI,CAAC,CAAC,8BAA8B,EAAE,qBAAqB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;QACnG;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,aAAqB,EACrB,MAAc,EAMN;QACR,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,OAAO,cAAc,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,YAAY,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QAC9C,MAAM,eAAe,cAAc,KAAK,CACrC,KAAK,CAAC,GAAG,WACT,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EACtB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAEhB,OAAO;YACL,YAAY,KAAK,QAAQ;YACzB;YACA,aAAa,KAAK,WAAW;YAC7B,eAAe,KAAK,aAAa;QACnC;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,aAAqB,EACrB,aAAqB,EACH;QAClB,MAAM,aAAa,MAAM,IAAI,CAAC,6BAA6B,CAAC,eAAe;QAC3E,OAAO,WAAW,OAAO;IAC3B;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 2974, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/legal-history/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter, useParams, useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLicenseData } from '@/hooks/useLicenseData';\r\nimport LegalHistory from '@/components/customer/application/steps/LegalHistory';\r\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\r\nimport {\r\n  getLicenseTypeStepConfig,\r\n  getStepByRoute,\r\n  getStepIndex,\r\n  getTotalSteps,\r\n  getNextStep,\r\n  getPreviousStep,\r\n  calculateProgress\r\n} from '@/config/licenseTypeStepConfig';\r\nimport { applicationProgressService } from '@/services/applicationProgressService';\r\nimport { stepValidationService } from '@/services/stepValidationService';\r\n\r\nconst LegalHistoryPage: React.FC = () => {\r\n  const router = useRouter();\r\n  const params = useParams();\r\n  const searchParams = useSearchParams();\r\n  const { isAuthenticated, loading: authLoading } = useAuth();\r\n\r\n  // Initialize license data to populate UUID-to-code mapping\r\n  const { loading: licenseDataLoading, categories } = useLicenseData();\r\n\r\n  const licenseCategoryId = params.categoryId as string;\r\n  const stepName = 'legal-history';\r\n\r\n  // Get application ID from URL params - required for this step\r\n  const applicationId = searchParams.get('app');\r\n  const isEditMode = applicationId !== null;\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [completedSteps, setCompletedSteps] = useState<string[]>([]);\r\n  const [progressPercentage, setProgressPercentage] = useState(0);\r\n  const [canNavigateNext, setCanNavigateNext] = useState(false);\r\n  const [canNavigatePrevious, setCanNavigatePrevious] = useState(false);\r\n  const [navigationError, setNavigationError] = useState<string | null>(null);\r\n  const [licenseTypeId, setLicenseTypeId] = useState<string>('');\r\n\r\n  // Form data state\r\n  const [formData, setFormData] = useState<any>({});\r\n  const [formErrors, setFormErrors] = useState<Record<string, string>>({});\r\n\r\n  // Get step configuration - we'll need to fetch the license type from category first\r\n  const [licenseConfig, setLicenseConfig] = useState<any>(null);\r\n  const [currentStep, setCurrentStep] = useState<any>(null);\r\n  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);\r\n  const [totalSteps, setTotalSteps] = useState<number>(0);\r\n  const [nextStep, setNextStep] = useState<any>(null);\r\n  const [previousStep, setPreviousStep] = useState<any>(null);\r\n\r\n  // Redirect if not authenticated\r\n  useEffect(() => {\r\n    if (!authLoading && !isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n      return;\r\n    }\r\n  }, [isAuthenticated, authLoading, router]);\r\n\r\n  // Fetch license category and determine license type\r\n  useEffect(() => {\r\n    const fetchLicenseCategory = async () => {\r\n      try {\r\n        // Use the license data hook to get category info\r\n        const category = categories.find((cat: any) => cat.id === licenseCategoryId);\r\n        \r\n        if (category && category.license_type_id) {\r\n          setLicenseTypeId(category.license_type_id);\r\n        } else {\r\n          // Fallback to API call if not found in hook data\r\n          const response = await fetch(`/api/license-categories/${licenseCategoryId}`);\r\n          if (!response.ok) {\r\n            throw new Error('Failed to fetch license category');\r\n          }\r\n          const categoryData = await response.json();\r\n          \r\n          if (categoryData.license_type_id) {\r\n            setLicenseTypeId(categoryData.license_type_id);\r\n          } else {\r\n            throw new Error('License category does not have a license type ID');\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching license category:', error);\r\n        setError('Failed to load license category information');\r\n      }\r\n    };\r\n\r\n    if (licenseCategoryId && !licenseDataLoading) {\r\n      fetchLicenseCategory();\r\n    }\r\n  }, [licenseCategoryId, licenseDataLoading, categories]);\r\n\r\n  // Load step configuration once we have license type ID\r\n  useEffect(() => {\r\n    if (licenseTypeId) {\r\n      const config = getLicenseTypeStepConfig(licenseTypeId);\r\n      setLicenseConfig(config);\r\n      setCurrentStep(getStepByRoute(licenseTypeId, stepName));\r\n      setCurrentStepIndex(getStepIndex(licenseTypeId, stepName));\r\n      setTotalSteps(getTotalSteps(licenseTypeId));\r\n      setNextStep(getNextStep(licenseTypeId, stepName));\r\n      setPreviousStep(getPreviousStep(licenseTypeId, stepName));\r\n    }\r\n  }, [licenseTypeId, stepName]);\r\n\r\n  // Load progress and validate step configuration\r\n  useEffect(() => {\r\n    const initializeStepPage = async () => {\r\n      if (licenseDataLoading || !licenseTypeId) return;\r\n\r\n      if (!licenseConfig) {\r\n        setError(`Invalid license type: ${licenseTypeId}. Please check the license type configuration.`);\r\n        return;\r\n      }\r\n\r\n      if (!currentStep) {\r\n        setError(`Invalid step: ${stepName} for license type ${licenseTypeId}`);\r\n        return;\r\n      }\r\n\r\n      // Redirect to applicant-info if no application ID\r\n      if (!applicationId) {\r\n        router.replace(`/customer/applications/apply/${licenseCategoryId}/applicant-info`);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        let progress = await applicationProgressService.getProgress(applicationId);\r\n        if (!progress) {\r\n          progress = await applicationProgressService.initializeProgress(applicationId, licenseTypeId);\r\n        }\r\n\r\n        if (progress) {\r\n          const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\r\n          setCompletedSteps(completedStepIds);\r\n          setProgressPercentage(progress.progressPercentage);\r\n\r\n          const nextValidation = await stepValidationService.validateNextStepNavigation(\r\n            applicationId, licenseTypeId, currentStep.id\r\n          );\r\n          setCanNavigateNext(nextValidation.canNavigateToStep);\r\n\r\n          const prevValidation = await stepValidationService.validatePreviousStepNavigation(\r\n            applicationId, licenseTypeId, currentStep.id\r\n          );\r\n          setCanNavigatePrevious(prevValidation.canNavigateToStep);\r\n\r\n          const currentStepValidation = await stepValidationService.validateStepNavigation(\r\n            applicationId, licenseTypeId, currentStep.id\r\n          );\r\n\r\n          if (!currentStepValidation.canNavigateToStep) {\r\n            setNavigationError(currentStepValidation.reason || 'Cannot access this step');\r\n            const nextAvailableStep = await stepValidationService.getNextAvailableStep(applicationId, licenseTypeId);\r\n            if (nextAvailableStep) {\r\n              router.replace(`/customer/applications/apply/${licenseCategoryId}/${nextAvailableStep}?app=${applicationId}`);\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading application progress:', error);\r\n        setProgressPercentage(0);\r\n        setCompletedSteps([]);\r\n        setCanNavigateNext(false);\r\n      }\r\n\r\n      // Load form data for this step\r\n      await loadFormData();\r\n\r\n      setIsLoading(false);\r\n    };\r\n\r\n    initializeStepPage();\r\n  }, [licenseConfig, currentStep, stepName, licenseTypeId, licenseCategoryId, applicationId, router, licenseDataLoading]);\r\n\r\n  // Load form data for this step\r\n  const loadFormData = async () => {\r\n    if (applicationId) {\r\n      try {\r\n        const data = await applicationFormDataService.getFormSection(applicationId, 'legalHistory');\r\n        if (data) {\r\n          setFormData(data);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading form data:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle form data changes\r\n  const handleFormDataChange = (field: string, value: any) => {\r\n    setFormData((prev: any) => ({ ...prev, [field]: value }));\r\n    // Clear any errors for this field\r\n    if (formErrors[field]) {\r\n      setFormErrors((prev) => ({ ...prev, [field]: '' }));\r\n    }\r\n  };\r\n\r\n  // Handle form save\r\n  const handleFormSave = async (data: any) => {\r\n    try {\r\n      if (applicationId) {\r\n        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'legalHistory', data);\r\n        setFormData(data);\r\n\r\n        // Mark step as completed\r\n        await handleStepComplete(currentStep?.id || stepName, data);\r\n\r\n        return applicationId;\r\n      }\r\n      throw new Error('No application ID available');\r\n    } catch (error) {\r\n      console.error('Error saving form data:', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleStepComplete = async (stepId: string, data?: any) => {\r\n    try {\r\n      if (applicationId) {\r\n        const progress = await applicationProgressService.markStepCompleted(applicationId, stepId, data);\r\n        setCompletedSteps(progress.steps.filter(s => s.completed).map(s => s.stepId));\r\n        setProgressPercentage(progress.progressPercentage);\r\n\r\n        if (currentStep) {\r\n          const nextValidation = await stepValidationService.validateNextStepNavigation(\r\n            applicationId, licenseTypeId, currentStep.id\r\n          );\r\n          setCanNavigateNext(nextValidation.canNavigateToStep);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error marking step as completed:', error);\r\n    }\r\n  };\r\n\r\n  const handleNavigate = (direction: 'next' | 'previous') => {\r\n    if (direction === 'next' && nextStep && canNavigateNext) {\r\n      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';\r\n      router.push(`/customer/applications/apply/${licenseCategoryId}/${nextStep.route}${appParam}`);\r\n    } else if (direction === 'previous' && previousStep && canNavigatePrevious) {\r\n      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';\r\n      router.push(`/customer/applications/apply/${licenseCategoryId}/${previousStep.route}${appParam}`);\r\n    }\r\n  };\r\n\r\n  if (authLoading || isLoading || !licenseTypeId) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application step...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"max-w-4xl mx-auto p-6\">\r\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4\"></i>\r\n              <div>\r\n                <h3 className=\"text-lg font-medium text-red-800 dark:text-red-200\">Error Loading Step</h3>\r\n                <p className=\"text-red-700 dark:text-red-300 mt-1\">{error}</p>\r\n                <button\r\n                  onClick={() => router.back()}\r\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40\"\r\n                >\r\n                  <i className=\"ri-arrow-left-line mr-2\"></i>\r\n                  Go Back\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-4xl mx-auto p-6\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\r\n            {licenseConfig?.name} License Application\r\n          </h1>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">\r\n            Step {currentStepIndex + 1} of {totalSteps}: {currentStep?.name}\r\n          </p>\r\n          <div className=\"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n              <i className=\"ri-edit-line mr-1\"></i>\r\n              Editing application: {applicationId}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Progress Bar */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between mb-2\">\r\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n              Step {currentStepIndex + 1} of {totalSteps}\r\n            </span>\r\n            <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              {progressPercentage}% Complete\r\n            </span>\r\n          </div>\r\n          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n            <div\r\n              className=\"bg-primary h-2 rounded-full transition-all duration-300\"\r\n              style={{ width: `${progressPercentage}%` }}\r\n            ></div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Step Navigation Breadcrumb */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {licenseConfig?.steps.map((step: any, index: number) => {\r\n              const isCompleted = completedSteps.includes(step.id);\r\n              const isCurrent = index === currentStepIndex;\r\n              \r\n              return (\r\n                <div\r\n                  key={step.id}\r\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                    isCurrent\r\n                      ? 'bg-primary text-white'\r\n                      : isCompleted\r\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\r\n                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'\r\n                  }`}\r\n                >\r\n                  {isCompleted && !isCurrent && (\r\n                    <i className=\"ri-check-line mr-1\"></i>\r\n                  )}\r\n                  {index + 1}. {step.name}\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation Error */}\r\n        {navigationError && (\r\n          <div className=\"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3\"></i>\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">Navigation Restriction</h3>\r\n                <p className=\"text-yellow-700 dark:text-yellow-300 text-sm mt-1\">{navigationError}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Step Content - Legal History */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6\">\r\n          <LegalHistory\r\n            formData={formData}\r\n            onChange={handleFormDataChange}\r\n            onSave={handleFormSave}\r\n            errors={formErrors}\r\n            applicationId={applicationId}\r\n            isLoading={isLoading}\r\n          />\r\n        </div>\r\n\r\n        {/* Navigation Buttons */}\r\n        <div className=\"flex justify-between\">\r\n          <button\r\n            onClick={() => handleNavigate('previous')}\r\n            disabled={!canNavigatePrevious || !previousStep}\r\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            <i className=\"ri-arrow-left-line mr-2\"></i>\r\n            Previous\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => handleNavigate('next')}\r\n            disabled={!canNavigateNext || !nextStep}\r\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Next\r\n            <i className=\"ri-arrow-right-line ml-2\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default LegalHistoryPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;;;AAnBA;;;;;;;;;;;AAqBA,MAAM,mBAA6B;;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExD,2DAA2D;IAC3D,MAAM,EAAE,SAAS,kBAAkB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjE,MAAM,oBAAoB,OAAO,UAAU;IAC3C,MAAM,WAAW;IAEjB,8DAA8D;IAC9D,MAAM,gBAAgB,aAAa,GAAG,CAAC;IACvC,MAAM,aAAa,kBAAkB;IAErC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,oFAAoF;IACpF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEtD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;qCAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;mEAAuB;oBAC3B,IAAI;wBACF,iDAAiD;wBACjD,MAAM,WAAW,WAAW,IAAI;wFAAC,CAAC,MAAa,IAAI,EAAE,KAAK;;wBAE1D,IAAI,YAAY,SAAS,eAAe,EAAE;4BACxC,iBAAiB,SAAS,eAAe;wBAC3C,OAAO;4BACL,iDAAiD;4BACjD,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,mBAAmB;4BAC3E,IAAI,CAAC,SAAS,EAAE,EAAE;gCAChB,MAAM,IAAI,MAAM;4BAClB;4BACA,MAAM,eAAe,MAAM,SAAS,IAAI;4BAExC,IAAI,aAAa,eAAe,EAAE;gCAChC,iBAAiB,aAAa,eAAe;4BAC/C,OAAO;gCACL,MAAM,IAAI,MAAM;4BAClB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,SAAS;oBACX;gBACF;;YAEA,IAAI,qBAAqB,CAAC,oBAAoB;gBAC5C;YACF;QACF;qCAAG;QAAC;QAAmB;QAAoB;KAAW;IAEtD,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,eAAe;gBACjB,MAAM,SAAS,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;gBACxC,iBAAiB;gBACjB,eAAe,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;gBAC7C,oBAAoB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;gBAChD,cAAc,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;gBAC5B,YAAY,CAAA,GAAA,yIAAA,CAAA,cAAW,AAAD,EAAE,eAAe;gBACvC,gBAAgB,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD,EAAE,eAAe;YACjD;QACF;qCAAG;QAAC;QAAe;KAAS;IAE5B,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB;oBACzB,IAAI,sBAAsB,CAAC,eAAe;oBAE1C,IAAI,CAAC,eAAe;wBAClB,SAAS,CAAC,sBAAsB,EAAE,cAAc,8CAA8C,CAAC;wBAC/F;oBACF;oBAEA,IAAI,CAAC,aAAa;wBAChB,SAAS,CAAC,cAAc,EAAE,SAAS,kBAAkB,EAAE,eAAe;wBACtE;oBACF;oBAEA,kDAAkD;oBAClD,IAAI,CAAC,eAAe;wBAClB,OAAO,OAAO,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,eAAe,CAAC;wBACjF;oBACF;oBAEA,IAAI;wBACF,IAAI,WAAW,MAAM,gJAAA,CAAA,6BAA0B,CAAC,WAAW,CAAC;wBAC5D,IAAI,CAAC,UAAU;4BACb,WAAW,MAAM,gJAAA,CAAA,6BAA0B,CAAC,kBAAkB,CAAC,eAAe;wBAChF;wBAEA,IAAI,UAAU;4BACZ,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;4BAC9E,kBAAkB;4BAClB,sBAAsB,SAAS,kBAAkB;4BAEjD,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,0BAA0B,CAC3E,eAAe,eAAe,YAAY,EAAE;4BAE9C,mBAAmB,eAAe,iBAAiB;4BAEnD,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,8BAA8B,CAC/E,eAAe,eAAe,YAAY,EAAE;4BAE9C,uBAAuB,eAAe,iBAAiB;4BAEvD,MAAM,wBAAwB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,sBAAsB,CAC9E,eAAe,eAAe,YAAY,EAAE;4BAG9C,IAAI,CAAC,sBAAsB,iBAAiB,EAAE;gCAC5C,mBAAmB,sBAAsB,MAAM,IAAI;gCACnD,MAAM,oBAAoB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,oBAAoB,CAAC,eAAe;gCAC1F,IAAI,mBAAmB;oCACrB,OAAO,OAAO,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,EAAE,kBAAkB,KAAK,EAAE,eAAe;oCAC5G;gCACF;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uCAAuC;wBACrD,sBAAsB;wBACtB,kBAAkB,EAAE;wBACpB,mBAAmB;oBACrB;oBAEA,+BAA+B;oBAC/B,MAAM;oBAEN,aAAa;gBACf;;YAEA;QACF;qCAAG;QAAC;QAAe;QAAa;QAAU;QAAe;QAAmB;QAAe;QAAQ;KAAmB;IAEtH,+BAA+B;IAC/B,MAAM,eAAe;QACnB,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,OAAO,MAAM,gJAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,eAAe;gBAC5E,IAAI,MAAM;oBACR,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,CAAC,OAAe;QAC3C,YAAY,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACvD,kCAAkC;QAClC,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,cAAc,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACnD;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,IAAI,eAAe;gBACjB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,uBAAuB,CAAC,eAAe,gBAAgB;gBACxF,YAAY;gBAEZ,yBAAyB;gBACzB,MAAM,mBAAmB,aAAa,MAAM,UAAU;gBAEtD,OAAO;YACT;YACA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,IAAI,eAAe;gBACjB,MAAM,WAAW,MAAM,gJAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,eAAe,QAAQ;gBAC3F,kBAAkB,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC3E,sBAAsB,SAAS,kBAAkB;gBAEjD,IAAI,aAAa;oBACf,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,0BAA0B,CAC3E,eAAe,eAAe,YAAY,EAAE;oBAE9C,mBAAmB,eAAe,iBAAiB;gBACrD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,cAAc,UAAU,YAAY,iBAAiB;YACvD,MAAM,WAAW,kBAAkB,QAAQ,CAAC,KAAK,EAAE,eAAe,GAAG;YACrE,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,EAAE,SAAS,KAAK,GAAG,UAAU;QAC9F,OAAO,IAAI,cAAc,cAAc,gBAAgB,qBAAqB;YAC1E,MAAM,WAAW,kBAAkB,QAAQ,CAAC,KAAK,EAAE,eAAe,GAAG;YACrE,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,EAAE,aAAa,KAAK,GAAG,UAAU;QAClG;IACF;IAEA,IAAI,eAAe,aAAa,CAAC,eAAe;QAC9C,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;kDACnE,6LAAC;wCAAE,WAAU;kDAAuC;;;;;;kDACpD,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,6LAAC;gDAAE,WAAU;;;;;;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS3D;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCACX,eAAe;gCAAK;;;;;;;sCAEvB,6LAAC;4BAAE,WAAU;;gCAAmC;gCACxC,mBAAmB;gCAAE;gCAAK;gCAAW;gCAAG,aAAa;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;wCAAE,WAAU;;;;;;oCAAwB;oCACf;;;;;;;;;;;;;;;;;;8BAM5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;wCAAuD;wCAC/D,mBAAmB;wCAAE;wCAAK;;;;;;;8CAElC,6LAAC;oCAAK,WAAU;;wCACb;wCAAmB;;;;;;;;;;;;;sCAGxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;8BAM/C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,eAAe,MAAM,IAAI,CAAC,MAAW;4BACpC,MAAM,cAAc,eAAe,QAAQ,CAAC,KAAK,EAAE;4BACnD,MAAM,YAAY,UAAU;4BAE5B,qBACE,6LAAC;gCAEC,WAAW,CAAC,2CAA2C,EACrD,YACI,0BACA,cACA,sEACA,iEACJ;;oCAED,eAAe,CAAC,2BACf,6LAAC;wCAAE,WAAU;;;;;;oCAEd,QAAQ;oCAAE;oCAAG,KAAK,IAAI;;+BAZlB,KAAK,EAAE;;;;;wBAelB;;;;;;;;;;;gBAKH,iCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;8BAO1E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yKAAA,CAAA,UAAY;wBACX,UAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,QAAQ;wBACR,eAAe;wBACf,WAAW;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,UAAU,CAAC,uBAAuB,CAAC;4BACnC,WAAU;;8CAEV,6LAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;sCAI7C,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,UAAU,CAAC,mBAAmB,CAAC;4BAC/B,WAAU;;gCACX;8CAEC,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;GAjYM;;QACW,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACc,kIAAA,CAAA,UAAO;QAGL,iIAAA,CAAA,iBAAc;;;KAP9D;uCAmYS", "debugId": null}}]}