'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { validateSection } from '@/utils/formValidation';
import { applicationFormDataService } from '@/services/applicationFormDataService';
import { applicationProgressService } from '@/services/applicationProgressService';
import { IndependentStepProps } from '../types';

interface BusinessInfoProps extends IndependentStepProps {}

const BusinessInfo: React.FC<BusinessInfoProps> = ({
  applicationId,
  licenseTypeId,
  licenseCategoryId,
  isEditMode = false,
  onStepComplete,
  onStepError,
  onNavigate
}) => {
  const [localData, setLocalData] = useState({
    business_model: '',
    operational_structure: '',
    target_market: '',
    competitive_advantage: '',
    facilities_description: '',
    equipment_description: '',
    operational_areas: '',
    service_delivery_model: '',
    quality_assurance: '',
    customer_support: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load existing data when component mounts (for edit mode)
  useEffect(() => {
    const loadExistingData = async () => {
      // Add more robust validation for applicationId
      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {
        console.log('Loading existing business info data for application:', applicationId);
        setIsLoading(true);
        try {
          const existingData = await applicationFormDataService.getFormSection(applicationId, 'businessInfo');
          if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {
            console.log('Loaded existing business info data:', existingData.section_data);
            setLocalData(prev => ({ ...prev, ...existingData.section_data }));
          } else {
            console.log('No existing business info data found for application:', applicationId);
          }
        } catch (error) {
          console.error('Error loading existing business info data:', error);
          // Don't call onStepError as it would hide the form
          // Just log the error and continue with empty form
          console.log('Continuing with empty form due to load error');
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('Skipping business info data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });
      }
    };

    loadExistingData();
  }, [applicationId, isEditMode]);

  // Handle local data changes
  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }

    // Clear save error when user starts making changes
    if (validationErrors.save) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));
    }
  }, [validationErrors]);

  // Validate form data
  const validateForm = () => {
    const validation = validateSection(localData, 'businessInfo');
    const errors = validation.errors || {};
    setValidationErrors(errors);
    return validation.isValid;
  };

  // Save data to backend
  const saveData = async () => {
    const validation = validateSection(localData, 'businessInfo');
    const errors = validation.errors || {};
    setValidationErrors(errors);

    if (!validation.isValid) {
      // Don't call onStepError as it hides the form
      // Instead, let the form show validation errors inline
      console.log('Validation failed:', errors);
      return false;
    }

    setIsSaving(true);
    try {
      if (applicationId && applicationId !== 'new') {
        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'businessInfo', localData);

        // Mark step as completed in progress tracking
        await applicationProgressService.markStepCompleted(applicationId, 'business-info', localData);

        console.log('Business info data saved for application:', applicationId);
      } else {
        console.warn('No application ID available for saving business info');
      }

      setHasUnsavedChanges(false);
      onStepComplete?.('business-info', localData);
      return true;
    } catch (error: any) {
      console.error('Error saving business info data:', error);

      // Extract meaningful error message
      let errorMessage = 'Failed to save business information';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Show error in the form itself instead of hiding the form
      setValidationErrors(prev => ({ ...prev, save: errorMessage }));
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save button click
  const handleSave = async () => {
    await saveData();
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Business Information
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Describe your business operations, structure, and service delivery model.
        </p>
      </div>

      {/* Business Model */}
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Business Model *
          </label>
          <textarea
            value={localData.business_model || ''}
            onChange={(e) => handleLocalChange('business_model', e.target.value)}
            onBlur={() => handleBlur('business_model')}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your business model, revenue streams, and value proposition..."
          />
          {(validationErrors.business_model || errors.business_model) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.business_model || errors.business_model}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Operational Structure *
          </label>
          <textarea
            value={localData.operational_structure || ''}
            onChange={(e) => handleLocalChange('operational_structure', e.target.value)}
            onBlur={() => handleBlur('operational_structure')}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your organizational structure, departments, and operational processes..."
          />
          {(validationErrors.operational_structure || errors.operational_structure) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.operational_structure || errors.operational_structure}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Target Market *
          </label>
          <textarea
            value={localData.target_market || ''}
            onChange={(e) => handleLocalChange('target_market', e.target.value)}
            onBlur={() => handleBlur('target_market')}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your target customers and market segments..."
          />
          {(validationErrors.target_market || errors.target_market) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.target_market || errors.target_market}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Competitive Advantage *
          </label>
          <textarea
            value={localData.competitive_advantage || ''}
            onChange={(e) => handleLocalChange('competitive_advantage', e.target.value)}
            onBlur={() => handleBlur('competitive_advantage')}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="What makes your business unique and competitive in the market..."
          />
          {(validationErrors.competitive_advantage || errors.competitive_advantage) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.competitive_advantage || errors.competitive_advantage}
            </p>
          )}
        </div>
      </div>

      {/* Facilities and Equipment */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Facilities and Equipment
        </h4>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Facilities Description *
            </label>
            <textarea
              value={localData.facilities_description || ''}
              onChange={(e) => handleLocalChange('facilities_description', e.target.value)}
              onBlur={() => handleBlur('facilities_description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your office locations, facilities, and infrastructure..."
            />
            {(validationErrors.facilities_description || errors.facilities_description) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.facilities_description || errors.facilities_description}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Equipment Description *
            </label>
            <textarea
              value={localData.equipment_description || ''}
              onChange={(e) => handleLocalChange('equipment_description', e.target.value)}
              onBlur={() => handleBlur('equipment_description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="List and describe your equipment, technology, and tools..."
            />
            {(validationErrors.equipment_description || errors.equipment_description) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.equipment_description || errors.equipment_description}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Operational Areas *
            </label>
            <textarea
              value={localData.operational_areas || ''}
              onChange={(e) => handleLocalChange('operational_areas', e.target.value)}
              onBlur={() => handleBlur('operational_areas')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe the geographic areas where you operate or plan to operate..."
            />
            {(validationErrors.operational_areas || errors.operational_areas) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.operational_areas || errors.operational_areas}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Service Delivery */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Service Delivery
        </h4>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Service Delivery Model *
            </label>
            <textarea
              value={localData.service_delivery_model || ''}
              onChange={(e) => handleLocalChange('service_delivery_model', e.target.value)}
              onBlur={() => handleBlur('service_delivery_model')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe how you deliver services to customers..."
            />
            {(validationErrors.service_delivery_model || errors.service_delivery_model) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.service_delivery_model || errors.service_delivery_model}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Quality Assurance *
            </label>
            <textarea
              value={localData.quality_assurance || ''}
              onChange={(e) => handleLocalChange('quality_assurance', e.target.value)}
              onBlur={() => handleBlur('quality_assurance')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your quality control and assurance processes..."
            />
            {(validationErrors.quality_assurance || errors.quality_assurance) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.quality_assurance || errors.quality_assurance}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Customer Support *
            </label>
            <textarea
              value={localData.customer_support || ''}
              onChange={(e) => handleLocalChange('customer_support', e.target.value)}
              onBlur={() => handleBlur('customer_support')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your customer support and service processes..."
            />
            {(validationErrors.customer_support || errors.customer_support) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.customer_support || errors.customer_support}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {validationErrors.save && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Saving Business Information
              </h3>
              <p className="text-red-700 dark:text-red-300 text-sm mt-1">
                {validationErrors.save}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Business Information
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default BusinessInfo;

// Create placeholder components for the remaining steps
export { default as ServiceScope } from './ServiceScope';
export { default as BusinessPlan } from './BusinessPlan';
export { default as LegalHistory } from './LegalHistory';
export { default as ReviewSubmit } from './ReviewSubmit';
