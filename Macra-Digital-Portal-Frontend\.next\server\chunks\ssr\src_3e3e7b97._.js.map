{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', processApiResponse(response) );\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (processApiResponse(response) ?.data) {\r\n        applications = Array.isArray(processApiResponse(response).data) ? processApiResponse(response) .data : [];\r\n      } else if (Array.isArray(processApiResponse(response))) {\r\n        applications = processApiResponse(response);\r\n      } else if (processApiResponse(response)) {\r\n        // Single application or other structure\r\n        applications = [processApiResponse(response) ];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7D,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAY,MAAM;gBACvC,eAAe,MAAM,OAAO,CAAC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAW,IAAI,GAAG,EAAE;YAC3G,OAAO,IAAI,MAAM,OAAO,CAAC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY;gBACtD,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACpC,OAAO,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBACvC,wCAAwC;gBACxC,eAAe;oBAAC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;iBAAW;YAChD;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationProgressService.ts"], "sourcesContent": ["/**\n * Application Progress Service\n * Manages step completion tracking and progress calculation for license applications\n */\n\nimport { getLicenseTypeStepConfig, calculateProgress } from '@/config/licenseTypeStepConfig';\n\nexport interface StepProgress {\n  stepId: string;\n  stepName: string;\n  completed: boolean;\n  completedAt?: Date;\n  data?: any;\n}\n\nexport interface ApplicationProgress {\n  applicationId: string;\n  licenseTypeId: string;\n  totalSteps: number;\n  completedSteps: number;\n  progressPercentage: number;\n  steps: StepProgress[];\n  lastUpdated: Date;\n}\n\nclass ApplicationProgressService {\n  private progressCache = new Map<string, ApplicationProgress>();\n\n  /**\n   * Initialize progress tracking for a new application\n   */\n  async initializeProgress(applicationId: string, licenseTypeId: string): Promise<ApplicationProgress> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      throw new Error(`Invalid license type: ${licenseTypeId}`);\n    }\n\n    const steps: StepProgress[] = licenseConfig.steps.map(step => ({\n      stepId: step.id,\n      stepName: step.name,\n      completed: false\n    }));\n\n    const progress: ApplicationProgress = {\n      applicationId,\n      licenseTypeId,\n      totalSteps: steps.length,\n      completedSteps: 0,\n      progressPercentage: 0,\n      steps,\n      lastUpdated: new Date()\n    };\n\n    this.progressCache.set(applicationId, progress);\n    await this.saveProgressToStorage(progress);\n    \n    return progress;\n  }\n\n  /**\n   * Mark a step as completed\n   */\n  async markStepCompleted(applicationId: string, stepId: string, data?: any): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (!progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = true;\n      progress.steps[stepIndex].completedAt = new Date();\n      progress.steps[stepIndex].data = data;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Mark a step as incomplete (for editing)\n   */\n  async markStepIncomplete(applicationId: string, stepId: string): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = false;\n      progress.steps[stepIndex].completedAt = undefined;\n      progress.steps[stepIndex].data = undefined;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get current progress for an application\n   */\n  async getProgress(applicationId: string): Promise<ApplicationProgress | null> {\n    // Check cache first\n    if (this.progressCache.has(applicationId)) {\n      return this.progressCache.get(applicationId)!;\n    }\n\n    // Load from storage\n    const progress = await this.loadProgressFromStorage(applicationId);\n    if (progress) {\n      this.progressCache.set(applicationId, progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get completed step IDs for an application\n   */\n  async getCompletedStepIds(applicationId: string): Promise<string[]> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return [];\n\n    return progress.steps\n      .filter(step => step.completed)\n      .map(step => step.stepId);\n  }\n\n  /**\n   * Check if a specific step is completed\n   */\n  async isStepCompleted(applicationId: string, stepId: string): Promise<boolean> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return false;\n\n    const step = progress.steps.find(s => s.stepId === stepId);\n    return step?.completed || false;\n  }\n\n  /**\n   * Get next incomplete step\n   */\n  async getNextIncompleteStep(applicationId: string): Promise<StepProgress | null> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return null;\n\n    return progress.steps.find(step => !step.completed) || null;\n  }\n\n  /**\n   * Calculate overall application completion status\n   */\n  async getApplicationStatus(applicationId: string): Promise<'not_started' | 'in_progress' | 'completed'> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return 'not_started';\n\n    if (progress.completedSteps === 0) return 'not_started';\n    if (progress.completedSteps === progress.totalSteps) return 'completed';\n    return 'in_progress';\n  }\n\n  /**\n   * Save progress to localStorage (in a real app, this would be an API call)\n   */\n  private async saveProgressToStorage(progress: ApplicationProgress): Promise<void> {\n    try {\n      const key = `application_progress_${progress.applicationId}`;\n      localStorage.setItem(key, JSON.stringify({\n        ...progress,\n        lastUpdated: progress.lastUpdated.toISOString()\n      }));\n    } catch (error) {\n      console.error('Error saving progress to storage:', error);\n    }\n  }\n\n  /**\n   * Load progress from localStorage (in a real app, this would be an API call)\n   */\n  private async loadProgressFromStorage(applicationId: string): Promise<ApplicationProgress | null> {\n    try {\n      const key = `application_progress_${applicationId}`;\n      const stored = localStorage.getItem(key);\n      \n      if (!stored) return null;\n\n      const parsed = JSON.parse(stored);\n      return {\n        ...parsed,\n        lastUpdated: new Date(parsed.lastUpdated),\n        steps: parsed.steps.map((step: any) => ({\n          ...step,\n          completedAt: step.completedAt ? new Date(step.completedAt) : undefined\n        }))\n      };\n    } catch (error) {\n      console.error('Error loading progress from storage:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Clear progress cache (useful for testing or when switching applications)\n   */\n  clearCache(): void {\n    this.progressCache.clear();\n  }\n\n  /**\n   * Delete progress for an application\n   */\n  async deleteProgress(applicationId: string): Promise<void> {\n    this.progressCache.delete(applicationId);\n    \n    try {\n      const key = `application_progress_${applicationId}`;\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error deleting progress from storage:', error);\n    }\n  }\n}\n\n// Export singleton instance\nexport const applicationProgressService = new ApplicationProgressService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAoBA,MAAM;IACI,gBAAgB,IAAI,MAAmC;IAE/D;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,aAAqB,EAAgC;QACnG,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,eAAe;QAC1D;QAEA,MAAM,QAAwB,cAAc,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7D,QAAQ,KAAK,EAAE;gBACf,UAAU,KAAK,IAAI;gBACnB,WAAW;YACb,CAAC;QAED,MAAM,WAAgC;YACpC;YACA;YACA,YAAY,MAAM,MAAM;YACxB,gBAAgB;YAChB,oBAAoB;YACpB;YACA,aAAa,IAAI;QACnB;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,kBAAkB,aAAqB,EAAE,MAAc,EAAE,IAAU,EAAgC;QACvG,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;YAC5C,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,MAAc,EAAgC;QAC5F,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACvC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,YAAY,aAAqB,EAAuC;QAC5E,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB;YACzC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAChC;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,IAAI,CAAC,uBAAuB,CAAC;QACpD,IAAI,UAAU;YACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACxC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBAAoB,aAAqB,EAAqB;QAClE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,OAAO,SAAS,KAAK,CAClB,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAC7B,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC5B;IAEA;;GAEC,GACD,MAAM,gBAAgB,aAAqB,EAAE,MAAc,EAAoB;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACnD,OAAO,MAAM,aAAa;IAC5B;IAEA;;GAEC,GACD,MAAM,sBAAsB,aAAqB,EAAgC;QAC/E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,KAAK;IACzD;IAEA;;GAEC,GACD,MAAM,qBAAqB,aAAqB,EAAwD;QACtG,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,IAAI,SAAS,cAAc,KAAK,GAAG,OAAO;QAC1C,IAAI,SAAS,cAAc,KAAK,SAAS,UAAU,EAAE,OAAO;QAC5D,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,sBAAsB,QAA6B,EAAiB;QAChF,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,SAAS,aAAa,EAAE;YAC5D,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBACvC,GAAG,QAAQ;gBACX,aAAa,SAAS,WAAW,CAAC,WAAW;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD,MAAc,wBAAwB,aAAqB,EAAuC;QAChG,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO;gBACL,GAAG,MAAM;gBACT,aAAa,IAAI,KAAK,OAAO,WAAW;gBACxC,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBACtC,GAAG,IAAI;wBACP,aAAa,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI;oBAC/D,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA;;GAEC,GACD,MAAM,eAAe,aAAqB,EAAiB;QACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAE1B,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;AACF;AAGO,MAAM,6BAA6B,IAAI", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info\r\n      const applicantRequiredFields = [\r\n        'applicant_type', 'first_name', 'last_name', 'email', 'phone',\r\n        'national_id', 'date_of_birth', 'nationality', 'gender',\r\n        'postal_address', 'physical_address', 'city', 'district'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Phone validation\r\n      if (data.phone && !/^(\\+265|0)?[1-9]\\d{7,8}$/.test(data.phone)) {\r\n        errors.phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,qCAAqC;YACrC,MAAM,0BAA0B;gBAC9B;gBAAkB;gBAAc;gBAAa;gBAAS;gBACtD;gBAAe;gBAAiB;gBAAe;gBAC/C;gBAAkB;gBAAoB;gBAAQ;aAC/C;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/business-info/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport CustomerLayout from '@/components/customer/CustomerLayout';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LicenseCategory } from '@/services/licenseCategoryService';\nimport { applicationService } from '@/services/applicationService';\nimport { applicationProgressService } from '@/services/applicationProgressService';\nimport { validateSection } from '@/utils/formValidation';\nimport { getLicenseTypeStepConfig, StepConfig, LicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';\nimport { CustomerApiService } from '@/lib/customer-api';\n\nconst BusinessInfoPage: React.FC = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  // Create customer API service instance\n  const customerApi = new CustomerApiService();\n\n  // Get query parameters\n  const licenseCategoryId = searchParams.get('license_category_id');\n  const applicationId = searchParams.get('application_id');\n\n  // State\n  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);\n  const [licenseTypeConfig, setLicenseTypeConfig] = useState<LicenseTypeStepConfig | null>(null);\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Form data state\n  const [formData, setFormData] = useState({\n    business_model: '',\n    operational_structure: '',\n    target_market: '',\n    competitive_advantage: '',\n    facilities_description: '',\n    equipment_description: '',\n    operational_areas: '',\n    service_delivery_model: '',\n    quality_assurance: '',\n    customer_support: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  // Load initial data\n  useEffect(() => {\n    const loadData = async () => {\n      if (!licenseCategoryId) {\n        setError('License category ID is required');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        setLoading(true);\n\n        // Load license category\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\n        setLicenseCategory(category);\n\n        // Get license type configuration\n        const config = getLicenseTypeStepConfig(category.license_type.name);\n        setLicenseTypeConfig(config);\n        setApplicationSteps(config.steps);\n\n        // Load existing application data if in edit mode\n        if (applicationId && applicationId !== 'new') {\n          try {\n            const application = await applicationService.getApplication(applicationId);\n            if (application && application.business_info) {\n              setFormData(prev => ({ ...prev, ...application.business_info }));\n            }\n          } catch (error) {\n            console.error('Error loading existing application data:', error);\n          }\n        }\n\n      } catch (error: any) {\n        console.error('Error loading data:', error);\n        setError(error.message || 'Failed to load application data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (!authLoading) {\n      loadData();\n    }\n  }, [licenseCategoryId, applicationId, authLoading]);\n\n  // Handle form field changes\n  const handleChange = useCallback((field: string, value: any) => {\n    setFormData((prev: any) => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n\n    // Clear save error when user starts making changes\n    if (validationErrors.save) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));\n    }\n  }, [validationErrors]);\n\n  // Handle field blur for validation\n  const handleBlur = (field: string) => {\n    // Validate single field on blur\n    const validation = validateSection({ [field]: formData[field] }, 'businessInfo');\n    if (validation.errors && validation.errors[field]) {\n      setValidationErrors(prev => ({ ...prev, [field]: validation.errors[field] }));\n    }\n  };\n\n  // Validate form data\n  const validateForm = () => {\n    const validation = validateSection(formData, 'businessInfo');\n    const errors = validation.errors || {};\n    setValidationErrors(errors);\n    return validation.isValid;\n  };\n\n  // Save data to backend\n  const saveData = async () => {\n    const validation = validateSection(formData, 'businessInfo');\n    const errors = validation.errors || {};\n    setValidationErrors(errors);\n\n    if (!validation.isValid) {\n      console.log('Validation failed:', errors);\n      return false;\n    }\n\n    if (!applicationId || applicationId === 'new') {\n      setValidationErrors(prev => ({ ...prev, save: 'Application ID is required to save business information' }));\n      return false;\n    }\n\n    setIsSaving(true);\n    try {\n      // Update application with business info\n      await applicationService.updateApplication(applicationId, {\n        business_info: formData\n      });\n\n      // Mark step as completed in progress tracking\n      await applicationProgressService.markStepCompleted(applicationId, 'business-info', formData);\n\n      console.log('Business info data saved for application:', applicationId);\n      setHasUnsavedChanges(false);\n      return true;\n    } catch (error: any) {\n      console.error('Error saving business info data:', error);\n\n      // Extract meaningful error message\n      let errorMessage = 'Failed to save business information';\n      if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      setValidationErrors(prev => ({ ...prev, save: errorMessage }));\n      return false;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Handle save button click\n  const handleSave = async () => {\n    const success = await saveData();\n    if (success) {\n      // Navigate to next step or show success message\n      const currentStepIndex = applicationSteps.findIndex(step => step.id === 'business-info');\n      if (currentStepIndex >= 0 && currentStepIndex < applicationSteps.length - 1) {\n        const nextStep = applicationSteps[currentStepIndex + 1];\n        router.push(`/customer/applications/apply/${nextStep.id}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);\n      }\n    }\n  };\n\n  // Handle navigation\n  const handleNavigation = (stepId: string) => {\n    if (hasUnsavedChanges) {\n      if (confirm('You have unsaved changes. Do you want to save before navigating?')) {\n        saveData().then(() => {\n          router.push(`/customer/applications/apply/${stepId}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);\n        });\n      } else {\n        router.push(`/customer/applications/apply/${stepId}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);\n      }\n    } else {\n      router.push(`/customer/applications/apply/${stepId}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);\n    }\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <i className=\"ri-loader-4-line animate-spin text-4xl text-primary mb-4\"></i>\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading business information form...</p>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <i className=\"ri-error-warning-line text-4xl text-red-500 mb-4\"></i>\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2\">Error Loading Form</h2>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">{error}</p>\n            <button\n              onClick={() => router.back()}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-arrow-left-line mr-2\"></i>\n              Go Back\n            </button>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  // Authentication check\n  if (!isAuthenticated) {\n    router.push('/customer/auth/login');\n    return null;\n  }\n\n  return (\n    <CustomerLayout>\n      <div className=\"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                Business Information\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {licenseCategory?.name} - {licenseCategory?.license_type?.name}\n              </p>\n            </div>\n            <button\n              onClick={() => router.back()}\n              className=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-arrow-left-line mr-2\"></i>\n              Back\n            </button>\n          </div>\n        </div>\n\n        {/* Progress Steps */}\n        {applicationSteps.length > 0 && (\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between\">\n              {applicationSteps.map((step, index) => {\n                const isActive = step.id === 'business-info';\n                const isCompleted = false; // You can implement completion tracking\n\n                return (\n                  <div key={step.id} className=\"flex items-center\">\n                    <div\n                      className={`flex items-center justify-center w-8 h-8 rounded-full border-2 cursor-pointer ${\n                        isActive\n                          ? 'border-primary bg-primary text-white'\n                          : isCompleted\n                          ? 'border-green-500 bg-green-500 text-white'\n                          : 'border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400'\n                      }`}\n                      onClick={() => handleNavigation(step.id)}\n                    >\n                      {isCompleted ? (\n                        <i className=\"ri-check-line text-sm\"></i>\n                      ) : (\n                        <span className=\"text-sm font-medium\">{index + 1}</span>\n                      )}\n                    </div>\n                    <span\n                      className={`ml-2 text-sm font-medium cursor-pointer ${\n                        isActive\n                          ? 'text-primary'\n                          : isCompleted\n                          ? 'text-green-600 dark:text-green-400'\n                          : 'text-gray-500 dark:text-gray-400'\n                      }`}\n                      onClick={() => handleNavigation(step.id)}\n                    >\n                      {step.name}\n                    </span>\n                    {index < applicationSteps.length - 1 && (\n                      <div className=\"flex-1 h-0.5 bg-gray-300 dark:bg-gray-600 mx-4\"></div>\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {/* Form Content */}\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n          <div className=\"space-y-6\">\n            <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                Business Information\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                Describe your business operations, structure, and service delivery model.\n              </p>\n            </div>\n\n            {/* Business Model */}\n            <div className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Business Model *\n                </label>\n                <textarea\n                  value={formData.business_model || ''}\n                  onChange={(e) => handleChange('business_model', e.target.value)}\n                  onBlur={() => handleBlur('business_model')}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                  placeholder=\"Describe your business model, revenue streams, and value proposition...\"\n                />\n                {validationErrors.business_model && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                    {validationErrors.business_model}\n                  </p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Operational Structure *\n                </label>\n                <textarea\n                  value={formData.operational_structure || ''}\n                  onChange={(e) => handleChange('operational_structure', e.target.value)}\n                  onBlur={() => handleBlur('operational_structure')}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                  placeholder=\"Describe your organizational structure, departments, and operational processes...\"\n                />\n                {validationErrors.operational_structure && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                    {validationErrors.operational_structure}\n                  </p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Target Market *\n                </label>\n                <textarea\n                  value={formData.target_market || ''}\n                  onChange={(e) => handleChange('target_market', e.target.value)}\n                  onBlur={() => handleBlur('target_market')}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                  placeholder=\"Describe your target customers and market segments...\"\n                />\n                {validationErrors.target_market && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                    {validationErrors.target_market}\n                  </p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Competitive Advantage *\n                </label>\n                <textarea\n                  value={formData.competitive_advantage || ''}\n                  onChange={(e) => handleChange('competitive_advantage', e.target.value)}\n                  onBlur={() => handleBlur('competitive_advantage')}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                  placeholder=\"What makes your business unique and competitive in the market...\"\n                />\n                {validationErrors.competitive_advantage && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                    {validationErrors.competitive_advantage}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Facilities and Equipment */}\n            <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n                Facilities and Equipment\n              </h4>\n\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Facilities Description *\n                  </label>\n                  <textarea\n                    value={formData.facilities_description || ''}\n                    onChange={(e) => handleChange('facilities_description', e.target.value)}\n                    onBlur={() => handleBlur('facilities_description')}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                    placeholder=\"Describe your office locations, facilities, and infrastructure...\"\n                  />\n                  {validationErrors.facilities_description && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                      {validationErrors.facilities_description}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Equipment Description *\n                  </label>\n                  <textarea\n                    value={formData.equipment_description || ''}\n                    onChange={(e) => handleChange('equipment_description', e.target.value)}\n                    onBlur={() => handleBlur('equipment_description')}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                    placeholder=\"List and describe your equipment, technology, and tools...\"\n                  />\n                  {validationErrors.equipment_description && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                      {validationErrors.equipment_description}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Operational Areas *\n                  </label>\n                  <textarea\n                    value={formData.operational_areas || ''}\n                    onChange={(e) => handleChange('operational_areas', e.target.value)}\n                    onBlur={() => handleBlur('operational_areas')}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                    placeholder=\"Describe the geographic areas where you operate or plan to operate...\"\n                  />\n                  {validationErrors.operational_areas && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                      {validationErrors.operational_areas}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Service Delivery */}\n            <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n                Service Delivery\n              </h4>\n\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Service Delivery Model *\n                  </label>\n                  <textarea\n                    value={formData.service_delivery_model || ''}\n                    onChange={(e) => handleChange('service_delivery_model', e.target.value)}\n                    onBlur={() => handleBlur('service_delivery_model')}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                    placeholder=\"Describe how you deliver services to customers...\"\n                  />\n                  {validationErrors.service_delivery_model && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                      {validationErrors.service_delivery_model}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Quality Assurance *\n                  </label>\n                  <textarea\n                    value={formData.quality_assurance || ''}\n                    onChange={(e) => handleChange('quality_assurance', e.target.value)}\n                    onBlur={() => handleBlur('quality_assurance')}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                    placeholder=\"Describe your quality control and assurance processes...\"\n                  />\n                  {validationErrors.quality_assurance && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                      {validationErrors.quality_assurance}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Customer Support *\n                  </label>\n                  <textarea\n                    value={formData.customer_support || ''}\n                    onChange={(e) => handleChange('customer_support', e.target.value)}\n                    onBlur={() => handleBlur('customer_support')}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                    placeholder=\"Describe your customer support and service processes...\"\n                  />\n                  {validationErrors.customer_support && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                      {validationErrors.customer_support}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Error Display */}\n            {validationErrors.save && (\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\n                  <div>\n                    <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                      Error Saving Business Information\n                    </h3>\n                    <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\n                      {validationErrors.save}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Save Button */}\n            <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n              <button\n                onClick={handleSave}\n                disabled={isSaving || isLoading}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isSaving || isLoading ? (\n                  <>\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                    Saving...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"ri-save-line mr-2\"></i>\n                    Save Business Information\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </CustomerLayout>\n  );\n};\n\nexport default BusinessInfoPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAaA,MAAM,mBAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,uCAAuC;IACvC,MAAM,cAAc,IAAI,6HAAA,CAAA,qBAAkB;IAE1C,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,gBAAgB;QAChB,uBAAuB;QACvB,eAAe;QACf,uBAAuB;QACvB,wBAAwB;QACxB,uBAAuB;QACvB,mBAAmB;QACnB,wBAAwB;QACxB,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI,CAAC,mBAAmB;gBACtB,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBAEX,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,mBAAmB;gBAEnB,iCAAiC;gBACjC,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,YAAY,CAAC,IAAI;gBAClE,qBAAqB;gBACrB,oBAAoB,OAAO,KAAK;gBAEhC,iDAAiD;gBACjD,IAAI,iBAAiB,kBAAkB,OAAO;oBAC5C,IAAI;wBACF,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAC5D,IAAI,eAAe,YAAY,aAAa,EAAE;4BAC5C,YAAY,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,GAAG,YAAY,aAAa;gCAAC,CAAC;wBAChE;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4CAA4C;oBAC5D;gBACF;YAEF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,SAAS,MAAM,OAAO,IAAI;YAC5B,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,CAAC,aAAa;YAChB;QACF;IACF,GAAG;QAAC;QAAmB;QAAe;KAAY;IAElD,4BAA4B;IAC5B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC/C,YAAY,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACvD,qBAAqB;QAErB,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;QAEA,mDAAmD;QACnD,IAAI,iBAAiB,IAAI,EAAE;YACzB,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAG,CAAC;QAC9E;IACF,GAAG;QAAC;KAAiB;IAErB,mCAAmC;IACnC,MAAM,aAAa,CAAC;QAClB,gCAAgC;QAChC,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE;YAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM;QAAC,GAAG;QACjE,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,EAAE;YACjD,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE,WAAW,MAAM,CAAC,MAAM;gBAAC,CAAC;QAC7E;IACF;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;QAC7C,MAAM,SAAS,WAAW,MAAM,IAAI,CAAC;QACrC,oBAAoB;QACpB,OAAO,WAAW,OAAO;IAC3B;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;QAC7C,MAAM,SAAS,WAAW,MAAM,IAAI,CAAC;QACrC,oBAAoB;QAEpB,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO;QACT;QAEA,IAAI,CAAC,iBAAiB,kBAAkB,OAAO;YAC7C,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAA0D,CAAC;YACzG,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,wCAAwC;YACxC,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,eAAe;gBACxD,eAAe;YACjB;YAEA,8CAA8C;YAC9C,MAAM,6IAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,eAAe,iBAAiB;YAEnF,QAAQ,GAAG,CAAC,6CAA6C;YACzD,qBAAqB;YACrB,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAElD,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAa,CAAC;YAC5D,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;QACjB,MAAM,UAAU,MAAM;QACtB,IAAI,SAAS;YACX,gDAAgD;YAChD,MAAM,mBAAmB,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACxE,IAAI,oBAAoB,KAAK,mBAAmB,iBAAiB,MAAM,GAAG,GAAG;gBAC3E,MAAM,WAAW,gBAAgB,CAAC,mBAAmB,EAAE;gBACvD,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,qBAAqB,EAAE,kBAAkB,gBAAgB,EAAE,eAAe;YACpI;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,CAAC;QACxB,IAAI,mBAAmB;YACrB,IAAI,QAAQ,qEAAqE;gBAC/E,WAAW,IAAI,CAAC;oBACd,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,OAAO,qBAAqB,EAAE,kBAAkB,gBAAgB,EAAE,eAAe;gBAC/H;YACF,OAAO;gBACL,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,OAAO,qBAAqB,EAAE,kBAAkB,gBAAgB,EAAE,eAAe;YAC/H;QACF,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,OAAO,qBAAqB,EAAE,kBAAkB,gBAAgB,EAAE,eAAe;QAC/H;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCACtD,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;IAOvD;IAEA,uBAAuB;IACvB,IAAI,CAAC,iBAAiB;QACpB,OAAO,IAAI,CAAC;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,8OAAC;wCAAE,WAAU;;4CACV,iBAAiB;4CAAK;4CAAI,iBAAiB,cAAc;;;;;;;;;;;;;0CAG9D,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA8B;;;;;;;;;;;;;;;;;;gBAOhD,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;4BAC3B,MAAM,WAAW,KAAK,EAAE,KAAK;4BAC7B,MAAM,cAAc,OAAO,wCAAwC;4BAEnE,qBACE,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCACC,WAAW,CAAC,8EAA8E,EACxF,WACI,yCACA,6EAEA,yEACJ;wCACF,SAAS,IAAM,iBAAiB,KAAK,EAAE;kDAEtC,2FAGC,8OAAC;4CAAK,WAAU;sDAAuB,QAAQ;;;;;;;;;;;kDAGnD,8OAAC;wCACC,WAAW,CAAC,wCAAwC,EAClD,WACI,iBACA,6EAEA,oCACJ;wCACF,SAAS,IAAM,iBAAiB,KAAK,EAAE;kDAEtC,KAAK,IAAI;;;;;;oCAEX,QAAQ,iBAAiB,MAAM,GAAG,mBACjC,8OAAC;wCAAI,WAAU;;;;;;;+BA9BT,KAAK,EAAE;;;;;wBAkCrB;;;;;;;;;;;8BAMN,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuD;;;;;;kDAGrE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAM/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,OAAO,SAAS,cAAc,IAAI;gDAClC,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAC9D,QAAQ,IAAM,WAAW;gDACzB,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;4CAEb,iBAAiB,cAAc,kBAC9B,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,cAAc;;;;;;;;;;;;kDAKtC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,OAAO,SAAS,qBAAqB,IAAI;gDACzC,UAAU,CAAC,IAAM,aAAa,yBAAyB,EAAE,MAAM,CAAC,KAAK;gDACrE,QAAQ,IAAM,WAAW;gDACzB,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;4CAEb,iBAAiB,qBAAqB,kBACrC,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,qBAAqB;;;;;;;;;;;;kDAK7C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,OAAO,SAAS,aAAa,IAAI;gDACjC,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAC7D,QAAQ,IAAM,WAAW;gDACzB,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;4CAEb,iBAAiB,aAAa,kBAC7B,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,aAAa;;;;;;;;;;;;kDAKrC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,OAAO,SAAS,qBAAqB,IAAI;gDACzC,UAAU,CAAC,IAAM,aAAa,yBAAyB,EAAE,MAAM,CAAC,KAAK;gDACrE,QAAQ,IAAM,WAAW;gDACzB,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;4CAEb,iBAAiB,qBAAqB,kBACrC,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,qBAAqB;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,sBAAsB,IAAI;wDAC1C,UAAU,CAAC,IAAM,aAAa,0BAA0B,EAAE,MAAM,CAAC,KAAK;wDACtE,QAAQ,IAAM,WAAW;wDACzB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,iBAAiB,sBAAsB,kBACtC,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,sBAAsB;;;;;;;;;;;;0DAK9C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,qBAAqB,IAAI;wDACzC,UAAU,CAAC,IAAM,aAAa,yBAAyB,EAAE,MAAM,CAAC,KAAK;wDACrE,QAAQ,IAAM,WAAW;wDACzB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,iBAAiB,qBAAqB,kBACrC,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,qBAAqB;;;;;;;;;;;;0DAK7C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,iBAAiB,IAAI;wDACrC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACjE,QAAQ,IAAM,WAAW;wDACzB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,iBAAiB,iBAAiB,kBACjC,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,sBAAsB,IAAI;wDAC1C,UAAU,CAAC,IAAM,aAAa,0BAA0B,EAAE,MAAM,CAAC,KAAK;wDACtE,QAAQ,IAAM,WAAW;wDACzB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,iBAAiB,sBAAsB,kBACtC,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,sBAAsB;;;;;;;;;;;;0DAK9C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,iBAAiB,IAAI;wDACrC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACjE,QAAQ,IAAM,WAAW;wDACzB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,iBAAiB,iBAAiB,kBACjC,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,iBAAiB;;;;;;;;;;;;0DAKzC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,gBAAgB,IAAI;wDACpC,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDAChE,QAAQ,IAAM,WAAW;wDACzB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,iBAAiB,gBAAgB,kBAChC,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;4BAQ3C,iBAAiB,IAAI,kBACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqD;;;;;;8DAGnE,8OAAC;oDAAE,WAAU;8DACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;0CAQhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU,YAAY;oCACtB,WAAU;8CAET,YAAY,0BACX;;0DACE,8OAAC;gDAAE,WAAU;;;;;;4CAAyC;;qEAIxD;;0DACE,8OAAC;gDAAE,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD;uCAEe", "debugId": null}}]}