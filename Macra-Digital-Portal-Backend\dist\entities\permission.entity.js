"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permission = exports.PERMISSION_NAMES = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const role_entity_1 = require("./role.entity");
exports.PERMISSION_NAMES = {
    USER_CREATE: 'user:create',
    USER_READ: 'user:read',
    USER_UPDATE: 'user:update',
    USER_DELETE: 'user:delete',
    ROLE_CREATE: 'role:create',
    ROLE_READ: 'role:read',
    ROLE_UPDATE: 'role:update',
    ROLE_DELETE: 'role:delete',
    PERMISSION_CREATE: 'permission:create',
    PERMISSION_READ: 'permission:read',
    PERMISSION_UPDATE: 'permission:update',
    PERMISSION_DELETE: 'permission:delete',
    LICENSE_CREATE: 'license:create',
    LICENSE_READ: 'license:read',
    LICENSE_UPDATE: 'license:update',
    LICENSE_DELETE: 'license:delete',
    LICENSE_APPROVE: 'license:approve',
    LICENSE_REJECT: 'license:reject',
    LICENSE_TYPE_CREATE: 'license_type:create',
    LICENSE_TYPE_READ: 'license_type:read',
    LICENSE_TYPE_UPDATE: 'license_type:update',
    LICENSE_TYPE_DELETE: 'license_type:delete',
    LICENSE_CATEGORY_CREATE: 'license_category:create',
    LICENSE_CATEGORY_READ: 'license_category:read',
    LICENSE_CATEGORY_UPDATE: 'license_category:update',
    LICENSE_CATEGORY_DELETE: 'license_category:delete',
    APPLICATION_CREATE: 'application:create',
    APPLICATION_READ: 'application:read',
    APPLICATION_UPDATE: 'application:update',
    APPLICATION_DELETE: 'application:delete',
    APPLICATION_EVALUATE: 'application:evaluate',
    APPLICATION_SUBMIT: 'application:submit',
    APPLICATION_APPROVE: 'application:approve',
    APPLICATION_REJECT: 'application:reject',
    FINANCIAL_READ: 'financial:read',
    FINANCIAL_UPDATE: 'financial:update',
    FINANCIAL_REPORTS: 'financial:reports',
    INVOICE_CREATE: 'invoice:create',
    INVOICE_READ: 'invoice:read',
    INVOICE_UPDATE: 'invoice:update',
    INVOICE_DELETE: 'invoice:delete',
    PAYMENT_CREATE: 'payment:create',
    PAYMENT_READ: 'payment:read',
    PAYMENT_UPDATE: 'payment:update',
    PAYMENT_DELETE: 'payment:delete',
    DOCUMENT_CREATE: 'document:create',
    DOCUMENT_READ: 'document:read',
    DOCUMENT_UPDATE: 'document:update',
    DOCUMENT_DELETE: 'document:delete',
    DOCUMENT_DOWNLOAD: 'document:download',
    IDENTIFICATION_TYPE_CREATE: 'identification_type:create',
    IDENTIFICATION_TYPE_READ: 'identification_type:read',
    IDENTIFICATION_TYPE_UPDATE: 'identification_type:update',
    IDENTIFICATION_TYPE_DELETE: 'identification_type:delete',
    CONTACT_CREATE: 'contact:create',
    CONTACT_READ: 'contact:read',
    CONTACT_UPDATE: 'contact:update',
    CONTACT_DELETE: 'contact:delete',
    APPLICANT_CREATE: 'applicant:create',
    APPLICANT_READ: 'applicant:read',
    APPLICANT_UPDATE: 'applicant:update',
    APPLICANT_DELETE: 'applicant:delete',
    EVALUATION_CREATE: 'evaluation:create',
    EVALUATION_READ: 'evaluation:read',
    EVALUATION_UPDATE: 'evaluation:update',
    EVALUATION_DELETE: 'evaluation:delete',
    EVALUATION_SUBMIT: 'evaluation:submit',
    NOTIFICATION_CREATE: 'notification:create',
    NOTIFICATION_READ: 'notification:read',
    NOTIFICATION_UPDATE: 'notification:update',
    NOTIFICATION_DELETE: 'notification:delete',
    NOTIFICATION_SEND: 'notification:send',
    SYSTEM_SETTINGS: 'system:settings',
    SYSTEM_AUDIT: 'system:audit',
    SYSTEM_BACKUP: 'system:backup',
    SYSTEM_MAINTENANCE: 'system:maintenance',
    REPORT_GENERATE: 'report:generate',
    REPORT_VIEW: 'report:view',
    REPORT_EXPORT: 'report:export',
    REPORT_SCHEDULE: 'report:schedule',
};
let Permission = class Permission {
    permission_id;
    name;
    description;
    category;
    created_at;
    updated_at;
    deleted_at;
    created_by;
    updated_by;
    creator;
    updater;
    roles;
    generateId() {
        if (!this.permission_id) {
            this.permission_id = (0, uuid_1.v4)();
        }
    }
};
exports.Permission = Permission;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], Permission.prototype, "permission_id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        unique: true,
    }),
    __metadata("design:type", String)
], Permission.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], Permission.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Permission.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Permission.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Permission.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Permission.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], Permission.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], Permission.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, (role) => role.permissions),
    __metadata("design:type", Array)
], Permission.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Permission.prototype, "generateId", null);
exports.Permission = Permission = __decorate([
    (0, typeorm_1.Entity)('permissions')
], Permission);
//# sourceMappingURL=permission.entity.js.map