{"version": 3, "file": "documents.entity.js", "sourceRoot": "", "sources": ["../../src/entities/documents.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQ8B;AAC9B,+BAAoC;AACpC,+CAAqC;AACrC,+DAAqD;AAErD,IAAY,YAgBX;AAhBD,WAAY,YAAY;IACtB,uEAAuD,CAAA;IACvD,iEAAiD,CAAA;IACjD,iEAAiD,CAAA;IACjD,+CAA+B,CAAA;IAC/B,6DAA6C,CAAA;IAC7C,yDAAyC,CAAA;IACzC,+CAA+B,CAAA;IAC/B,mDAAmC,CAAA;IACnC,qEAAqD,CAAA;IACrD,+DAA+C,CAAA;IAC/C,+CAA+B,CAAA;IAC/B,qDAAqC,CAAA;IACrC,iDAAiC,CAAA;IACjC,2CAA2B,CAAA;IAC3B,+BAAe,CAAA;AACjB,CAAC,EAhBW,YAAY,4BAAZ,YAAY,QAgBvB;AAGM,IAAM,SAAS,GAAf,MAAM,SAAS;IAOpB,WAAW,CAAS;IAGpB,cAAc,CAAU;IAMxB,aAAa,CAAe;IAG5B,SAAS,CAAS;IAGlB,WAAW,CAAS;IAGpB,SAAS,CAAS;IAGlB,SAAS,CAAS;IAGlB,SAAS,CAAS;IAGlB,SAAS,CAAS;IAGlB,WAAW,CAAU;IAGrB,UAAU,CAAO;IAGjB,UAAU,CAAS;IAGnB,UAAU,CAAO;IAGjB,UAAU,CAAU;IAGpB,UAAU,CAAQ;IAKlB,WAAW,CAAgB;IAI3B,OAAO,CAAO;IAId,OAAO,CAAQ;IAGf,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,IAAA,SAAM,GAAE,CAAC;QAC9B,CAAC;IACH,CAAC;CACF,CAAA;AAzEY,8BAAS;AAOpB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;KACb,CAAC;;8CACkB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACjB;AAMxB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,YAAY;KACnB,CAAC;;gDAC0B;AAG5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;4CACvB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;8CACrB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;4CACP;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;4CACP;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;4CACN;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;4CACvB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;8CACvB;AAGrB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;6CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CACN;AAGnB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;6CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CACrB;AAGpB;IADC,IAAA,0BAAgB,GAAE;8BACN,IAAI;6CAAC;AAKlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kCAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACjD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;8BACzB,kCAAY;8CAAC;AAI3B;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,CAAC;IACrB,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC1B,kBAAI;0CAAC;AAId;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,kBAAI;0CAAC;AAGf;IADC,IAAA,sBAAY,GAAE;;;;2CAKd;oBAxEU,SAAS;IADrB,IAAA,gBAAM,EAAC,WAAW,CAAC;GACP,SAAS,CAyErB"}