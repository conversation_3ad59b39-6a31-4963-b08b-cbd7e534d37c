{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', processApiResponse(response) );\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (processApiResponse(response) ?.data) {\r\n        applications = Array.isArray(processApiResponse(response).data) ? processApiResponse(response) .data : [];\r\n      } else if (Array.isArray(processApiResponse(response))) {\r\n        applications = processApiResponse(response);\r\n      } else if (processApiResponse(response)) {\r\n        // Single application or other structure\r\n        applications = [processApiResponse(response) ];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7D,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAY,MAAM;gBACvC,eAAe,MAAM,OAAO,CAAC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAW,IAAI,GAAG,EAAE;YAC3G,OAAO,IAAI,MAAM,OAAO,CAAC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY;gBACtD,eAAe,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACpC,OAAO,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBACvC,wCAAwC;gBACxC,eAAe;oBAAC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;iBAAW;YAChD;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport '@/styles/dashboard.css';\r\n\r\nexport default function Dashboard() {\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [applicationStats, setApplicationStats] = useState<Record<string, number>>({});\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  // Helper function to calculate pending applications\r\n  const getPendingApplicationsCount = () => {\r\n    return (applicationStats.submitted || 0) + (applicationStats.under_review || 0) + (applicationStats.evaluation || 0);\r\n  };\r\n\r\n  // Helper function to get new submissions count\r\n  const getNewSubmissionsCount = () => {\r\n    return applicationStats.submitted || 0;\r\n  };\r\n\r\n  // Set mounted state to prevent hydration errors\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Only run on client side to prevent hydration errors\r\n    if (typeof window === 'undefined') return;\r\n\r\n    // Listen for tab changes from header\r\n    const handleTabChange = (event: CustomEvent) => {\r\n      setActiveTab(event.detail.tab);\r\n    };\r\n\r\n    window.addEventListener('tabChange', handleTabChange as EventListener);\r\n\r\n    return () => {\r\n      window.removeEventListener('tabChange', handleTabChange as EventListener);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Fetch application statistics\r\n    const fetchApplicationStats = async () => {\r\n      try {\r\n        setStatsLoading(true);\r\n        const stats = await applicationService.getApplicationStats();\r\n        setApplicationStats(stats);\r\n      } catch (error) {\r\n        console.error('Failed to fetch application statistics:', error);\r\n      } finally {\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchApplicationStats();\r\n  }, []);\r\n\r\n  // Don't render anything until mounted to prevent hydration errors\r\n  if (!isMounted) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Tab content sections */}\r\n\r\n        {/* Overview Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'overview' ? '' : 'hidden'}`}>\r\n          {/* Page header */}\r\n          <div className=\"mb-6\">\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n              <div>\r\n                <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Dashboard Overview</h1>\r\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n                  Comprehensive view of your licenses, spectrum, users, and financial activities.\r\n                </p>\r\n              </div>\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto\">\r\n                <div className=\"flex space-x-3 place-content-start\">\r\n                  <div className=\"relative\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap w-full\"\r\n                    >\r\n                      <div className=\"w-4 h-4 flex items-center justify-center mr-2\">\r\n                        <i className=\"ri-calendar-line\"></i>\r\n                      </div>\r\n                      May 7, 2025\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"relative\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Key Metrics Section */}\r\n          <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden\">\r\n            <div className=\"p-6\">\r\n              <h3 className=\"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4\">Key Metrics</h3>\r\n              <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5\">\r\n                {/* License Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-key-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Licenses</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,482</div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-yellow-600\">57</span> expiring soon\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/licenses\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* User Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-user-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Users</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">3,649</div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-green-600\">247</span> new this month\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/users\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Financial Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-money-dollar-circle-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Revenue (<strong>MWK</strong>)</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">115.4M</div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-green-600\">1M</span> more this month\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/financial\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Compliance Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-shield-check-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Compliance (<strong>%</strong>)</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">92.1</div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-red-600\">8</span> new issues\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/spectrum\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Pending Applications Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-orange-100 dark:bg-orange-900 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-file-list-3-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Applications</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\" title={!isMounted || statsLoading ? 'Loading...' : `Submitted: ${applicationStats.submitted || 0}, Under Review: ${applicationStats.under_review || 0}, Evaluation: ${applicationStats.evaluation || 0}`}>\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded\"></div>\r\n                          ) : (\r\n                            getPendingApplicationsCount()\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-orange-600\">{!isMounted || statsLoading ? '...' : getNewSubmissionsCount()}</span> new submissions\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/applications\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View Applications\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Licenses Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'licenses' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">License Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage and monitor all telecommunications licenses.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-key-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Licenses</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,482</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,425</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Expiring Soon</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">57</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-close-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Expired</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">12</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent License Applications</h3>\r\n                <Link href=\"/dashboard/licenses\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                    <tr>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">License ID</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Company</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Type</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Status</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Expiry</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                    <tr>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100\">LIC-2023-0587</td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">Global Technologies Inc.</td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">Enterprise</td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\">Active</span>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">Dec 15, 2025</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100\">LIC-2022-1845</td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">Quantum Solutions Ltd.</td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">Professional</td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\">Expiring Soon</span>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">May 12, 2025</td>\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Users Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'users' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">User Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage system users and their access permissions.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-user-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">3,649</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-user-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Users</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">3,402</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-user-add-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">New This Month</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">247</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-shield-user-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Administrators</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">12</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent User Activity</h3>\r\n                <Link href=\"/dashboard/users\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-user-add-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">New user Alexander Mitchell registered</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">42 minutes ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-user-settings-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">User profile updated by Sarah Johnson</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">1 hour ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Transactions Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'transactions' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Financial Transactions</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor payments, invoices, and financial activities.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-money-dollar-circle-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Revenue (MWK)</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">115.4M</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-exchange-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">This Month (MWK)</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">8.7M</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">23</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-purple-100 dark:bg-purple-900 rounded-lg\">\r\n                  <i className=\"ri-file-list-line text-xl text-purple-600 dark:text-purple-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Transactions</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">4,892</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Transactions</h3>\r\n                <Link href=\"/dashboard/financial\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Payment of MWK 2.450M received from Acme Corp</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">3 hours ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-file-text-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Invoice INV-2025-0234 generated</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">5 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Spectrum Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'spectrum' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Spectrum Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor frequency allocations and spectrum usage.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-radio-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Allocations</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,248</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-signal-tower-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,156</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-bar-chart-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Utilization</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">78%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-alert-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Interference Issues</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">5</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Spectrum Bands Overview</h3>\r\n                <Link href=\"/dashboard/spectrum\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">VHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">30-300 MHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">85%</span>\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1\">\r\n                      <div className=\"bg-blue-600 dark:bg-blue-500 h-2 rounded-full w-[85%]\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">UHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">300-3000 MHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">72%</span>\r\n                    </div>\r\n                    <div className=\"progress-container\">\r\n                      <div className=\"progress-fill progress-green progress-bar-72\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">SHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">3-30 GHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">45%</span>\r\n                    </div>\r\n                    <div className=\"progress-container\">\r\n                      <div className=\"progress-fill progress-yellow progress-bar-45\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Compliance Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'compliance' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Compliance Overview</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor regulatory compliance and audit information.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-shield-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Compliance Rate</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">92.1%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-file-shield-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Audits Completed</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">156</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-alert-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Open Issues</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">8</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Reviews</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">23</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Compliance Activities</h3>\r\n                <Link href=\"/dashboard/audit\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Compliance audit completed for Global Tech Inc.</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">2 hours ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400\">\r\n                      <i className=\"ri-alert-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Non-compliance issue detected for Quantum Solutions</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">4 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oDAAoD;IACpD,MAAM,8BAA8B;QAClC,OAAO,CAAC,iBAAiB,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,YAAY,IAAI,CAAC,IAAI,CAAC,iBAAiB,UAAU,IAAI,CAAC;IACrH;IAEA,+CAA+C;IAC/C,MAAM,yBAAyB;QAC7B,OAAO,iBAAiB,SAAS,IAAI;IACvC;IAEA,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,aAAa;QACf;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,sDAAsD;YACtD,uCAAmC;;YAAM;YAEzC,qCAAqC;YACrC,MAAM;uDAAkB,CAAC;oBACvB,aAAa,MAAM,MAAM,CAAC,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC;uCAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;gBAC1C;;QACF;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,+BAA+B;YAC/B,MAAM;6DAAwB;oBAC5B,IAAI;wBACF,gBAAgB;wBAChB,MAAM,QAAQ,MAAM,wIAAA,CAAA,qBAAkB,CAAC,mBAAmB;wBAC1D,oBAAoB;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2CAA2C;oBAC3D,SAAU;wBACR,gBAAgB;oBAClB;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,kEAAkE;IAClE,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAIb,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCAEvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,MAAK;wDACL,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;4DACT;;;;;;;;;;;;8DAIV,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsE;;;;;;kDACpF,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFAA0D;;;;;;;;;;;kFAE3E,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAkB;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAKnD,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAsB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOvS,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFAA0D;;;;;;;;;;;kFAE3E,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAiB;;;;;;gFAAU;;;;;;;;;;;;;;;;;;;;;;;;kEAKnD,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOpS,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;;4EAAuD;0FAAS,6LAAC;0FAAO;;;;;;4EAAY;;;;;;;kFAClG,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFAA0D;;;;;;;;;;;kFAE3E,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAiB;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAKlD,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAuB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOxS,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;;4EAAuD;0FAAY,6LAAC;0FAAO;;;;;;4EAAU;;;;;;;kFACnG,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFAA0D;;;;;;;;;;;kFAE3E,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAe;;;;;;gFAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAK/C,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAsB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOvS,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA0D,OAAO,CAAC,aAAa,eAAe,eAAe,CAAC,WAAW,EAAE,iBAAiB,SAAS,IAAI,EAAE,gBAAgB,EAAE,iBAAiB,YAAY,IAAI,EAAE,cAAc,EAAE,iBAAiB,UAAU,IAAI,GAAG;sFAC9Q,CAAC,aAAa,6BACb,6LAAC;gFAAI,WAAU;;;;;uFAEf;;;;;;;;;;;kFAIN,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAmB,CAAC,aAAa,eAAe,QAAQ;;;;;;gFAAgC;;;;;;;;;;;;;;;;;;;;;;;;kEAKhH,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWzS,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCACvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAA0C;;;;;;;;;;;;kDAIvF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;;;;;;;;;;;;8DAGtH,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAmF;;;;;;8EACjG,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;8EACrF,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;8EACrF,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAK,WAAU;kFAA6H;;;;;;;;;;;8EAE/I,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;;;;;;;sEAEvF,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAmF;;;;;;8EACjG,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;8EACrF,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;8EACrF,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAK,WAAU;kFAAiI;;;;;;;;;;;8EAEnJ,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnG,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,UAAU,KAAK,UAAU;;sCACpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DAA0C;;;;;;;;;;;;kDAIpF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpE,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,iBAAiB,KAAK,UAAU;;sCAC3E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAA0C;;;;;;;;;;;;kDAIxF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpE,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCACvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAA0C;;;;;;;;;;;;kDAIvF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU7B,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,eAAe,KAAK,UAAU;;sCACzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DAA0C;;;;;;;;;;;;kDAIpF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5E;GAjuBwB;KAAA", "debugId": null}}]}