'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { validateSection } from '@/utils/formValidation';
import { applicationFormDataService } from '@/services/applicationFormDataService';
import { IndependentStepProps } from '../types';

interface CompanyProfileProps extends IndependentStepProps {}

const CompanyProfile: React.FC<CompanyProfileProps> = ({
  applicationId,
  licenseTypeId,
  licenseCategoryId,
  isEditMode = false,
  onStepComplete,
  onStepError,
  onNavigate
}) => {
  console.log('CompanyProfile component rendered with props:', {
    applicationId,
    licenseTypeId,
    licenseCategoryId,
    isEditMode
  });
  const [localData, setLocalData] = useState({
    company_name: '',
    business_registration_number: '',
    tax_number: '',
    company_type: '',
    incorporation_date: '',
    incorporation_place: '',
    company_email: '',
    company_phone: '',
    company_address: '',
    company_city: '',
    company_district: '',
    website: '',
    number_of_employees: '',
    annual_revenue: '',
    business_description: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load existing data when component mounts (for edit mode)
  useEffect(() => {
    const loadExistingData = async () => {
      // Add more robust validation for applicationId
      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {
        console.log('Loading existing company profile data for application:', applicationId);
        setIsLoading(true);
        try {
          const existingData = await applicationFormDataService.getFormSection(applicationId, 'companyProfile');
          if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {
            console.log('Loaded existing company profile data:', existingData.section_data);
            setLocalData(prev => ({ ...prev, ...existingData.section_data }));
          } else {
            console.log('No existing company profile data found for application:', applicationId);
          }
        } catch (error) {
          console.error('Error loading existing company profile data:', error);
          // Don't call onStepError as it would hide the form
          // Just log the error and continue with empty form
          console.log('Continuing with empty form due to load error');
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('Skipping company profile data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });
      }
    };

    loadExistingData();
  }, [applicationId, isEditMode]);

  // Handle local data changes
  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }

    // Clear save error when user starts making changes
    if (validationErrors.save) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));
    }
  }, [validationErrors]);

  // Validate form data
  const validateForm = () => {
    const validation = validateSection(localData, 'companyProfile');
    const errors = validation.errors || {};
    setValidationErrors(errors);
    return validation.isValid;
  };

  // Save data to backend
  const saveData = async () => {
    const validation = validateSection(localData, 'companyProfile');
    const errors = validation.errors || {};
    setValidationErrors(errors);

    if (!validation.isValid) {
      // Don't call onStepError as it hides the form
      // Instead, let the form show validation errors inline
      console.log('Validation failed:', errors);
      return false;
    }

    setIsSaving(true);
    try {
      if (applicationId && applicationId !== 'new') {
        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'companyProfile', localData);
        console.log('Company profile data saved for application:', applicationId);
      } else {
        console.warn('No application ID available for saving company profile');
      }

      setHasUnsavedChanges(false);
      onStepComplete?.('company-profile', localData);
      return true;
    } catch (error: any) {
      console.error('Error saving company profile data:', error);

      // Extract meaningful error message
      let errorMessage = 'Failed to save company profile information';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Show error in the form itself instead of hiding the form
      setValidationErrors(prev => ({ ...prev, save: errorMessage }));
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save button click
  const handleSave = async () => {
    await saveData();
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Company Profile
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide your company registration and business details.
        </p>
      </div>

      {/* Basic Company Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <TextInput
            label="Company Name"
            value={localData.company_name || ''}
            onChange={(e) => handleLocalChange('company_name', e.target.value)}
            required
            error={validationErrors.company_name}
          />
        </div>

        <TextInput
          label="Business Registration Number"
          value={localData.business_registration_number || ''}
          onChange={(e) => handleLocalChange('business_registration_number', e.target.value)}
          placeholder="**********"
          required
          error={validationErrors.business_registration_number}
        />

        <TextInput
          label="Tax Number (TPIN)"
          value={localData.tax_number || ''}
          onChange={(e) => handleLocalChange('tax_number', e.target.value)}
          placeholder="TP123456789"
          required
          error={validationErrors.tax_number}
        />

        <Select
          label="Company Type"
          value={localData.company_type || ''}
          onChange={(value) => handleLocalChange('company_type', value)}
          options={[
            { value: 'private_limited', label: 'Private Limited Company' },
            { value: 'public_limited', label: 'Public Limited Company' },
            { value: 'partnership', label: 'Partnership' },
            { value: 'sole_proprietorship', label: 'Sole Proprietorship' },
            { value: 'ngo', label: 'Non-Governmental Organization' },
            { value: 'other', label: 'Other' }
          ]}
          required
          error={validationErrors.company_type}
        />

        <TextInput
          label="Date of Incorporation"
          type="date"
          value={localData.incorporation_date || ''}
          onChange={(e) => handleLocalChange('incorporation_date', e.target.value)}
          required
          error={validationErrors.incorporation_date}
        />

        <TextInput
          label="Place of Incorporation"
          value={localData.incorporation_place || ''}
          onChange={(e) => handleLocalChange('incorporation_place', e.target.value)}
          required
          error={validationErrors.incorporation_place}
        />
      </div>

      {/* Contact Information */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Company Contact Information
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            label="Company Email"
            type="email"
            value={localData.company_email || ''}
            onChange={(e) => handleLocalChange('company_email', e.target.value)}
            required
            error={validationErrors.company_email}
          />

          <TextInput
            label="Company Phone"
            value={localData.company_phone || ''}
            onChange={(e) => handleLocalChange('company_phone', e.target.value)}
            placeholder="+265 1 234 567"
            required
            error={validationErrors.company_phone}
          />

          <TextInput
            label="Website"
            value={localData.website || ''}
            onChange={(e) => handleLocalChange('website', e.target.value)}
            placeholder="https://www.company.com"
            error={validationErrors.website}
          />

          <div className="md:col-span-2">
            <TextInput
              label="Company Address"
              value={localData.company_address || ''}
              onChange={(e) => handleLocalChange('company_address', e.target.value)}
              required
              error={validationErrors.company_address}
            />
          </div>

          <TextInput
            label="City"
            value={localData.company_city || ''}
            onChange={(e) => handleLocalChange('company_city', e.target.value)}
            required
            error={validationErrors.company_city}
          />

          <Select
            label="District"
            value={localData.company_district || ''}
            onChange={(value) => handleLocalChange('company_district', value)}
            options={[
              { value: 'Blantyre', label: 'Blantyre' },
              { value: 'Lilongwe', label: 'Lilongwe' },
              { value: 'Mzuzu', label: 'Mzuzu' },
              { value: 'Zomba', label: 'Zomba' },
              { value: 'Other', label: 'Other' }
            ]}
            required
            error={validationErrors.company_district}
          />
        </div>
      </div>

      {/* Business Details */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Business Details
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Number of Employees"
            value={localData.number_of_employees || ''}
            onChange={(value) => handleLocalChange('number_of_employees', value)}
            options={[
              { value: '1-10', label: '1-10 employees' },
              { value: '11-50', label: '11-50 employees' },
              { value: '51-100', label: '51-100 employees' },
              { value: '101-500', label: '101-500 employees' },
              { value: '500+', label: '500+ employees' }
            ]}
            required
            error={validationErrors.number_of_employees}
          />

          <Select
            label="Annual Revenue (MWK)"
            value={localData.annual_revenue || ''}
            onChange={(value) => handleLocalChange('annual_revenue', value)}
            options={[
              { value: 'under_1m', label: 'Under 1 Million' },
              { value: '1m_10m', label: '1-10 Million' },
              { value: '10m_50m', label: '10-50 Million' },
              { value: '50m_100m', label: '50-100 Million' },
              { value: 'over_100m', label: 'Over 100 Million' }
            ]}
            required
            error={validationErrors.annual_revenue}
          />

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Business Description *
            </label>
            <textarea
              value={localData.business_description || ''}
              onChange={(e) => handleLocalChange('business_description', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your business activities and operations..."
            />
            {validationErrors.business_description && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.business_description}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {validationErrors.save && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Saving Company Profile
              </h3>
              <p className="text-red-700 dark:text-red-300 text-sm mt-1">
                {validationErrors.save}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Company Profile
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CompanyProfile;
