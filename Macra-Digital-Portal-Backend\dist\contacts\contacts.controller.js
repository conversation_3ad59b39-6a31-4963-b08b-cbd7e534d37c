"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const contacts_service_1 = require("./contacts.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_contact_dto_1 = require("../dto/contact/create-contact.dto");
const update_contact_dto_1 = require("../dto/contact/update-contact.dto");
const contacts_entity_1 = require("../entities/contacts.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let ContactsController = class ContactsController {
    contactsService;
    constructor(contactsService) {
        this.contactsService = contactsService;
    }
    async create(createContactDto, req) {
        return this.contactsService.create(createContactDto, req.user.userId);
    }
    async findAll(query) {
        const result = await this.contactsService.findAll(query);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async search(searchTerm) {
        return this.contactsService.search(searchTerm);
    }
    async getContactsWithEmail() {
        return this.contactsService.getContactsWithEmail();
    }
    async getContactsWithoutEmail() {
        return this.contactsService.getContactsWithoutEmail();
    }
    async findByTelephone(telephone) {
        return this.contactsService.findByTelephone(telephone);
    }
    async findByEmail(email) {
        return this.contactsService.findByEmail(email);
    }
    async findOne(id) {
        return this.contactsService.findOne(id);
    }
    async update(id, updateContactDto, req) {
        return this.contactsService.update(id, updateContactDto, req.user.userId);
    }
    async remove(id) {
        await this.contactsService.remove(id);
        return { message: 'Contact deleted successfully' };
    }
};
exports.ContactsController = ContactsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new contact' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Contact created successfully',
        type: contacts_entity_1.Contacts,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Created new contact',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_contact_dto_1.CreateContactDto, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all contacts with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contacts retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Viewed contacts list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search contacts' }),
    (0, swagger_1.ApiQuery)({ name: 'q', description: 'Search term' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search results retrieved successfully',
        type: [contacts_entity_1.Contacts],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Searched contacts',
    }),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('with-email'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contacts with email addresses' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contacts with email retrieved successfully',
        type: [contacts_entity_1.Contacts],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Viewed contacts with email',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "getContactsWithEmail", null);
__decorate([
    (0, common_1.Get)('without-email'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contacts without email addresses' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contacts without email retrieved successfully',
        type: [contacts_entity_1.Contacts],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Viewed contacts without email',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "getContactsWithoutEmail", null);
__decorate([
    (0, common_1.Get)('by-telephone/:telephone'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contact by telephone' }),
    (0, swagger_1.ApiParam)({ name: 'telephone', description: 'Telephone number' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact retrieved successfully',
        type: contacts_entity_1.Contacts,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Viewed contact by telephone',
    }),
    __param(0, (0, common_1.Param)('telephone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findByTelephone", null);
__decorate([
    (0, common_1.Get)('by-email/:email'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contact by email' }),
    (0, swagger_1.ApiParam)({ name: 'email', description: 'Email address' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact retrieved successfully',
        type: contacts_entity_1.Contacts,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Viewed contact by email',
    }),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findByEmail", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contact by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact retrieved successfully',
        type: contacts_entity_1.Contacts,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Viewed contact details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update contact' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact updated successfully',
        type: contacts_entity_1.Contacts,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Updated contact',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_contact_dto_1.UpdateContactDto, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete contact' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Contact',
        description: 'Deleted contact',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "remove", null);
exports.ContactsController = ContactsController = __decorate([
    (0, swagger_1.ApiTags)('contacts'),
    (0, common_1.Controller)('contacts'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [contacts_service_1.ContactsService])
], ContactsController);
//# sourceMappingURL=contacts.controller.js.map