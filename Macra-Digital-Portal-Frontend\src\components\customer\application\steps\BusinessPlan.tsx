'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { validateSection } from '@/utils/formValidation';

interface BusinessPlanProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
}

const BusinessPlan: React.FC<BusinessPlanProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    executive_summary: '',
    market_analysis: '',
    financial_projections: '',
    revenue_model: '',
    investment_requirements: '',
    implementation_timeline: '',
    risk_analysis: '',
    success_metrics: '',
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync formData to localData only when formData changes and is different
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData((prev: any) => {
        const newData = { ...prev, ...formData };
        // Only update if there are actual changes to prevent infinite loops
        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);
        return hasChanges ? newData : prev;
      });
    }
  }, [formData]);

  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));

    // Call onChange with the field and value
    if (onChange) {
      onChange(field, value);
    }

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }
  }, [onChange, validationErrors]);

  const validateForm = () => {
    const validation = validateSection(localData, 'businessPlan');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(localData);
      console.log('Business plan saved');
    } catch (error) {
      console.error('Error saving business plan:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Business Plan
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide your business plan including financial projections and strategy.
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Executive Summary *
          </label>
          <textarea
            value={localData.executive_summary || ''}
            onChange={(e) => handleLocalChange('executive_summary', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Provide a summary of your business plan..."
          />
          {(validationErrors.executive_summary || errors.executive_summary) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.executive_summary || errors.executive_summary}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Market Analysis *
          </label>
          <textarea
            value={localData.market_analysis || ''}
            onChange={(e) => handleLocalChange('market_analysis', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your market analysis and opportunities..."
          />
          {(validationErrors.market_analysis || errors.market_analysis) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.market_analysis || errors.market_analysis}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Financial Projections *
          </label>
          <textarea
            value={localData.financial_projections || ''}
            onChange={(e) => handleLocalChange('financial_projections', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Provide your financial projections for the next 3-5 years..."
          />
          {(validationErrors.financial_projections || errors.financial_projections) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.financial_projections || errors.financial_projections}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Revenue Model *
          </label>
          <textarea
            value={localData.revenue_model || ''}
            onChange={(e) => handleLocalChange('revenue_model', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe how your business will generate revenue..."
          />
          {(validationErrors.revenue_model || errors.revenue_model) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.revenue_model || errors.revenue_model}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Investment Requirements *
          </label>
          <textarea
            value={localData.investment_requirements || ''}
            onChange={(e) => handleLocalChange('investment_requirements', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your funding and investment requirements..."
          />
          {(validationErrors.investment_requirements || errors.investment_requirements) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.investment_requirements || errors.investment_requirements}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Implementation Timeline *
          </label>
          <textarea
            value={localData.implementation_timeline || ''}
            onChange={(e) => handleLocalChange('implementation_timeline', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Provide your implementation timeline and milestones..."
          />
          {(validationErrors.implementation_timeline || errors.implementation_timeline) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.implementation_timeline || errors.implementation_timeline}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Risk Analysis *
          </label>
          <textarea
            value={localData.risk_analysis || ''}
            onChange={(e) => handleLocalChange('risk_analysis', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Identify and analyze potential risks and mitigation strategies..."
          />
          {(validationErrors.risk_analysis || errors.risk_analysis) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.risk_analysis || errors.risk_analysis}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Success Metrics *
          </label>
          <textarea
            value={localData.success_metrics || ''}
            onChange={(e) => handleLocalChange('success_metrics', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Define how you will measure success and performance..."
          />
          {(validationErrors.success_metrics || errors.success_metrics) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.success_metrics || errors.success_metrics}
            </p>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Business Plan
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default BusinessPlan;
