"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../entities/user.entity");
const role_entity_1 = require("../entities/role.entity");
const bcrypt = __importStar(require("bcryptjs"));
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
let UsersService = class UsersService {
    usersRepository;
    rolesRepository;
    constructor(usersRepository, rolesRepository) {
        this.usersRepository = usersRepository;
        this.rolesRepository = rolesRepository;
    }
    async findByEmail(email) {
        return this.usersRepository.findOne({
            where: { email },
            relations: ['roles'],
        });
    }
    async findById(id) {
        return this.usersRepository.findOne({
            where: { user_id: id },
            relations: ['roles'],
        });
    }
    async create(createUserDto) {
        const existingUser = await this.findByEmail(createUserDto.email);
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        let roles = [];
        if (createUserDto.role_ids && createUserDto.role_ids.length > 0) {
            const foundRoles = await this.rolesRepository.findByIds(createUserDto.role_ids);
            if (foundRoles.length !== createUserDto.role_ids.length) {
                throw new common_1.NotFoundException('One or more roles not found');
            }
            roles = foundRoles;
        }
        else {
            const defaultRole = await this.rolesRepository.findOne({
                where: { name: role_entity_1.RoleName.CUSTOMER },
            });
            if (defaultRole) {
                roles = [defaultRole];
            }
        }
        const hashedPassword = await bcrypt.hash(createUserDto.password, 12);
        const user = this.usersRepository.create({
            ...createUserDto,
            password: hashedPassword,
            status: createUserDto.status || user_entity_1.UserStatus.ACTIVE,
            roles,
        });
        return this.usersRepository.save(user);
    }
    async validatePassword(plainPassword, hashedPassword) {
        return bcrypt.compare(plainPassword, hashedPassword);
    }
    async updateLastLogin(userId) {
        await this.usersRepository.update(userId, {
            last_login: new Date(),
        });
    }
    async updatePassword(userId, newPassword) {
        const hashedPassword = await bcrypt.hash(newPassword, 12);
        await this.usersRepository.update(userId, {
            password: hashedPassword,
        });
    }
    async setTwoFactorCode(userId, code, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_code: code,
            two_factor_next_verification: expiresAt,
            two_factor_enabled: true,
            two_factor_temp: null,
        });
    }
    async setTempTwoFactorCode(userId, secret, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_temp: secret,
            two_factor_enabled: false,
            two_factor_next_verification: expiresAt,
        });
    }
    async clearTempTwoFactorCode(userId) {
        await this.usersRepository.update(userId, {
            two_factor_temp: null,
        });
    }
    async clearTwoFactorCode(userId) {
        await this.usersRepository.update(userId, {
            two_factor_code: null,
            two_factor_temp: null,
        });
    }
    async setTwoFactorCodeTempReset(userId, secret, code, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_code: code,
            two_factor_temp: secret,
            two_factor_next_verification: expiresAt,
        });
    }
    async disableTwoFactor(userId) {
        await this.usersRepository.update(userId, {
            two_factor_enabled: false,
            two_factor_code: null,
            two_factor_temp: null,
        });
    }
    async enableTwoFactor(userId, expiresAt) {
        await this.usersRepository.update(userId, {
            two_factor_enabled: true,
            two_factor_next_verification: expiresAt,
            two_factor_temp: null,
        });
    }
    async verifyEmail(userId) {
        await this.usersRepository.update(userId, {
            email_verified_at: new Date(),
        });
    }
    async updateStatus(userId, status) {
        await this.usersRepository.update(userId, { status });
    }
    async findAll(query) {
        console.log('UsersService: findAll called with query:', JSON.stringify(query, null, 2));
        const config = {
            sortableColumns: ['first_name', 'last_name', 'email', 'created_at', 'status'],
            searchableColumns: ['first_name', 'last_name', 'email'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            filterableColumns: {
                status: true,
            },
            relations: ['roles'],
        };
        console.log('UsersService: Using config:', JSON.stringify(config, null, 2));
        const result = await (0, nestjs_paginate_1.paginate)(query, this.usersRepository, config);
        console.log('UsersService: Raw pagination result:', JSON.stringify(result, null, 2));
        const transformedResult = pagination_interface_1.PaginationTransformer.transform(result);
        console.log('UsersService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));
        return transformedResult;
    }
    async update(userId, updateUserDto) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        let roles;
        if (updateUserDto.role_ids && updateUserDto.role_ids.length > 0) {
            const foundRoles = await this.rolesRepository.findByIds(updateUserDto.role_ids);
            if (foundRoles.length !== updateUserDto.role_ids.length) {
                throw new common_1.NotFoundException('One or more roles not found');
            }
            roles = foundRoles;
        }
        let hashedPassword;
        if (updateUserDto.password) {
            hashedPassword = await bcrypt.hash(updateUserDto.password, 12);
        }
        const { role_ids, ...updateDataWithoutRoles } = updateUserDto;
        const updateData = {
            ...updateDataWithoutRoles,
            ...(hashedPassword && { password: hashedPassword }),
        };
        const { password, two_factor_code, two_factor_next_verification, ...safeUpdateData } = updateData;
        Object.assign(user, safeUpdateData);
        if (roles) {
            user.roles = roles;
        }
        await this.usersRepository.save(user);
        const updatedUser = await this.findById(userId);
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        return updatedUser;
    }
    async delete(userId) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.usersRepository.softDelete(userId);
    }
    async updateProfile(userId, updateProfileDto) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (updateProfileDto.email && updateProfileDto.email !== user.email) {
            const existingUser = await this.findByEmail(updateProfileDto.email);
            if (existingUser) {
                throw new common_1.ConflictException('Email is already taken');
            }
        }
        await this.usersRepository.update(userId, updateProfileDto);
        const updatedUser = await this.findById(userId);
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        return updatedUser;
    }
    async changePassword(userId, changePasswordDto) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const isCurrentPasswordValid = await this.validatePassword(changePasswordDto.current_password, user.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.BadRequestException('Current password is incorrect');
        }
        if (changePasswordDto.new_password !== changePasswordDto.confirm_password) {
            throw new common_1.BadRequestException('New password and confirmation do not match');
        }
        await this.updatePassword(userId, changePasswordDto.new_password);
        return { message: 'Password changed successfully' };
    }
    async uploadAvatar(userId, file) {
        console.log('UsersService: uploadAvatar called', { userId, file: file ? file.originalname : 'no file' });
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (!file) {
            throw new common_1.BadRequestException('No file uploaded');
        }
        console.log('UsersService: File details', {
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            filename: file.filename,
            path: file.path
        });
        try {
            const base64Image = `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;
            console.log('UsersService: Updating user with base64 image');
            await this.usersRepository.update(userId, {
                profile_image: base64Image,
            });
            const updatedUser = await this.findById(userId);
            if (!updatedUser) {
                throw new common_1.NotFoundException('User not found after update');
            }
            console.log('UsersService: Avatar upload successful');
            return updatedUser;
        }
        catch (error) {
            console.error('UsersService: Error uploading avatar', error);
            throw new common_1.BadRequestException('Failed to upload avatar');
        }
    }
    async removeAvatar(userId) {
        const user = await this.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.usersRepository.update(userId, {
            profile_image: null,
        });
        const updatedUser = await this.findById(userId);
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        return updatedUser;
    }
    async mailUser(userEmail) {
        const user = await this.findByEmail(userEmail);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return { message: 'Email sent! Please check inbox' };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], UsersService);
//# sourceMappingURL=users.service.js.map