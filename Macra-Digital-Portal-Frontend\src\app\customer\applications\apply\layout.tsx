'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { getLicenseTypeStepConfig, StepConfig } from '@/config/licenseTypeStepConfig';
import { CustomerApiService } from '@/lib/customer-api';

interface ApplyLayoutProps {
  children: React.ReactNode;
}

const ApplyLayout: React.FC<ApplyLayoutProps> = ({ children }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // Create customer API service instance
  const customerApi = new CustomerApiService();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State
  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);
  const [loading, setLoading] = useState(true);

  // Get current step from pathname
  const getCurrentStepIndex = () => {
    const pathSegments = pathname.split('/');
    const currentStepId = pathSegments[pathSegments.length - 1];
    return applicationSteps.findIndex(step => step.id === currentStepId);
  };

  const currentStepIndex = getCurrentStepIndex();

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        if (!licenseCategoryId) {
          setLoading(false);
          return;
        }

        // Load license category
        const category = await customerApi.getLicenseCategory(licenseCategoryId);
        const licenseType = await customerApi.getLicenseType(category.license_type_id);

        if (!category || !licenseType) {
          setLoading(false);
          return;
        }

        const config = getLicenseTypeStepConfig(licenseType.code);
        if (config) {
          setApplicationSteps(config.steps);
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Error loading data:', err);
        setLoading(false);
      }
    };

    loadData();
  }, [licenseCategoryId, applicationId]);

  // Navigation handlers
  const handleStepClick = (stepIndex: number) => {
    // Prevent navigation to future steps if not editing an existing application
    if (!applicationId && stepIndex > currentStepIndex) {
      return;
    }

    const step = applicationSteps[stepIndex];
    const params = new URLSearchParams();
    params.set('license_category_id', licenseCategoryId!);
    if (applicationId) {
      params.set('application_id', applicationId);
    }
    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);
  };

  // Don't render steps if still loading or no data
  if (loading || !applicationSteps.length) {
    return <>{children}</>;
  }

  return (
    <>
      {/* Progress Steps - Vertical Layout for Scalability */}
      <div className="mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
            Application Progress ({currentStepIndex + 1} of {applicationSteps.length})
          </h3>
          <div className="space-y-2">
            {applicationSteps.map((step, index) => {
              const isAccessible = applicationId || index <= currentStepIndex;
              return (
                <div
                  key={step.id}
                  className={`flex items-center p-2 rounded-md transition-colors ${
                    isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'
                  } ${
                    index === currentStepIndex
                      ? 'bg-primary/10 border border-primary/20'
                      : index < currentStepIndex
                      ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                      : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'
                  }`}
                  onClick={() => isAccessible && handleStepClick(index)}
                >
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${
                      index === currentStepIndex
                        ? 'bg-primary text-white'
                        : index < currentStepIndex
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
                    }`}
                  >
                    {index < currentStepIndex ? (
                      <i className="ri-check-line"></i>
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      index === currentStepIndex
                        ? 'text-primary'
                        : index < currentStepIndex
                        ? 'text-green-700 dark:text-green-300'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {step.name}
                    </div>
                    {step.description && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {step.description}
                      </div>
                    )}
                  </div>
                  {step.required && (
                    <span className="text-xs text-red-500 ml-2">*</span>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Page Content */}
      {children}
    </>
  );
};

export default ApplyLayout;
