"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseTypesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const license_types_entity_1 = require("../entities/license-types.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
let LicenseTypesService = class LicenseTypesService {
    licenseTypesRepository;
    constructor(licenseTypesRepository) {
        this.licenseTypesRepository = licenseTypesRepository;
    }
    async findAll(query) {
        const config = {
            sortableColumns: ['name', 'validity', 'created_at'],
            searchableColumns: ['name', 'description'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            relations: ['creator', 'updater'],
        };
        const result = await (0, nestjs_paginate_1.paginate)(query, this.licenseTypesRepository, config);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async findOne(id) {
        const licenseType = await this.licenseTypesRepository.findOne({
            where: { license_type_id: id },
            relations: ['creator', 'updater'],
        });
        if (!licenseType) {
            throw new common_1.NotFoundException('License type not found');
        }
        return licenseType;
    }
    async create(createLicenseTypeDto, userId) {
        const existingLicenseType = await this.licenseTypesRepository.findOne({
            where: { name: createLicenseTypeDto.name },
        });
        if (existingLicenseType) {
            throw new common_1.ConflictException('License type with this name already exists');
        }
        const licenseType = this.licenseTypesRepository.create({
            ...createLicenseTypeDto,
            created_by: userId,
        });
        return this.licenseTypesRepository.save(licenseType);
    }
    async update(id, updateLicenseTypeDto, userId) {
        const licenseType = await this.findOne(id);
        if (updateLicenseTypeDto.name && updateLicenseTypeDto.name !== licenseType.name) {
            const existingLicenseType = await this.licenseTypesRepository.findOne({
                where: { name: updateLicenseTypeDto.name },
            });
            if (existingLicenseType) {
                throw new common_1.ConflictException('License type with this name already exists');
            }
        }
        await this.licenseTypesRepository.update(id, {
            ...updateLicenseTypeDto,
            updated_by: userId,
        });
        return this.findOne(id);
    }
    async remove(id) {
        const licenseType = await this.findOne(id);
        await this.licenseTypesRepository.softDelete(id);
    }
};
exports.LicenseTypesService = LicenseTypesService;
exports.LicenseTypesService = LicenseTypesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(license_types_entity_1.LicenseTypes)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LicenseTypesService);
//# sourceMappingURL=license-types.service.js.map