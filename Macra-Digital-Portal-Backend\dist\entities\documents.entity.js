"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Documents = exports.DocumentType = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applications_entity_1 = require("./applications.entity");
var DocumentType;
(function (DocumentType) {
    DocumentType["CERTIFICATE_INCORPORATION"] = "certificate_incorporation";
    DocumentType["MEMORANDUM_ASSOCIATION"] = "memorandum_association";
    DocumentType["SHAREHOLDING_STRUCTURE"] = "shareholding_structure";
    DocumentType["BUSINESS_PLAN"] = "business_plan";
    DocumentType["FINANCIAL_STATEMENTS"] = "financial_statements";
    DocumentType["TECHNICAL_PROPOSAL"] = "technical_proposal";
    DocumentType["COVERAGE_PLAN"] = "coverage_plan";
    DocumentType["NETWORK_DIAGRAM"] = "network_diagram";
    DocumentType["EQUIPMENT_SPECIFICATIONS"] = "equipment_specifications";
    DocumentType["INSURANCE_CERTIFICATE"] = "insurance_certificate";
    DocumentType["TAX_CLEARANCE"] = "tax_clearance";
    DocumentType["AUDITED_ACCOUNTS"] = "audited_accounts";
    DocumentType["BANK_STATEMENT"] = "bank_statement";
    DocumentType["CV_DOCUMENT"] = "cv_document";
    DocumentType["OTHER"] = "other";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
let Documents = class Documents {
    document_id;
    application_id;
    document_type;
    file_name;
    entity_type;
    entity_id;
    file_path;
    file_size;
    mime_type;
    is_required;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    application;
    creator;
    updater;
    generateId() {
        if (!this.document_id) {
            this.document_id = (0, uuid_1.v4)();
        }
    }
};
exports.Documents = Documents;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], Documents.prototype, "document_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Documents.prototype, "application_id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DocumentType,
    }),
    __metadata("design:type", String)
], Documents.prototype, "document_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], Documents.prototype, "file_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], Documents.prototype, "entity_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Documents.prototype, "entity_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Documents.prototype, "file_path", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], Documents.prototype, "file_size", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Documents.prototype, "mime_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Documents.prototype, "is_required", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Documents.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Documents.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Documents.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Documents.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Documents.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applications_entity_1.Applications, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", applications_entity_1.Applications)
], Documents.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], Documents.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], Documents.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Documents.prototype, "generateId", null);
exports.Documents = Documents = __decorate([
    (0, typeorm_1.Entity)('documents')
], Documents);
//# sourceMappingURL=documents.entity.js.map