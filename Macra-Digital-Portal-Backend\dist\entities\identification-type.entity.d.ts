import { User } from './user.entity';
import { UserIdentification } from './user-identification.entity';
export declare class IdentificationType {
    identification_type_id: string;
    name: string;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    creator?: User;
    updater?: User;
    user_identifications: UserIdentification[];
    generateId(): void;
}
