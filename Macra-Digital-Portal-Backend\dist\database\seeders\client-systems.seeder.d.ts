import { Repository } from 'typeorm';
import { ClientSystems } from '../../entities/client-systems.entity';
import { User } from '../../entities/user.entity';
export declare class ClientSystemsSeeder {
    private readonly clientSystemsRepository;
    private readonly userRepository;
    constructor(clientSystemsRepository: Repository<ClientSystems>, userRepository: Repository<User>);
    seed(): Promise<void>;
    reset(): Promise<void>;
}
