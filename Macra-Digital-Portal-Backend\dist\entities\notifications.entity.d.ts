import { User } from './user.entity';
export declare enum NotificationType {
    APPLICATION_STATUS = "application_status",
    EVALUATION_ASSIGNED = "evaluation_assigned",
    PAYMENT_DUE = "payment_due",
    LICENSE_EXPIRY = "license_expiry",
    SYSTEM_ALERT = "system_alert"
}
export declare enum NotificationPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class Notifications {
    notification_id: string;
    user_id: string;
    type: NotificationType;
    title: string;
    message: string;
    is_read: boolean;
    priority: NotificationPriority;
    related_entity_type?: string;
    related_entity_id?: string;
    action_url?: string;
    expires_at?: Date;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    read_at?: Date;
    user: User;
    creator: User;
    updater?: User;
    generateId(): void;
}
