'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { validateSection } from '@/utils/formValidation';

interface ProfessionalServicesProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
}

const ProfessionalServices: React.FC<ProfessionalServicesProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    consultants: '',
    service_providers: '',
    technical_support: '',
    maintenance_arrangements: '',
    professional_partnerships: '',
    outsourced_services: '',
    quality_assurance: '',
    training_programs: '',
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync with parent form data
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData(prev => ({ ...prev, ...formData }));
    }
  }, [formData]);

  // Handle local data changes
  const handleLocalChange = (field: string, value: any) => {
    setLocalData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validation function
  const validateForm = () => {
    const errors = validateSection('professionalServices', localData);
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle save
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(localData);
      console.log('Professional services data saved');
    } catch (error) {
      console.error('Error saving professional services data:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Auto-save on blur
  const handleBlur = (field: string) => {
    if (localData[field] && !validationErrors[field]) {
      onChange(field, localData[field]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Professional Services
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide information about external consultants, service providers, and support arrangements.
        </p>
      </div>

      {/* External Consultants */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          External Consultants *
        </label>
        <textarea
          value={localData.consultants || ''}
          onChange={(e) => handleLocalChange('consultants', e.target.value)}
          onBlur={() => handleBlur('consultants')}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="List any external consultants, their areas of expertise, and their role in your operations..."
        />
        {(validationErrors.consultants || errors.consultants) && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.consultants || errors.consultants}
          </p>
        )}
      </div>

      {/* Service Providers */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Service Providers *
        </label>
        <textarea
          value={localData.service_providers || ''}
          onChange={(e) => handleLocalChange('service_providers', e.target.value)}
          onBlur={() => handleBlur('service_providers')}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe your service providers, their services, and contractual arrangements..."
        />
        {(validationErrors.service_providers || errors.service_providers) && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.service_providers || errors.service_providers}
          </p>
        )}
      </div>

      {/* Technical Support */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Technical Support Arrangements *
        </label>
        <textarea
          value={localData.technical_support || ''}
          onChange={(e) => handleLocalChange('technical_support', e.target.value)}
          onBlur={() => handleBlur('technical_support')}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe your technical support arrangements, including internal and external resources..."
        />
        {(validationErrors.technical_support || errors.technical_support) && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.technical_support || errors.technical_support}
          </p>
        )}
      </div>

      {/* Maintenance Arrangements */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Maintenance Arrangements *
        </label>
        <textarea
          value={localData.maintenance_arrangements || ''}
          onChange={(e) => handleLocalChange('maintenance_arrangements', e.target.value)}
          onBlur={() => handleBlur('maintenance_arrangements')}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Detail your maintenance arrangements for equipment, systems, and infrastructure..."
        />
        {(validationErrors.maintenance_arrangements || errors.maintenance_arrangements) && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.maintenance_arrangements || errors.maintenance_arrangements}
          </p>
        )}
      </div>

      {/* Professional Partnerships */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Professional Partnerships
        </label>
        <textarea
          value={localData.professional_partnerships || ''}
          onChange={(e) => handleLocalChange('professional_partnerships', e.target.value)}
          onBlur={() => handleBlur('professional_partnerships')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe any professional partnerships or strategic alliances..."
        />
      </div>

      {/* Outsourced Services */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Outsourced Services
        </label>
        <textarea
          value={localData.outsourced_services || ''}
          onChange={(e) => handleLocalChange('outsourced_services', e.target.value)}
          onBlur={() => handleBlur('outsourced_services')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="List any services that will be outsourced and the rationale..."
        />
      </div>

      {/* Quality Assurance */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Quality Assurance Measures
        </label>
        <textarea
          value={localData.quality_assurance || ''}
          onChange={(e) => handleLocalChange('quality_assurance', e.target.value)}
          onBlur={() => handleBlur('quality_assurance')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe your quality assurance processes and standards..."
        />
      </div>

      {/* Training Programs */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Training Programs
        </label>
        <textarea
          value={localData.training_programs || ''}
          onChange={(e) => handleLocalChange('training_programs', e.target.value)}
          onBlur={() => handleBlur('training_programs')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe training programs for staff and ongoing professional development..."
        />
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-4">
        <button
          type="button"
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Professional Services
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProfessionalServices;
