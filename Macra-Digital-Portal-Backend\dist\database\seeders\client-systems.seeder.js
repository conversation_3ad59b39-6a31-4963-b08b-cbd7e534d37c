"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientSystemsSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const client_systems_entity_1 = require("../../entities/client-systems.entity");
const user_entity_1 = require("../../entities/user.entity");
let ClientSystemsSeeder = class ClientSystemsSeeder {
    clientSystemsRepository;
    userRepository;
    constructor(clientSystemsRepository, userRepository) {
        this.clientSystemsRepository = clientSystemsRepository;
        this.userRepository = userRepository;
    }
    async seed() {
        console.log('🔧 Seeding client systems...');
        const adminUser = await this.userRepository.findOne({
            where: { email: '<EMAIL>' },
        });
        if (!adminUser) {
            console.log('❌ Admin user not found. Skipping client systems seeding.');
            return;
        }
        const clientSystems = [
            {
                name: 'MACRA Digital Portal Web',
                system_code: 'MACRA_WEB_PORTAL',
                description: 'Main web portal for license applications and management',
                system_type: client_systems_entity_1.ClientSystemType.WEB_APPLICATION,
                status: client_systems_entity_1.ClientSystemStatus.ACTIVE,
                api_endpoint: 'https://portal.macra.gov.zm/api',
                callback_url: 'https://portal.macra.gov.zm/callback',
                contact_email: '<EMAIL>',
                contact_phone: '+260211123456',
                organization: 'MACRA IT Department',
                access_permissions: JSON.stringify({
                    read: true,
                    write: true,
                    admin: true,
                    license_management: true,
                    user_management: true,
                }),
                version: '1.0.0',
                notes: 'Primary web application for customer and staff portals',
                created_by: adminUser.user_id,
            },
            {
                name: 'MACRA Mobile App',
                system_code: 'MACRA_MOBILE_APP',
                description: 'Mobile application for license applications and status tracking',
                system_type: client_systems_entity_1.ClientSystemType.MOBILE_APP,
                status: client_systems_entity_1.ClientSystemStatus.ACTIVE,
                api_endpoint: 'https://mobile-api.macra.gov.zm/v1',
                callback_url: 'https://mobile.macra.gov.zm/callback',
                contact_email: '<EMAIL>',
                contact_phone: '+260211123457',
                organization: 'MACRA Mobile Team',
                access_permissions: JSON.stringify({
                    read: true,
                    write: true,
                    admin: false,
                    license_management: true,
                    user_management: false,
                }),
                version: '2.1.0',
                notes: 'Mobile application for iOS and Android platforms',
                created_by: adminUser.user_id,
            },
            {
                name: 'Third Party Integration API',
                system_code: 'THIRD_PARTY_API',
                description: 'API for third-party system integrations',
                system_type: client_systems_entity_1.ClientSystemType.API_CLIENT,
                status: client_systems_entity_1.ClientSystemStatus.ACTIVE,
                api_endpoint: 'https://api.macra.gov.zm/v1',
                contact_email: '<EMAIL>',
                contact_phone: '+260211123458',
                organization: 'MACRA Integration Team',
                access_permissions: JSON.stringify({
                    read: true,
                    write: false,
                    admin: false,
                    license_management: true,
                    user_management: false,
                }),
                version: '1.2.0',
                notes: 'API for external system integrations and data exchange',
                created_by: adminUser.user_id,
            },
            {
                name: 'Internal Management System',
                system_code: 'INTERNAL_MGMT_SYS',
                description: 'Internal system for staff and administrative functions',
                system_type: client_systems_entity_1.ClientSystemType.INTERNAL_SYSTEM,
                status: client_systems_entity_1.ClientSystemStatus.ACTIVE,
                api_endpoint: 'https://internal.macra.gov.zm/api',
                callback_url: 'https://internal.macra.gov.zm/callback',
                contact_email: '<EMAIL>',
                contact_phone: '+260211123459',
                organization: 'MACRA Internal Operations',
                access_permissions: JSON.stringify({
                    read: true,
                    write: true,
                    admin: true,
                    license_management: true,
                    user_management: true,
                    audit_trail: true,
                    system_settings: true,
                }),
                version: '1.0.0',
                notes: 'Internal system for staff operations and administration',
                created_by: adminUser.user_id,
            },
            {
                name: 'Legacy System Bridge',
                system_code: 'LEGACY_BRIDGE',
                description: 'Bridge system for legacy system integration',
                system_type: client_systems_entity_1.ClientSystemType.THIRD_PARTY_INTEGRATION,
                status: client_systems_entity_1.ClientSystemStatus.MAINTENANCE,
                api_endpoint: 'https://legacy-bridge.macra.gov.zm/api',
                contact_email: '<EMAIL>',
                contact_phone: '+260211123460',
                organization: 'MACRA Legacy Systems Team',
                access_permissions: JSON.stringify({
                    read: true,
                    write: false,
                    admin: false,
                    license_management: false,
                    user_management: false,
                }),
                version: '0.9.0',
                notes: 'Bridge system for legacy data migration and integration',
                created_by: adminUser.user_id,
            },
        ];
        for (const systemData of clientSystems) {
            const existingSystem = await this.clientSystemsRepository.findOne({
                where: { system_code: systemData.system_code },
            });
            if (!existingSystem) {
                const clientSystem = this.clientSystemsRepository.create(systemData);
                await this.clientSystemsRepository.save(clientSystem);
                console.log(`✅ Created client system: ${systemData.name}`);
            }
            else {
                console.log(`⏭️  Client system already exists: ${systemData.name}`);
            }
        }
        console.log('✅ Client systems seeding completed');
    }
    async reset() {
        console.log('🗑️  Resetting client systems...');
        const allSystems = await this.clientSystemsRepository.find();
        if (allSystems.length > 0) {
            await this.clientSystemsRepository.remove(allSystems);
            console.log(`✅ Deleted ${allSystems.length} client systems`);
        }
        else {
            console.log('✅ No client systems to delete');
        }
        console.log('✅ Client systems reset completed');
    }
};
exports.ClientSystemsSeeder = ClientSystemsSeeder;
exports.ClientSystemsSeeder = ClientSystemsSeeder = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(client_systems_entity_1.ClientSystems)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], ClientSystemsSeeder);
//# sourceMappingURL=client-systems.seeder.js.map