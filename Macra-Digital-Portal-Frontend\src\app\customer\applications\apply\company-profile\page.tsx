'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { CustomerApiService } from '@/lib/customer-api';
import { TextInput, Select, TextArea } from '@/components/forms';
import { applicationService } from '@/services/applicationService';
import { applicationProgressService } from '@/services/applicationProgressService';
// Note: validateSection function will be implemented or imported as needed
import { LicenseType } from '@/services/licenseTypeService';
import {
  getLicenseTypeStepConfig,
  getStepByRoute,
  getStepIndex,
  getTotalSteps,
  getNextStep,
  getPreviousStep
} from '@/config/licenseTypeStepConfig';

const CompanyProfilePage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // Form data state - matching CompanyProfile component structure
  const [formData, setFormData] = useState({
    company_name: '',
    business_registration_number: '',
    tax_number: '',
    company_type: '',
    incorporation_date: '',
    incorporation_place: '',
    company_email: '',
    company_phone: '',
    company_address: '',
    company_city: '',
    company_district: '',
    website: '',
    number_of_employees: '',
    annual_revenue: '',
    business_description: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [licenseCategory, setLicenseCategory] = useState<any>(null);
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);
  const [applicationSteps, setApplicationSteps] = useState<any[]>([]);

  // Form handling functions
  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  // Load license category and application data
  useEffect(() => {
    const loadData = async () => {
      if (!licenseCategoryId) {
        setError('License category ID is required');
        setIsLoading(false);
        return;
      }

      if (!applicationId) {
        setError('Application ID is required for this step');
        setIsLoading(false);
        return;
      }

      try {
        const customerApi = new CustomerApiService();

        // Load license category
        const category = await customerApi.getLicenseCategory(licenseCategoryId);
        setLicenseCategory(category);

        // Load license type
        const licenseType = await customerApi.getLicenseType(category.license_type_id);
        setLicenseType(licenseType);

        // Get step configuration
        const config = getLicenseTypeStepConfig(licenseType.code);
        setApplicationSteps(config.steps);

        // Load existing application data if in edit mode
        if (applicationId && applicationId !== 'new') {
          const application = await applicationService.getApplication(applicationId);
          if (application) {
            // Load existing company profile data if available
            // This would typically come from a separate company profile service
            // For now, we'll leave the form empty for new data entry
          }
        }

      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    if (!authLoading && isAuthenticated) {
      loadData();
    }
  }, [licenseCategoryId, applicationId, authLoading, isAuthenticated]);

  // Save form data
  const saveFormData = async () => {
    if (!applicationId || applicationId === 'new') {
      setError('Application ID is required to save company profile data');
      return false;
    }

    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      if (!formData.company_name.trim()) errors.company_name = 'Company name is required';
      if (!formData.business_registration_number.trim()) errors.business_registration_number = 'Business registration number is required';
      if (!formData.tax_number.trim()) errors.tax_number = 'Tax number is required';
      if (!formData.company_type.trim()) errors.company_type = 'Company type is required';
      if (!formData.incorporation_date.trim()) errors.incorporation_date = 'Date of incorporation is required';
      if (!formData.incorporation_place.trim()) errors.incorporation_place = 'Place of incorporation is required';
      if (!formData.company_email.trim()) errors.company_email = 'Company email is required';
      if (!formData.company_phone.trim()) errors.company_phone = 'Company phone is required';
      if (!formData.company_address.trim()) errors.company_address = 'Company address is required';
      if (!formData.company_city.trim()) errors.company_city = 'City is required';
      if (!formData.company_district.trim()) errors.company_district = 'District is required';
      if (!formData.business_description.trim()) errors.business_description = 'Business description is required';

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSaving(false);
        return false;
      }

      // Save company profile data to application
      // Note: For now, we'll save to a generic field or create a separate company profile service
      // This is a placeholder - the actual implementation may need a different approach
      await applicationService.updateApplication(applicationId, {
        // Save as JSON in a text field or use a separate service
      });

      // Mark step as completed
      await applicationProgressService.markStepCompleted(applicationId, 'company-profile');

      setHasUnsavedChanges(false);
      return true;
    } catch (error: any) {
      console.error('Error saving company profile data:', error);
      setValidationErrors(prev => ({
        ...prev,
        save: error.response?.data?.message || error.message || 'Failed to save company profile data'
      }));
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Handle next button click
  const handleNext = async () => {
    const saved = await saveFormData();
    if (saved && licenseType && licenseType.code) {
      const config = getLicenseTypeStepConfig(licenseType.code);
      const currentStepIndex = config?.steps.findIndex(step => step.id === 'company-profile') || 0;
      const nextStep = config?.steps[currentStepIndex + 1];

      if (nextStep) {
        router.push(`/customer/applications/apply/${nextStep.route}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
      }
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    if (licenseType && licenseType.code) {
      const config = getLicenseTypeStepConfig(licenseType.code);
      const currentStepIndex = config?.steps.findIndex(step => step.id === 'company-profile') || 0;
      const previousStep = config?.steps[currentStepIndex - 1];

      if (previousStep) {
        router.push(`/customer/applications/apply/${previousStep.route}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
      }
    }
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading company profile form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Form</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => router.back()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {licenseType?.name} License Application
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Company Profile Information
          </p>
          {applicationId && (
            <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <i className="ri-edit-line mr-1"></i>
                Editing application: {applicationId}
              </p>
            </div>
          )}
        </div>

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Company Profile
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Provide your company registration and business details.
              </p>
            </div>

            {/* Validation Errors */}
            {validationErrors.save && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
                  <p className="text-red-700 dark:text-red-300 text-sm">{validationErrors.save}</p>
                </div>
              </div>
            )}

            {/* Basic Company Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <TextInput
                  label="Company Name"
                  value={formData.company_name}
                  onChange={(e) => handleFormChange('company_name', e.target.value)}
                  required
                  error={validationErrors.company_name}
                />
              </div>

              <TextInput
                label="Business Registration Number"
                value={formData.business_registration_number}
                onChange={(e) => handleFormChange('business_registration_number', e.target.value)}
                placeholder="**********"
                required
                error={validationErrors.business_registration_number}
              />

              <TextInput
                label="Tax Number (TPIN)"
                value={formData.tax_number}
                onChange={(e) => handleFormChange('tax_number', e.target.value)}
                placeholder="TP123456789"
                required
                error={validationErrors.tax_number}
              />

              <Select
                label="Company Type"
                value={formData.company_type}
                onChange={(e) => handleFormChange('company_type', e.target.value)}
                options={[
                  { value: 'private_limited', label: 'Private Limited Company' },
                  { value: 'public_limited', label: 'Public Limited Company' },
                  { value: 'partnership', label: 'Partnership' },
                  { value: 'sole_proprietorship', label: 'Sole Proprietorship' },
                  { value: 'ngo', label: 'Non-Governmental Organization' },
                  { value: 'other', label: 'Other' }
                ]}
                required
                error={validationErrors.company_type}
              />

              <TextInput
                type="date"
                label="Date of Incorporation"
                value={formData.incorporation_date}
                onChange={(e) => handleFormChange('incorporation_date', e.target.value)}
                required
                error={validationErrors.incorporation_date}
              />

              <TextInput
                label="Place of Incorporation"
                value={formData.incorporation_place}
                onChange={(e) => handleFormChange('incorporation_place', e.target.value)}
                placeholder="Enter place of incorporation"
                required
                error={validationErrors.incorporation_place}
              />

              <TextInput
                label="Website"
                value={formData.website}
                onChange={(e) => handleFormChange('website', e.target.value)}
                placeholder="https://www.company.com"
                error={validationErrors.website}
              />
            </div>

            {/* Contact Information */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Contact Information
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextInput
                  label="Company Email"
                  type="email"
                  value={formData.company_email}
                  onChange={(e) => handleFormChange('company_email', e.target.value)}
                  required
                  error={validationErrors.company_email}
                />

                <TextInput
                  label="Company Phone"
                  value={formData.company_phone}
                  onChange={(e) => handleFormChange('company_phone', e.target.value)}
                  placeholder="+265 1 234 567"
                  required
                  error={validationErrors.company_phone}
                />
              </div>
            </div>

            {/* Address Information */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Address Information
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextInput
                  label="Company Address"
                  value={formData.company_address}
                  onChange={(e) => handleFormChange('company_address', e.target.value)}
                  placeholder="Enter company address"
                  required
                  error={validationErrors.company_address}
                />

                <TextInput
                  label="City"
                  value={formData.company_city}
                  onChange={(e) => handleFormChange('company_city', e.target.value)}
                  placeholder="Enter city"
                  required
                  error={validationErrors.company_city}
                />

                <TextInput
                  label="District"
                  value={formData.company_district}
                  onChange={(e) => handleFormChange('company_district', e.target.value)}
                  placeholder="Enter district"
                  required
                  error={validationErrors.company_district}
                />
              </div>
            </div>

            {/* Business Information */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Business Information
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextInput
                  label="Number of Employees"
                  type="number"
                  value={formData.number_of_employees}
                  onChange={(e) => handleFormChange('number_of_employees', e.target.value)}
                  placeholder="Enter number of employees"
                  error={validationErrors.number_of_employees}
                />

                <TextInput
                  label="Annual Revenue (MWK)"
                  value={formData.annual_revenue}
                  onChange={(e) => handleFormChange('annual_revenue', e.target.value)}
                  placeholder="Enter annual revenue"
                  error={validationErrors.annual_revenue}
                />
              </div>

              <div className="mt-6">
                <TextArea
                  label="Business Description"
                  value={formData.business_description}
                  onChange={(e) => handleFormChange('business_description', e.target.value)}
                  placeholder="Describe your business activities and services"
                  rows={4}
                  required
                  error={validationErrors.business_description}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            onClick={handlePrevious}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Previous
          </button>

          <div className="flex gap-3">
            <button
              onClick={saveFormData}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <i className="ri-save-line mr-2"></i>
                  Save
                </>
              )}
            </button>

            <button
              onClick={handleNext}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
              <i className="ri-arrow-right-line ml-2"></i>
            </button>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CompanyProfilePage;
