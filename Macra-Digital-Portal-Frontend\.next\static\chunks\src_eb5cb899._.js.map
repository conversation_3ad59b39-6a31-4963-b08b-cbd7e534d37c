{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\n  license_type_id: string;\n  name: string;\n  code?: string;\n  description?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LicenseCategory {\n  license_category_id: string;\n  name: string;\n  description?: string;\n  license_type_id: string;\n  license_type?: LicenseType;\n  parent_id?: string;\n  parent?: LicenseCategory;\n  children?: LicenseCategory[];\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Applicant {\n  applicant_id: string;\n  name: string;\n  business_registration_number: string;\n  tpin: string;\n  website: string;\n  email: string;\n  phone: string;\n  fax?: string;\n  level_of_insurance_cover?: string;\n  address_id?: string;\n  contact_id?: string;\n  date_incorporation: string;\n  place_incorporation: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Application {\n  application_id: string;\n  application_number: string;\n  applicant_id: string;\n  license_category_id: string;\n  status: ApplicationStatus;\n  current_step: number;\n  progress_percentage: number;\n  submitted_at?: string;\n  created_at: string;\n  updated_at: string;\n  applicant?: Applicant;\n  license_category?: LicenseCategory;\n}\n\nexport enum ApplicationStatus {\n  DRAFT = 'draft',\n  SUBMITTED = 'submitted',\n  UNDER_REVIEW = 'under_review',\n  EVALUATION = 'evaluation',\n  APPROVED = 'approved',\n  REJECTED = 'rejected',\n  WITHDRAWN = 'withdrawn',\n}\n\nexport interface ApplicationFilters {\n  licenseTypeId?: string;\n  licenseCategoryId?: string;\n  status?: ApplicationStatus | '';\n  dateRange?: string;\n  search?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems: number;\n    currentPage: number;\n    totalPages: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    filter: Record<string, string | string[]>;\n  };\n  links: {\n    first: string;\n    previous: string;\n    current: string;\n    next: string;\n    last: string;\n  };\n}\n"], "names": [], "mappings": ";;;AAuDO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', processApiResponse(response) );\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (processApiResponse(response) ?.data) {\r\n        applications = Array.isArray(processApiResponse(response).data) ? processApiResponse(response) .data : [];\r\n      } else if (Array.isArray(processApiResponse(response))) {\r\n        applications = processApiResponse(response);\r\n      } else if (processApiResponse(response)) {\r\n        // Single application or other structure\r\n        applications = [processApiResponse(response) ];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7D,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAY,MAAM;gBACvC,eAAe,MAAM,OAAO,CAAC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAW,IAAI,GAAG,EAAE;YAC3G,OAAO,IAAI,MAAM,OAAO,CAAC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY;gBACtD,eAAe,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACpC,OAAO,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBACvC,wCAAwC;gBACxC,eAAe;oBAAC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;iBAAW;YAChD;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\n\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheItem<any>>();\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\n\n  /**\n   * Set cache item with TTL\n   */\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    const item: CacheItem<T> = {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    };\n    \n    this.cache.set(key, item);\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\n  }\n\n  /**\n   * Get cache item if not expired\n   */\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    \n    if (!item) {\n      console.log(`Cache MISS: ${key}`);\n      return null;\n    }\n\n    const now = Date.now();\n    if (now > item.expiresAt) {\n      console.log(`Cache EXPIRED: ${key}`);\n      this.cache.delete(key);\n      return null;\n    }\n\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\n    return item.data as T;\n  }\n\n  /**\n   * Check if cache has valid item\n   */\n  has(key: string): boolean {\n    return this.get(key) !== null;\n  }\n\n  /**\n   * Delete cache item\n   */\n  delete(key: string): boolean {\n    console.log(`Cache DELETE: ${key}`);\n    return this.cache.delete(key);\n  }\n\n  /**\n   * Clear all cache\n   */\n  clear(): void {\n    console.log('Cache CLEAR: All items');\n    this.cache.clear();\n  }\n\n  /**\n   * Get cache stats\n   */\n  getStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Clean expired items\n   */\n  cleanup(): void {\n    const now = Date.now();\n    let cleaned = 0;\n\n    for (const [key, item] of this.cache.entries()) {\n      if (now > item.expiresAt) {\n        this.cache.delete(key);\n        cleaned++;\n      }\n    }\n\n    if (cleaned > 0) {\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\n    }\n  }\n\n  /**\n   * Get or set pattern - fetch data if not cached\n   */\n  async getOrSet<T>(\n    key: string,\n    fetcher: () => Promise<T>,\n    ttl: number = this.defaultTTL\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    console.log(`Cache FETCH: ${key}`);\n    const data = await fetcher();\n    \n    // Store in cache\n    this.set(key, data, ttl);\n    \n    return data;\n  }\n\n  /**\n   * Invalidate cache by pattern\n   */\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    let invalidated = 0;\n\n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n        invalidated++;\n      }\n    }\n\n    if (invalidated > 0) {\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\n    }\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Cache keys constants\nexport const CACHE_KEYS = {\n  LICENSE_TYPES: 'license-types',\n  LICENSE_CATEGORIES: 'license-categories',\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\n  USER_APPLICATIONS: 'user-applications',\n  APPLICATION: (id: string) => `application-${id}`,\n} as const;\n\n// Cache TTL constants (in milliseconds)\nexport const CACHE_TTL = {\n  SHORT: 2 * 60 * 1000,      // 2 minutes\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\n  LONG: 15 * 60 * 1000,      // 15 minutes\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\n} as const;\n\n// Auto cleanup every 5 minutes\nsetInterval(() => {\n  cacheService.cleanup();\n}, 5 * 60 * 1000);\n\nexport default cacheService;\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { LicenseType } from './licenseTypeService';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Utility functions for category codes\nexport const generateCategoryCode = (name: string): string => {\n  return name\n    .toLowerCase()\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    .substring(0, 50); // Limit length\n};\n\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\n  return categories.map(category => ({\n    ...category,\n    code: generateCategoryCode(category.name),\n    children: category.children ? addCodesToCategories(category.children) : undefined\n  }));\n};\n\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\n  for (const category of categories) {\n    if (category.code === code) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryByCode(category.children, code);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\n  for (const category of categories) {\n    if (category.license_category_id === id) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryById(category.children, id);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\n// Types\nexport interface LicenseCategory {\n  license_category_id: string;\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  license_type?: LicenseType;\n  parent?: LicenseCategory;\n  children?: LicenseCategory[];\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  // Generated code for URL-friendly routing\n  code?: string;\n}\n\nexport interface CreateLicenseCategoryDto {\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n}\n\nexport interface UpdateLicenseCategoryDto {\n  license_type_id?: string;\n  parent_id?: string;\n  name?: string;\n  fee?: string;\n  description?: string;\n  authorizes?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseCategoryService = {\n  // Get all license categories with pagination\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license category by ID\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\n    const response = await apiClient.get(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get license categories by license type\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<LicenseCategory[]> {\n    const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n    return response.data;\n  },\n\n  // Create new license category\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\n    return response.data;\n  },\n\n  // Update license category\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\n    return response.data;\n  },\n\n  // Delete license category\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get all license categories (simple list for dropdowns) with caching\n  async getAllLicenseCategories(): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_CATEGORIES,\n      async () => {\n        console.log('Fetching license categories from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseCategories({ limit: 100 });\n        return addCodesToCategories(response.data);\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n\n  // Get hierarchical tree of categories for a license type with caching\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `category-tree-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\n        return addCodesToCategories(response.data);\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get root categories (no parent) for a license type with caching\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `root-categories-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\n        return response.data;\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get license categories for parent selection dropdown\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    try {\n      const params = excludeId ? { excludeId } : {};\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\n\n      // Try the new endpoint first\n      try {\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\n\n\n        if (response.data && Array.isArray(response.data.data)) {\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\n          return response.data.data;\n        } else {\n          console.warn('⚠️ API returned non-array data:', response.data);\n          return [];\n        }\n      } catch (newEndpointError) {\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\n\n        // Fallback to existing endpoint\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n        console.log('🔄 Fallback response:', response.data);\n\n        if (response.data && Array.isArray(response.data)) {\n          // Filter out the excluded category if specified\n          let categories = response.data;\n          if (excludeId) {\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\n          }\n          console.log('✅ Fallback successful with', categories.length, 'items');\n          return categories;\n        } else {\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\n          return [];\n        }\n      }\n    } catch (error) {\n\n      return [];\n    }\n  },\n\n  // Get potential parent categories for a license type\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    const params = excludeId ? { excludeId } : {};\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,IAAI,KAAK,MAAM;YAC1B,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAqFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,oBAAmB,EAAU;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,yCAAyC;IACzC,MAAM,4BAA2B,aAAqB;QACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC3F,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Types\nexport interface LicenseType {\n  license_type_id: string;\n  name: string;\n  code?: string;\n  description: string;\n  validity: number;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n}\n\nexport interface CreateLicenseTypeDto {\n  name: string;\n  description: string;\n  validity: number;\n}\n\nexport interface UpdateLicenseTypeDto {\n  name?: string;\n  description?: string;\n  validity?: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseTypesResponse = PaginatedResponse<LicenseType>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseTypeService = {\n  // Get all license types with pagination\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license type by ID\n  async getLicenseType(id: string): Promise<LicenseType> {\n    const response = await apiClient.get(`/license-types/${id}`);\n    return response.data;\n  },\n\n  // Create new license type\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\n    const response = await apiClient.post('/license-types', licenseTypeData);\n    return response.data;\n  },\n\n  // Update license type\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\n    return response.data;\n  },\n\n  // Delete license type\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-types/${id}`);\n    return response.data;\n  },\n\n  // Get all license types (simple list for dropdowns) with caching\n  async getAllLicenseTypes(): Promise<LicenseType[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_TYPES,\n      async () => {\n        console.log('Fetching license types from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseTypes({ limit: 100 });\n        return response.data;\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAwEO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;AACF", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/license/LicenseManagementTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { Application, ApplicationStatus, PaginatedResponse } from '../../types/license';\r\nimport { applicationService } from '../../services/applicationService';\r\nimport { licenseCategoryService, LicenseCategory } from '../../services/licenseCategoryService';\r\nimport { licenseTypeService, LicenseType } from '../../services/licenseTypeService';\r\n\r\ninterface LicenseManagementTableProps {\r\n  licenseTypeId?: string;\r\n  licenseTypeFilter?: string; // Filter by license type name\r\n  title: string;\r\n  description: string;\r\n  searchPlaceholder: string;\r\n  emptyStateIcon: string;\r\n  emptyStateMessage: string;\r\n}\r\n\r\nexport default function LicenseManagementTable({\r\n  licenseTypeId,\r\n  licenseTypeFilter,\r\n  title,\r\n  description,\r\n  searchPlaceholder,\r\n  emptyStateIcon,\r\n  emptyStateMessage,\r\n}: LicenseManagementTableProps) {\r\n  const { user } = useAuth();\r\n  const [applications, setApplications] = useState<Application[]>([]);\r\n  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n  const [resolvedLicenseTypeId, setResolvedLicenseTypeId] = useState<string | undefined>(undefined);\r\n  const [loading, setLoading] = useState(true);\r\n  const [loadingMessage, setLoadingMessage] = useState('Loading applications...');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\r\n\r\n  const [selectedLicenseCategory, setSelectedLicenseCategory] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | ''>('');\r\n  const [dateRangeFilter, setDateRangeFilter] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [totalItems, setTotalItems] = useState(0);\r\n  const [itemsPerPage] = useState(10);\r\n\r\n  const isAdmin = user?.isAdmin;\r\n\r\n  // Debounce search term to reduce API calls\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedSearchTerm(searchTerm);\r\n    }, 1200); // Increased to 1200ms delay to reduce API calls\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [searchTerm]);\r\n\r\n\r\n\r\n  // Fetch license types and resolve license type ID from filter name\r\n  useEffect(() => {\r\n    const fetchLicenseTypes = async () => {\r\n      try {\r\n        // Add delay to space out API calls\r\n        await new Promise(resolve => setTimeout(resolve, 200));\r\n        const response = await licenseTypeService.getAllLicenseTypes();\r\n        const types = Array.isArray(response) ? response : (response?.data || []);\r\n\r\n        // Ensure types is always an array\r\n        if (!Array.isArray(types)) {\r\n          console.warn('License types response is not an array:', types);\r\n          setLicenseTypes([]);\r\n          return;\r\n        }\r\n\r\n        setLicenseTypes(types);\r\n\r\n        // If we have a licenseTypeFilter, find the matching license type ID\r\n        if (licenseTypeFilter && types.length > 0) {\r\n          const matchingType = types.find(type =>\r\n            type.name.toLowerCase().includes(licenseTypeFilter.toLowerCase())\r\n          );\r\n          if (matchingType) {\r\n            setResolvedLicenseTypeId(matchingType.license_type_id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching license types:', error);\r\n        setLicenseTypes([]);\r\n      }\r\n    };\r\n\r\n    fetchLicenseTypes();\r\n  }, [licenseTypeFilter]);\r\n\r\n  // Fetch license categories for the dropdown\r\n  useEffect(() => {\r\n    const fetchLicenseCategories = async () => {\r\n      try {\r\n        // Add delay to space out API calls\r\n        await new Promise(resolve => setTimeout(resolve, 400));\r\n        let categories: LicenseCategory[] = [];\r\n\r\n        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;\r\n\r\n        if (effectiveLicenseTypeId) {\r\n          // Fetch categories for specific license type\r\n          const response = await licenseCategoryService.getLicenseCategoriesByType(effectiveLicenseTypeId);\r\n          categories = Array.isArray(response) ? response : (response?.data || []);\r\n        } else {\r\n          // Fetch all categories\r\n          const response = await licenseCategoryService.getAllLicenseCategories();\r\n          categories = Array.isArray(response) ? response : (response?.data || []);\r\n        }\r\n\r\n        // Ensure categories is always an array\r\n        if (!Array.isArray(categories)) {\r\n          console.warn('License categories response is not an array:', categories);\r\n          categories = [];\r\n        }\r\n\r\n        setLicenseCategories(categories);\r\n      } catch (error) {\r\n        console.error('Error fetching license categories:', error);\r\n        setLicenseCategories([]);\r\n      }\r\n    };\r\n\r\n    fetchLicenseCategories();\r\n  }, [licenseTypeId, resolvedLicenseTypeId]);\r\n\r\n  // Fetch applications\r\n  useEffect(() => {\r\n    const fetchApplications = async () => {\r\n      setLoading(true);\r\n      setLoadingMessage('Loading applications...');\r\n\r\n      // Add a minimum loading time to give impression of thorough processing\r\n      const minLoadingTime = 800; // 800ms minimum\r\n      const startTime = Date.now();\r\n\r\n      try {\r\n        // Add delay to space out API calls\r\n        await new Promise(resolve => setTimeout(resolve, 600));\r\n        const effectiveLicenseTypeId = licenseTypeId || resolvedLicenseTypeId;\r\n\r\n        const params = {\r\n          page: currentPage,\r\n          limit: itemsPerPage,\r\n          search: debouncedSearchTerm || undefined,\r\n          filters: {\r\n            licenseCategoryId: selectedLicenseCategory || undefined,\r\n            licenseTypeId: effectiveLicenseTypeId || undefined,\r\n            status: statusFilter || undefined,\r\n          },\r\n        };\r\n\r\n        const response = await applicationService.getApplications(params);\r\n\r\n        // Ensure applications data is always an array\r\n        const applications = Array.isArray(response?.data) ? response.data : [];\r\n        setApplications(applications);\r\n\r\n        // Set pagination data with fallbacks\r\n        setTotalPages(response?.meta?.totalPages || 1);\r\n        setTotalItems(response?.meta?.totalItems || 0);\r\n      } catch (error: any) {\r\n        console.error('Error fetching applications:', error);\r\n        setApplications([]);\r\n      } finally {\r\n        // Ensure minimum loading time for better UX\r\n        const elapsedTime = Date.now() - startTime;\r\n        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);\r\n\r\n        if (remainingTime > 0) {\r\n          setLoadingMessage('Finalizing results...');\r\n          await new Promise(resolve => setTimeout(resolve, remainingTime));\r\n        }\r\n\r\n        setLoading(false);\r\n        setLoadingMessage('Loading applications...');\r\n      }\r\n    };\r\n\r\n    fetchApplications();\r\n  }, [currentPage, debouncedSearchTerm, selectedLicenseCategory, statusFilter, licenseTypeId, resolvedLicenseTypeId]);\r\n\r\n  const getStatusBadge = (status: ApplicationStatus) => {\r\n    const statusClasses: Record<ApplicationStatus, string> = {\r\n      [ApplicationStatus.DRAFT]: 'bg-gray-100 text-gray-800',\r\n      [ApplicationStatus.SUBMITTED]: 'bg-blue-100 text-blue-800',\r\n      [ApplicationStatus.UNDER_REVIEW]: 'bg-yellow-100 text-yellow-800',\r\n      [ApplicationStatus.EVALUATION]: 'bg-purple-100 text-purple-800',\r\n      [ApplicationStatus.APPROVED]: 'bg-green-100 text-green-800',\r\n      [ApplicationStatus.REJECTED]: 'bg-red-100 text-red-800',\r\n      [ApplicationStatus.WITHDRAWN]: 'bg-gray-100 text-gray-800',\r\n    };\r\n\r\n    const statusLabels: Record<ApplicationStatus, string> = {\r\n      [ApplicationStatus.DRAFT]: 'Draft',\r\n      [ApplicationStatus.SUBMITTED]: 'Submitted',\r\n      [ApplicationStatus.UNDER_REVIEW]: 'Under Review',\r\n      [ApplicationStatus.EVALUATION]: 'Evaluation',\r\n      [ApplicationStatus.APPROVED]: 'Approved',\r\n      [ApplicationStatus.REJECTED]: 'Rejected',\r\n      [ApplicationStatus.WITHDRAWN]: 'Withdrawn',\r\n    };\r\n\r\n    return (\r\n      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status]}`}>\r\n        {statusLabels[status]}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  const getProgressBar = (percentage: number) => {\r\n    const getProgressColor = (percent: number) => {\r\n      if (percent >= 100) return 'bg-green-600';\r\n      if (percent >= 75) return 'bg-blue-600';\r\n      if (percent >= 50) return 'bg-yellow-600';\r\n      if (percent >= 25) return 'bg-orange-600';\r\n      return 'bg-red-600';\r\n    };\r\n\r\n    return (\r\n      <div className=\"flex items-center\">\r\n        <div className=\"w-16 bg-gray-200 rounded-full h-2 mr-2\">\r\n          <div \r\n            className={`h-2 rounded-full ${getProgressColor(percentage)}`} \r\n            style={{ width: `${percentage}%` }}\r\n          ></div>\r\n        </div>\r\n        <span className=\"text-sm text-gray-500\">{percentage}%</span>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const handleStatusUpdate = async (applicationId: string, newStatus: ApplicationStatus) => {\r\n    try {\r\n      await applicationService.updateApplicationStatus(applicationId, newStatus);\r\n      // Refresh the applications list\r\n      const params = {\r\n        page: currentPage,\r\n        limit: itemsPerPage,\r\n        search: searchTerm || undefined,\r\n        filters: {\r\n          licenseCategoryId: selectedLicenseCategory || undefined,\r\n          licenseTypeId: licenseTypeId || undefined,\r\n          status: statusFilter || undefined,\r\n        },\r\n      };\r\n      const response = await applicationService.getApplications(params);\r\n      setApplications(response.data);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    setCurrentPage(1); // Reset to first page when applying filters\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 dark:border-red-500 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600 dark:text-gray-400 text-sm\">{loadingMessage}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\r\n              <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\r\n                {description}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filters Section - Matching Audit Trail Style */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\r\n          <h2 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4\">Filters</h2>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            {/* License Category Filter */}\r\n            <div>\r\n              <label htmlFor=\"license-category\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                License Category\r\n              </label>\r\n              <select\r\n                id=\"license-category\"\r\n                name=\"license-category\"\r\n                value={selectedLicenseCategory}\r\n                onChange={(e) => setSelectedLicenseCategory(e.target.value)}\r\n                className=\"appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm\"\r\n              >\r\n                <option value=\"\">All Categories</option>\r\n                {Array.isArray(licenseCategories) && licenseCategories.map((category) => (\r\n                  <option key={category.license_category_id} value={category.license_category_id}>\r\n                    {category.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* Status Filter */}\r\n            <div>\r\n              <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Application Status\r\n              </label>\r\n              <select\r\n                id=\"status\"\r\n                name=\"status\"\r\n                value={statusFilter}\r\n                onChange={(e) => setStatusFilter(e.target.value as ApplicationStatus | '')}\r\n                className=\"appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm\"\r\n              >\r\n                <option value=\"\">All Statuses</option>\r\n                <option value={ApplicationStatus.DRAFT}>Draft</option>\r\n                <option value={ApplicationStatus.SUBMITTED}>Submitted</option>\r\n                <option value={ApplicationStatus.UNDER_REVIEW}>Under Review</option>\r\n                <option value={ApplicationStatus.EVALUATION}>Evaluation</option>\r\n                <option value={ApplicationStatus.APPROVED}>Approved</option>\r\n                <option value={ApplicationStatus.REJECTED}>Rejected</option>\r\n                <option value={ApplicationStatus.WITHDRAWN}>Withdrawn</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Date Range Filter */}\r\n            <div>\r\n              <label htmlFor=\"date-range\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Date Range\r\n              </label>\r\n              <select\r\n                id=\"date-range\"\r\n                name=\"date-range\"\r\n                value={dateRangeFilter}\r\n                onChange={(e) => setDateRangeFilter(e.target.value)}\r\n                className=\"appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm\"\r\n              >\r\n                <option value=\"\">All Time</option>\r\n                <option value=\"last-30\">Last 30 Days</option>\r\n                <option value=\"last-90\">Last 90 Days</option>\r\n                <option value=\"last-year\">Last Year</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Search Filter */}\r\n            <div>\r\n              <label htmlFor=\"search-filter\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Search Applications\r\n              </label>\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n                </div>\r\n                <input\r\n                  id=\"search-filter\"\r\n                  name=\"search-filter\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-200 sm:text-sm\"\r\n                  placeholder={searchPlaceholder}\r\n                  type=\"search\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Filter Actions */}\r\n          <div className=\"mt-4 flex items-center justify-between\">\r\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              {applications.length > 0 && (\r\n                <span>\r\n                  Showing {applications.length} of {totalItems} applications\r\n                </span>\r\n              )}\r\n            </div>\r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  setSelectedLicenseCategory('');\r\n                  setStatusFilter('');\r\n                  setDateRangeFilter('');\r\n                  setSearchTerm('');\r\n                  setCurrentPage(1);\r\n                }}\r\n                className=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n              >\r\n                <i className=\"ri-refresh-line mr-2\"></i>\r\n                Clear Filters\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={applyFilters}\r\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n              >\r\n                <i className=\"ri-filter-line mr-2\"></i>\r\n                Apply Filters\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Applications Table - Matching Audit Trail Style */}\r\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Applications</h3>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage and track license applications for {title.toLowerCase()}\r\n            </p>\r\n          </div>\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n              <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n                <tr>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Application Number\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Applicant\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    License Category\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Status\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Progress\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Submitted Date\r\n                  </th>\r\n                  <th scope=\"col\" className=\"relative px-6 py-3\">\r\n                    <span className=\"sr-only\">Actions</span>\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                {applications.map((application) => (\r\n                  <tr key={application.application_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                        {application.application_number}\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"flex-shrink-0 h-10 w-10\">\r\n                          <div className=\"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center\">\r\n                            <i className=\"ri-building-line text-blue-600 dark:text-blue-400\"></i>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                          <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                            {application.applicant?.name || 'N/A'}\r\n                          </div>\r\n                          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                            BRN: {application.applicant?.business_registration_number || 'N/A'} |\r\n                            TPIN: {application.applicant?.tpin || 'N/A'}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                        {application.license_category?.name || 'N/A'}\r\n                      </div>\r\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                        {application.license_category?.license_type?.name || 'N/A'}\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      {getStatusBadge(application.status)}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      {getProgressBar(application.progress_percentage)}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                      {application.submitted_at\r\n                        ? new Date(application.submitted_at).toLocaleDateString()\r\n                        : new Date(application.created_at).toLocaleDateString()\r\n                      }\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                      <div className=\"flex items-center justify-end space-x-2\">\r\n                        <button\r\n                          type=\"button\"\r\n                          className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors\"\r\n                        >\r\n                          <i className=\"ri-eye-line mr-1\"></i>\r\n                          View\r\n                        </button>\r\n                        {isAdmin && (\r\n                          <>\r\n                            {application.status === ApplicationStatus.SUBMITTED && (\r\n                              <button\r\n                                type=\"button\"\r\n                                onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.UNDER_REVIEW)}\r\n                                className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md hover:bg-yellow-100 dark:hover:bg-yellow-900/40 transition-colors\"\r\n                              >\r\n                                <i className=\"ri-search-line mr-1\"></i>\r\n                                Review\r\n                              </button>\r\n                            )}\r\n                            {application.status === ApplicationStatus.UNDER_REVIEW && (\r\n                              <button\r\n                                type=\"button\"\r\n                                onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.EVALUATION)}\r\n                                className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors\"\r\n                              >\r\n                                <i className=\"ri-clipboard-line mr-1\"></i>\r\n                                Evaluate\r\n                              </button>\r\n                            )}\r\n                            {application.status === ApplicationStatus.EVALUATION && (\r\n                              <>\r\n                                <button\r\n                                  type=\"button\"\r\n                                  onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.APPROVED)}\r\n                                  className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors\"\r\n                                >\r\n                                  <i className=\"ri-check-line mr-1\"></i>\r\n                                  Approve\r\n                                </button>\r\n                                <button\r\n                                  type=\"button\"\r\n                                  onClick={() => handleStatusUpdate(application.application_id, ApplicationStatus.REJECTED)}\r\n                                  className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md hover:bg-red-100 dark:hover:bg-red-900/40 transition-colors\"\r\n                                >\r\n                                  <i className=\"ri-close-line mr-1\"></i>\r\n                                  Reject\r\n                                </button>\r\n                              </>\r\n                            )}\r\n                            <button\r\n                              type=\"button\"\r\n                              className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md hover:bg-gray-100 dark:hover:bg-gray-900/40 transition-colors\"\r\n                            >\r\n                              <i className=\"ri-file-text-line mr-1\"></i>\r\n                            </button>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n\r\n          {/* Empty State */}\r\n          {applications.length === 0 && !loading && (\r\n            <div className=\"text-center py-12\">\r\n              <i className={`${emptyStateIcon} text-4xl text-gray-400 dark:text-gray-500 mb-4`}></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No Applications Found</h3>\r\n              <p className=\"text-gray-500 dark:text-gray-400\">{emptyStateMessage}</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Pagination - Matching Audit Trail Style */}\r\n          {applications.length > 0 && (\r\n            <div className=\"bg-white dark:bg-gray-800 px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex-1 flex justify-between sm:hidden\">\r\n                <button\r\n                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n                  disabled={currentPage === 1}\r\n                  className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  <i className=\"ri-arrow-left-line mr-2\"></i>\r\n                  Previous\r\n                </button>\r\n                <button\r\n                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n                  disabled={currentPage === totalPages}\r\n                  className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  Next\r\n                  <i className=\"ri-arrow-right-line ml-2\"></i>\r\n                </button>\r\n              </div>\r\n              <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                    Showing <span className=\"font-medium\">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}\r\n                    <span className=\"font-medium\">{Math.min(currentPage * itemsPerPage, totalItems)}</span> of{' '}\r\n                    <span className=\"font-medium\">{totalItems}</span> applications\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n                    <button\r\n                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n                      disabled={currentPage === 1}\r\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                      <span className=\"sr-only\">Previous</span>\r\n                      <i className=\"ri-arrow-left-s-line\"></i>\r\n                    </button>\r\n\r\n                    {/* Page numbers */}\r\n                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                      const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;\r\n                      if (pageNumber > totalPages) return null;\r\n\r\n                      return (\r\n                        <button\r\n                          key={pageNumber}\r\n                          onClick={() => handlePageChange(pageNumber)}\r\n                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\r\n                            pageNumber === currentPage\r\n                              ? 'z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400'\r\n                              : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'\r\n                          }`}\r\n                        >\r\n                          {pageNumber}\r\n                        </button>\r\n                      );\r\n                    })}\r\n\r\n                    <button\r\n                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n                      disabled={currentPage === totalPages}\r\n                      className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                      <span className=\"sr-only\">Next</span>\r\n                      <i className=\"ri-arrow-right-s-line\"></i>\r\n                    </button>\r\n                  </nav>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Empty state */}\r\n        {applications.length === 0 && !loading && (\r\n          <div className=\"text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <i className={`${emptyStateIcon} text-4xl text-gray-400 dark:text-gray-500 mb-4`}></i>\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No applications found</h3>\r\n            <p className=\"text-gray-500 dark:text-gray-400\">\r\n              {searchTerm || statusFilter !== '' || selectedLicenseCategory !== ''\r\n                ? 'Try adjusting your search or filter criteria.'\r\n                : emptyStateMessage}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAmBe,SAAS,uBAAuB,EAC7C,aAAa,EACb,iBAAiB,EACjB,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACW;;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEhC,MAAM,UAAU,MAAM;IAEtB,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,QAAQ;0DAAW;oBACvB,uBAAuB;gBACzB;yDAAG,OAAO,gDAAgD;YAE1D;oDAAO,IAAM,aAAa;;QAC5B;2CAAG;QAAC;KAAW;IAIf,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;sEAAoB;oBACxB,IAAI;wBACF,mCAAmC;wBACnC,MAAM,IAAI;kFAAQ,CAAA,UAAW,WAAW,SAAS;;wBACjD,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;wBAC5D,MAAM,QAAQ,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;wBAExE,kCAAkC;wBAClC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;4BACzB,QAAQ,IAAI,CAAC,2CAA2C;4BACxD,gBAAgB,EAAE;4BAClB;wBACF;wBAEA,gBAAgB;wBAEhB,oEAAoE;wBACpE,IAAI,qBAAqB,MAAM,MAAM,GAAG,GAAG;4BACzC,MAAM,eAAe,MAAM,IAAI;mGAAC,CAAA,OAC9B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,kBAAkB,WAAW;;4BAEhE,IAAI,cAAc;gCAChB,yBAAyB,aAAa,eAAe;4BACvD;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,gBAAgB,EAAE;oBACpB;gBACF;;YAEA;QACF;2CAAG;QAAC;KAAkB;IAEtB,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;2EAAyB;oBAC7B,IAAI;wBACF,mCAAmC;wBACnC,MAAM,IAAI;uFAAQ,CAAA,UAAW,WAAW,SAAS;;wBACjD,IAAI,aAAgC,EAAE;wBAEtC,MAAM,yBAAyB,iBAAiB;wBAEhD,IAAI,wBAAwB;4BAC1B,6CAA6C;4BAC7C,MAAM,WAAW,MAAM,4IAAA,CAAA,yBAAsB,CAAC,0BAA0B,CAAC;4BACzE,aAAa,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;wBACzE,OAAO;4BACL,uBAAuB;4BACvB,MAAM,WAAW,MAAM,4IAAA,CAAA,yBAAsB,CAAC,uBAAuB;4BACrE,aAAa,MAAM,OAAO,CAAC,YAAY,WAAY,UAAU,QAAQ,EAAE;wBACzE;wBAEA,uCAAuC;wBACvC,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa;4BAC9B,QAAQ,IAAI,CAAC,gDAAgD;4BAC7D,aAAa,EAAE;wBACjB;wBAEA,qBAAqB;oBACvB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sCAAsC;wBACpD,qBAAqB,EAAE;oBACzB;gBACF;;YAEA;QACF;2CAAG;QAAC;QAAe;KAAsB;IAEzC,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;sEAAoB;oBACxB,WAAW;oBACX,kBAAkB;oBAElB,uEAAuE;oBACvE,MAAM,iBAAiB,KAAK,gBAAgB;oBAC5C,MAAM,YAAY,KAAK,GAAG;oBAE1B,IAAI;wBACF,mCAAmC;wBACnC,MAAM,IAAI;kFAAQ,CAAA,UAAW,WAAW,SAAS;;wBACjD,MAAM,yBAAyB,iBAAiB;wBAEhD,MAAM,SAAS;4BACb,MAAM;4BACN,OAAO;4BACP,QAAQ,uBAAuB;4BAC/B,SAAS;gCACP,mBAAmB,2BAA2B;gCAC9C,eAAe,0BAA0B;gCACzC,QAAQ,gBAAgB;4BAC1B;wBACF;wBAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;wBAE1D,8CAA8C;wBAC9C,MAAM,eAAe,MAAM,OAAO,CAAC,UAAU,QAAQ,SAAS,IAAI,GAAG,EAAE;wBACvE,gBAAgB;wBAEhB,qCAAqC;wBACrC,cAAc,UAAU,MAAM,cAAc;wBAC5C,cAAc,UAAU,MAAM,cAAc;oBAC9C,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,gBAAgB,EAAE;oBACpB,SAAU;wBACR,4CAA4C;wBAC5C,MAAM,cAAc,KAAK,GAAG,KAAK;wBACjC,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG,iBAAiB;wBAEnD,IAAI,gBAAgB,GAAG;4BACrB,kBAAkB;4BAClB,MAAM,IAAI;sFAAQ,CAAA,UAAW,WAAW,SAAS;;wBACnD;wBAEA,WAAW;wBACX,kBAAkB;oBACpB;gBACF;;YAEA;QACF;2CAAG;QAAC;QAAa;QAAqB;QAAyB;QAAc;QAAe;KAAsB;IAElH,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAmD;YACvD,CAAC,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YAC3B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAClC,CAAC,0HAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAChC,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC;QAEA,MAAM,eAAkD;YACtD,CAAC,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YAC3B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAClC,CAAC,0HAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAChC,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC9B,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC;QAEA,qBACE,6LAAC;YAAK,WAAW,CAAC,8DAA8D,EAAE,aAAa,CAAC,OAAO,EAAE;sBACtG,YAAY,CAAC,OAAO;;;;;;IAG3B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,CAAC;YACxB,IAAI,WAAW,KAAK,OAAO;YAC3B,IAAI,WAAW,IAAI,OAAO;YAC1B,IAAI,WAAW,IAAI,OAAO;YAC1B,IAAI,WAAW,IAAI,OAAO;YAC1B,OAAO;QACT;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAW,CAAC,iBAAiB,EAAE,iBAAiB,aAAa;wBAC7D,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;;;;;;8BAGrC,6LAAC;oBAAK,WAAU;;wBAAyB;wBAAW;;;;;;;;;;;;;IAG1D;IAEA,MAAM,qBAAqB,OAAO,eAAuB;QACvD,IAAI;YACF,MAAM,wIAAA,CAAA,qBAAkB,CAAC,uBAAuB,CAAC,eAAe;YAChE,gCAAgC;YAChC,MAAM,SAAS;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,cAAc;gBACtB,SAAS;oBACP,mBAAmB,2BAA2B;oBAC9C,eAAe,iBAAiB;oBAChC,QAAQ,gBAAgB;gBAC1B;YACF;YACA,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,eAAe;QACnB,eAAe,IAAI,4CAA4C;IACjE;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;;;;;;IAIjE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;8BAOT,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAmB,WAAU;sDAAkE;;;;;;sDAG9G,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,2BAA2B,EAAE,MAAM,CAAC,KAAK;4CAC1D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,MAAM,OAAO,CAAC,sBAAsB,kBAAkB,GAAG,CAAC,CAAC,yBAC1D,6LAAC;wDAA0C,OAAO,SAAS,mBAAmB;kEAC3E,SAAS,IAAI;uDADH,SAAS,mBAAmB;;;;;;;;;;;;;;;;;8CAQ/C,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAAkE;;;;;;sDAGpG,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,KAAK;8DAAE;;;;;;8DACxC,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,SAAS;8DAAE;;;;;;8DAC5C,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,YAAY;8DAAE;;;;;;8DAC/C,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,UAAU;8DAAE;;;;;;8DAC7C,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,QAAQ;8DAAE;;;;;;8DAC3C,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,QAAQ;8DAAE;;;;;;8DAC3C,6LAAC;oDAAO,OAAO,0HAAA,CAAA,oBAAiB,CAAC,SAAS;8DAAE;;;;;;;;;;;;;;;;;;8CAKhD,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAkE;;;;;;sDAGxG,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAkE;;;;;;sDAG3G,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;oDACV,aAAa;oDACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,aAAa,MAAM,GAAG,mBACrB,6LAAC;;4CAAK;4CACK,aAAa,MAAM;4CAAC;4CAAK;4CAAW;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;gDACP,2BAA2B;gDAC3B,gBAAgB;gDAChB,mBAAmB;gDACnB,cAAc;gDACd,eAAe;4CACjB;4CACA,WAAU;;8DAEV,6LAAC;oDAAE,WAAU;;;;;;gDAA2B;;;;;;;sDAG1C,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC;oDAAE,WAAU;;;;;;gDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;8BAU/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,6LAAC;oCAAE,WAAU;;wCAAgD;wCAChB,MAAM,WAAW;;;;;;;;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,6LAAC;oDAAG,OAAM;oDAAM,WAAU;8DACxB,cAAA,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC;wCAAM,WAAU;kDACd,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;gDAAoC,WAAU;;kEAC7C,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;sEACZ,YAAY,kBAAkB;;;;;;;;;;;kEAGnC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAE,WAAU;;;;;;;;;;;;;;;;8EAGjB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,YAAY,SAAS,EAAE,QAAQ;;;;;;sFAElC,6LAAC;4EAAI,WAAU;;gFAA2C;gFAClD,YAAY,SAAS,EAAE,gCAAgC;gFAAM;gFAC5D,YAAY,SAAS,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAK9C,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACZ,YAAY,gBAAgB,EAAE,QAAQ;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;0EACZ,YAAY,gBAAgB,EAAE,cAAc,QAAQ;;;;;;;;;;;;kEAGzD,6LAAC;wDAAG,WAAU;kEACX,eAAe,YAAY,MAAM;;;;;;kEAEpC,6LAAC;wDAAG,WAAU;kEACX,eAAe,YAAY,mBAAmB;;;;;;kEAEjD,6LAAC;wDAAG,WAAU;kEACX,YAAY,YAAY,GACrB,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KACrD,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;kEAGzD,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,WAAU;;sFAEV,6LAAC;4EAAE,WAAU;;;;;;wEAAuB;;;;;;;gEAGrC,yBACC;;wEACG,YAAY,MAAM,KAAK,0HAAA,CAAA,oBAAiB,CAAC,SAAS,kBACjD,6LAAC;4EACC,MAAK;4EACL,SAAS,IAAM,mBAAmB,YAAY,cAAc,EAAE,0HAAA,CAAA,oBAAiB,CAAC,YAAY;4EAC5F,WAAU;;8FAEV,6LAAC;oFAAE,WAAU;;;;;;gFAA0B;;;;;;;wEAI1C,YAAY,MAAM,KAAK,0HAAA,CAAA,oBAAiB,CAAC,YAAY,kBACpD,6LAAC;4EACC,MAAK;4EACL,SAAS,IAAM,mBAAmB,YAAY,cAAc,EAAE,0HAAA,CAAA,oBAAiB,CAAC,UAAU;4EAC1F,WAAU;;8FAEV,6LAAC;oFAAE,WAAU;;;;;;gFAA6B;;;;;;;wEAI7C,YAAY,MAAM,KAAK,0HAAA,CAAA,oBAAiB,CAAC,UAAU,kBAClD;;8FACE,6LAAC;oFACC,MAAK;oFACL,SAAS,IAAM,mBAAmB,YAAY,cAAc,EAAE,0HAAA,CAAA,oBAAiB,CAAC,QAAQ;oFACxF,WAAU;;sGAEV,6LAAC;4FAAE,WAAU;;;;;;wFAAyB;;;;;;;8FAGxC,6LAAC;oFACC,MAAK;oFACL,SAAS,IAAM,mBAAmB,YAAY,cAAc,EAAE,0HAAA,CAAA,oBAAiB,CAAC,QAAQ;oFACxF,WAAU;;sGAEV,6LAAC;4FAAE,WAAU;;;;;;wFAAyB;;;;;;;;;sFAK5C,6LAAC;4EACC,MAAK;4EACL,WAAU;sFAEV,cAAA,6LAAC;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;+CAnGhB,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;wBAgH1C,aAAa,MAAM,KAAK,KAAK,CAAC,yBAC7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAW,GAAG,eAAe,+CAA+C,CAAC;;;;;;8CAChF,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;;wBAKpD,aAAa,MAAM,GAAG,mBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,cAAc;4CAC1D,UAAU,gBAAgB;4CAC1B,WAAU;;8DAEV,6LAAC;oDAAE,WAAU;;;;;;gDAA8B;;;;;;;sDAG7C,6LAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,GAAG,CAAC,YAAY,cAAc;4CACnE,UAAU,gBAAgB;4CAC1B,WAAU;;gDACX;8DAEC,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC;gDAAE,WAAU;;oDAA2C;kEAC9C,6LAAC;wDAAK,WAAU;kEAAe,AAAC,CAAC,cAAc,CAAC,IAAI,eAAgB;;;;;;oDAAS;oDAAI;kEACzF,6LAAC;wDAAK,WAAU;kEAAe,KAAK,GAAG,CAAC,cAAc,cAAc;;;;;;oDAAmB;oDAAI;kEAC3F,6LAAC;wDAAK,WAAU;kEAAe;;;;;;oDAAkB;;;;;;;;;;;;sDAGrD,6LAAC;sDACC,cAAA,6LAAC;gDAAI,WAAU;gDAA4D,cAAW;;kEACpF,6LAAC;wDACC,SAAS,IAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,cAAc;wDAC1D,UAAU,gBAAgB;wDAC1B,WAAU;;0EAEV,6LAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,6LAAC;gEAAE,WAAU;;;;;;;;;;;;oDAId,MAAM,IAAI,CAAC;wDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;oDAAY,GAAG,CAAC,GAAG;wDACnD,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,MAAM;wDAC5E,IAAI,aAAa,YAAY,OAAO;wDAEpC,qBACE,6LAAC;4DAEC,SAAS,IAAM,iBAAiB;4DAChC,WAAW,CAAC,uEAAuE,EACjF,eAAe,cACX,oFACA,2IACJ;sEAED;2DARI;;;;;oDAWX;kEAEA,6LAAC;wDACC,SAAS,IAAM,iBAAiB,KAAK,GAAG,CAAC,YAAY,cAAc;wDACnE,UAAU,gBAAgB;wDAC1B,WAAU;;0EAEV,6LAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAU1B,aAAa,MAAM,KAAK,KAAK,CAAC,yBAC7B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAW,GAAG,eAAe,+CAA+C,CAAC;;;;;;sCAChF,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCACV,cAAc,iBAAiB,MAAM,4BAA4B,KAC9D,kDACA;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAvoBwB;;QASL,kIAAA,CAAA,UAAO;;;KATF", "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useParams } from 'next/navigation';\r\nimport LicenseManagementTable from '../../../components/license/LicenseManagementTable';\r\n\r\n// License type configuration with backend license type IDs\r\nconst licenseTypeConfig = {\r\n  'postal': {\r\n    name: 'Postal Services',\r\n    description: 'Manage postal and courier service license applications for domestic and international operations. View applications, filter by category and status.',\r\n    searchPlaceholder: 'Search postal applications, companies, application numbers...',\r\n    emptyStateIcon: 'ri-mail-line',\r\n    emptyStateMessage: 'No postal license applications are available.',\r\n    licenseTypeFilter: 'postal' // This will be used to filter by license type name\r\n  },\r\n  'telecommunications': {\r\n    name: 'Telecommunications',\r\n    description: 'Manage telecommunications and spectrum license applications for mobile networks, ISPs, and broadcasting services. View applications, filter by category and status.',\r\n    searchPlaceholder: 'Search telecommunications applications, companies, application numbers...',\r\n    emptyStateIcon: 'ri-signal-tower-line',\r\n    emptyStateMessage: 'No telecommunications license applications are available.',\r\n    licenseTypeFilter: 'telecommunications'\r\n  },\r\n  'standards': {\r\n    name: 'Standards Compliance',\r\n    description: 'Manage standards compliance and type approval certificate applications. View applications, filter by category and status.',\r\n    searchPlaceholder: 'Search standards applications, companies, application numbers...',\r\n    emptyStateIcon: 'ri-award-line',\r\n    emptyStateMessage: 'No standards applications are available.',\r\n    licenseTypeFilter: 'standards'\r\n  },\r\n  'clf': {\r\n    name: 'CLF (Converged Licensing Framework)',\r\n    description: 'Manage CLF licenses including Network Facility, Network Service, Application Service, and Content Service licenses.',\r\n    searchPlaceholder: 'Search CLF licenses, companies, license IDs...',\r\n    emptyStateIcon: 'ri-collage-line',\r\n    emptyStateMessage: 'No CLF license applications are available.',\r\n    licenseTypeFilter: 'clf'\r\n  }\r\n};\r\n\r\nexport default function LicenseTypeApplicationsPage() {\r\n  const params = useParams();\r\n  const licenseType = params['license-type'] as string;\r\n\r\n  // Get current license type configuration\r\n  const currentConfig = licenseTypeConfig[licenseType as keyof typeof licenseTypeConfig];\r\n\r\n  useEffect(() => {\r\n    if (!currentConfig) {\r\n      // Invalid license type, redirect to default\r\n      window.location.href = '/applications/telecommunications';\r\n      return;\r\n    }\r\n  }, [licenseType, currentConfig]);\r\n\r\n  if (!currentConfig) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64\">\r\n        <div className=\"text-center\">\r\n          <i className=\"ri-error-warning-line text-4xl text-red-500 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Invalid License Type</h3>\r\n          <p className=\"text-gray-500\">The requested license type was not found.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <LicenseManagementTable\r\n      licenseTypeFilter={currentConfig.licenseTypeFilter}\r\n      title={`${currentConfig.name} License Management`}\r\n      description={currentConfig.description}\r\n      searchPlaceholder={currentConfig.searchPlaceholder}\r\n      emptyStateIcon={currentConfig.emptyStateIcon}\r\n      emptyStateMessage={currentConfig.emptyStateMessage}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,2DAA2D;AAC3D,MAAM,oBAAoB;IACxB,UAAU;QACR,MAAM;QACN,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB,SAAS,mDAAmD;IACjF;IACA,sBAAsB;QACpB,MAAM;QACN,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;IACrB;IACA,aAAa;QACX,MAAM;QACN,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;IACrB;IACA,OAAO;QACL,MAAM;QACN,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;IACrB;AACF;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,MAAM,CAAC,eAAe;IAE1C,yCAAyC;IACzC,MAAM,gBAAgB,iBAAiB,CAAC,YAA8C;IAEtF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACR,IAAI,CAAC,eAAe;gBAClB,4CAA4C;gBAC5C,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF;QACF;gDAAG;QAAC;QAAa;KAAc;IAE/B,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC,0JAAA,CAAA,UAAsB;QACrB,mBAAmB,cAAc,iBAAiB;QAClD,OAAO,GAAG,cAAc,IAAI,CAAC,mBAAmB,CAAC;QACjD,aAAa,cAAc,WAAW;QACtC,mBAAmB,cAAc,iBAAiB;QAClD,gBAAgB,cAAc,cAAc;QAC5C,mBAAmB,cAAc,iBAAiB;;;;;;AAGxD;GArCwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}