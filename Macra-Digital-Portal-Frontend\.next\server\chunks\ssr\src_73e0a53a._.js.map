{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,sBAAsB;YACtB,0BAA0B;YAC1B,oCAAoC;YACpC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,8BAA8B;YAC9B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/TextInput.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  className?: string;\n  containerClassName?: string;\n}\n\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  id,\n  ...props\n}, ref) => {\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseInputClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n  `;\n  \n  const inputClasses = error\n    ? `${baseInputClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseInputClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={inputId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <input\n        ref={ref}\n        id={inputId}\n        className={`${inputClasses} ${className}`}\n        {...props}\n      />\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nTextInput.displayName = 'TextInput';\n\nexport default TextInput;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,MAAM,mBAAmB,CAAC;;;;;;EAM1B,CAAC;IAED,MAAM,eAAe,QACjB,GAAG,iBAAiB,2EAA2E,CAAC,GAChG,GAAG,iBAAiB,0GAA0G,CAAC;IAEnI,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;gBACxC,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface SelectOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  options: SelectOption[];\n  placeholder?: string;\n  className?: string;\n  containerClassName?: string;\n  onChange?: (value: string) => void;\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  options = [],\n  placeholder = 'Select an option...',\n  className = '',\n  containerClassName = '',\n  onChange,\n  id,\n  value,\n  ...props\n}, ref) => {\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseSelectClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  `;\n  \n  const selectClasses = error\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    if (onChange) {\n      onChange(e.target.value);\n    }\n  };\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={selectId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <select\n          ref={ref}\n          id={selectId}\n          value={value || ''}\n          onChange={handleChange}\n          className={`${selectClasses} ${className}`}\n          {...props}\n        >\n          {placeholder && (\n            <option value=\"\" disabled>\n              {placeholder}\n            </option>\n          )}\n          \n          {options.map((option) => (\n            <option \n              key={option.value} \n              value={option.value}\n              disabled={option.disabled}\n            >\n              {option.label}\n            </option>\n          ))}\n        </select>\n        \n        {/* Custom dropdown arrow */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSelect.displayName = 'Select';\n\nexport default Select;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,8OAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', processApiResponse(response) );\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (processApiResponse(response) ?.data) {\r\n        applications = Array.isArray(processApiResponse(response).data) ? processApiResponse(response) .data : [];\r\n      } else if (Array.isArray(processApiResponse(response))) {\r\n        applications = processApiResponse(response);\r\n      } else if (processApiResponse(response)) {\r\n        // Single application or other structure\r\n        applications = [processApiResponse(response) ];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7D,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAY,MAAM;gBACvC,eAAe,MAAM,OAAO,CAAC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAW,IAAI,GAAG,EAAE;YAC3G,OAAO,IAAI,MAAM,OAAO,CAAC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY;gBACtD,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YACpC,OAAO,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBACvC,wCAAwC;gBACxC,eAAe;oBAAC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;iBAAW;YAChD;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1401, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicantService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { Applicant } from '../types/license';\n\nexport interface CreateApplicantData {\n  name: string;\n  business_registration_number: string;\n  tpin: string;\n  website: string;\n  email: string;\n  phone: string;\n  fax?: string;\n  level_of_insurance_cover?: string;\n  address_id?: string;\n  contact_id?: string;\n  date_incorporation: string; // Changed from Date to string to match backend DTO\n  place_incorporation: string;\n}\n\nexport const applicantService = {\n  // Create new applicant\n  async createApplicant(data: CreateApplicantData): Promise<Applicant> {\n    try {\n      console.log('Creating applicant with data:', data);\n\n      const response = await apiClient.post('/applicants', data);\n      // Handle different response formats\n      if (response.data) {\n        // Check if it's a standard success response format\n        if (response.data.success !== undefined && response.data.data) {\n          console.log('Standard response format detected');\n          return response.data.data;\n        }\n        // Check if it's direct data format\n        else if (response.data.applicant_id || response.data.id) {\n          console.log('Direct data format detected');\n          return response.data;\n        }\n        // Fallback: assume it's the applicant data\n        else {\n          console.log('Fallback: treating response.data as applicant');\n          return response.data;\n        }\n      }\n\n      throw new Error('Invalid response format from applicant creation');\n    } catch (error) {\n      console.error('Error creating applicant:', error);\n      console.error('Error details:', (error as any)?.response?.data);\n      throw error;\n    }\n  },\n\n  // Get applicant by ID\n  async getApplicant(id: string): Promise<Applicant> {\n    try {\n      const response = await apiClient.get(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching applicant:', error);\n      throw error;\n    }\n  },\n\n  // Update applicant\n  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {\n    try {\n      console.log('Updating applicant:', id, data);\n      \n      const response = await apiClient.put(`/applicants/${id}`, data);\n      \n      console.log('Applicant updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating applicant:', error);\n      throw error;\n    }\n  },\n\n  // Get applicants by user (if user can have multiple applicants)\n  async getApplicantsByUser(): Promise<Applicant[]> {\n    try {\n      const response = await apiClient.get('/applicants/by-user');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user applicants:', error);\n      throw error;\n    }\n  },\n\n  // Delete applicant\n  async deleteApplicant(id: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting applicant:', error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAkBO,MAAM,mBAAmB;IAC9B,uBAAuB;IACvB,MAAM,iBAAgB,IAAyB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,eAAe;YACrD,oCAAoC;YACpC,IAAI,SAAS,IAAI,EAAE;gBACjB,mDAAmD;gBACnD,IAAI,SAAS,IAAI,CAAC,OAAO,KAAK,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC7D,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAEK,IAAI,SAAS,IAAI,CAAC,YAAY,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACvD,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB,OAEK;oBACH,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAmB,OAAe,UAAU;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAkC;QAClE,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAEvC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAE1D,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;YAC5D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gEAAgE;IAChE,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info\r\n      const applicantRequiredFields = [\r\n        'applicant_type', 'first_name', 'last_name', 'email', 'phone',\r\n        'national_id', 'date_of_birth', 'nationality', 'gender',\r\n        'postal_address', 'physical_address', 'city', 'district'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Phone validation\r\n      if (data.phone && !/^(\\+265|0)?[1-9]\\d{7,8}$/.test(data.phone)) {\r\n        errors.phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,qCAAqC;YACrC,MAAM,0BAA0B;gBAC9B;gBAAkB;gBAAc;gBAAa;gBAAS;gBACtD;gBAAe;gBAAiB;gBAAe;gBAC/C;gBAAkB;gBAAoB;gBAAQ;aAC/C;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\n * License Type Step Configuration System\n * Defines which form steps are required for each license type\n */\n\nexport interface StepConfig {\n  id: string;\n  name: string;\n  component: string;\n  route: string;\n  required: boolean;\n  description: string;\n  estimatedTime: string; // in minutes\n}\n\nexport interface LicenseTypeStepConfig {\n  licenseTypeId: string;\n  name: string;\n  description: string;\n  steps: StepConfig[];\n  estimatedTotalTime: string;\n  requirements: string[];\n}\n\n// Base steps that can be used across license types\nconst BASE_STEPS: Record<string, StepConfig> = {\n  applicantInfo: {\n    id: 'applicant-info',\n    name: 'Applicant Information',\n    component: 'ApplicantInfo',\n    route: 'applicant-info',\n    required: true,\n    description: 'Personal or company information of the applicant',\n    estimatedTime: '5'\n  },\n  companyProfile: {\n    id: 'company-profile',\n    name: 'Company Profile',\n    component: 'CompanyProfile',\n    route: 'company-profile',\n    required: true,\n    description: 'Company structure, shareholders, and directors',\n    estimatedTime: '10'\n  },\n  management: {\n    id: 'management',\n    name: 'Management Structure',\n    component: 'Management',\n    route: 'management',\n    required: false,\n    description: 'Management team and organizational structure',\n    estimatedTime: '8'\n  },\n  professionalServices: {\n    id: 'professional-services',\n    name: 'Professional Services',\n    component: 'ProfessionalServices',\n    route: 'professional-services',\n    required: false,\n    description: 'External consultants and service providers',\n    estimatedTime: '6'\n  },\n  businessInfo: {\n    id: 'business-info',\n    name: 'Business Information',\n    component: 'BusinessInfo',\n    route: 'business-info',\n    required: true,\n    description: 'Business description and operational plan',\n    estimatedTime: '7'\n  },\n  serviceScope: {\n    id: 'service-scope',\n    name: 'Service Scope',\n    component: 'ServiceScope',\n    route: 'service-scope',\n    required: true,\n    description: 'Services offered and geographic coverage',\n    estimatedTime: '8'\n  },\n  businessPlan: {\n    id: 'business-plan',\n    name: 'Business Plan',\n    component: 'BusinessPlan',\n    route: 'business-plan',\n    required: true,\n    description: 'Market analysis and financial projections',\n    estimatedTime: '15'\n  },\n  legalHistory: {\n    id: 'legal-history',\n    name: 'Legal History',\n    component: 'LegalHistory',\n    route: 'legal-history',\n    required: true,\n    description: 'Legal compliance and regulatory history',\n    estimatedTime: '5'\n  },\n  reviewSubmit: {\n    id: 'review-submit',\n    name: 'Review & Submit',\n    component: 'ReviewSubmit',\n    route: 'review-submit',\n    required: true,\n    description: 'Review all information and submit application',\n    estimatedTime: '10'\n  }\n};\n\n// License type specific configurations\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\n  telecommunications: {\n    licenseTypeId: 'telecommunications',\n    name: 'Telecommunications License',\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Tax compliance certificate',\n      'Technical specifications',\n      'Financial statements',\n      'Management CVs',\n      'Network coverage plans'\n    ]\n  },\n\n  postal_services: {\n    licenseTypeId: 'postal_services',\n    name: 'Postal Services License',\n    description: 'License for postal and courier service providers',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '42 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Fleet inventory',\n      'Service coverage map',\n      'Insurance certificates',\n      'Premises documentation'\n    ]\n  },\n\n  standards_compliance: {\n    licenseTypeId: 'standards_compliance',\n    name: 'Standards Compliance License',\n    description: 'License for standards compliance and certification services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '59 minutes',\n    requirements: [\n      'Accreditation certificates',\n      'Technical competency proof',\n      'Quality management system',\n      'Laboratory facilities documentation',\n      'Staff qualifications'\n    ]\n  },\n\n  broadcasting: {\n    licenseTypeId: 'broadcasting',\n    name: 'Broadcasting License',\n    description: 'License for radio and television broadcasting services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '63 minutes',\n    requirements: [\n      'Broadcasting equipment specifications',\n      'Content programming plan',\n      'Studio facility documentation',\n      'Transmission coverage maps',\n      'Local content compliance plan'\n    ]\n  },\n\n  spectrum_management: {\n    licenseTypeId: 'spectrum_management',\n    name: 'Spectrum Management License',\n    description: 'License for radio frequency spectrum management and allocation',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Spectrum usage plan',\n      'Technical interference analysis',\n      'Equipment type approval',\n      'Frequency coordination agreements',\n      'Monitoring capabilities documentation'\n    ]\n  },\n\n  clf: {\n    licenseTypeId: 'clf',\n    name: 'CLF License',\n    description: 'Consumer Lending and Finance license',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '50 minutes',\n    requirements: [\n      'Financial institution license',\n      'Capital adequacy documentation',\n      'Risk management framework',\n      'Consumer protection policies',\n      'Anti-money laundering procedures'\n    ]\n  }\n};\n\n// License type name to config key mapping\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\n  'telecommunications': 'telecommunications',\n  'postal services': 'postal_services',\n  'postal_services': 'postal_services',\n  'standards compliance': 'standards_compliance',\n  'standards_compliance': 'standards_compliance',\n  'broadcasting': 'broadcasting',\n  'spectrum management': 'spectrum_management',\n  'spectrum_management': 'spectrum_management',\n  'clf': 'clf',\n  'consumer lending and finance': 'clf'\n};\n\n// Helper functions\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig | null => {\n\n  // Check if licenseTypeId is valid\n  if (!licenseTypeId || typeof licenseTypeId !== 'string') {\n    console.log('Invalid licenseTypeId provided:', licenseTypeId);\n    return null;\n  }\n\n  // First try direct lookup\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\n  if (config) {\n    console.log('Found config via direct lookup:', config.name);\n    return config;\n  }\n\n  // Try normalized lookup\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\n  if (config) {\n    console.log('Found config via normalized lookup:', config.name);\n    return config;\n  }\n\n  // Try name mapping\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\n  if (mappedKey) {\n    console.log('Found config via name mapping:', mappedKey);\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\n  }\n\n  // If licenseTypeId looks like a UUID, try to get the code from license types\n  if (isUUID(licenseTypeId)) {\n    console.log('Detected UUID, trying to get code...');\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\n    console.log('Got code from UUID:', code);\n    if (code) {\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\n      if (foundConfig) {\n        console.log('Found config via UUID mapping:', foundConfig.name);\n        return foundConfig;\n      }\n    }\n  }\n\n  console.log('No config found for license type:', licenseTypeId);\n  return null;\n};\n\n// Helper function to check if a string is a UUID\nconst isUUID = (str: string): boolean => {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(str);\n};\n\n// Helper function to get license type code from UUID\n// This will be populated by the license type service\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\n\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\n  licenseTypeUUIDToCodeMap = map;\n};\n\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\n  return licenseTypeUUIDToCodeMap[uuid] || null;\n};\n\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  return config.steps.find(step => step.route === stepRoute) || null;\n};\n\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;\n  \n  return config.steps[stepIndex];\n};\n\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return -1;\n  \n  return config.steps.findIndex(step => step.route === stepRoute);\n};\n\nexport const getTotalSteps = (licenseTypeId: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.length : 0;\n};\n\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => step.required) : [];\n};\n\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => !step.required) : [];\n};\n\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return 0;\n  \n  const totalSteps = config.steps.length;\n  const completed = completedSteps.length;\n  \n  return Math.round((completed / totalSteps) * 100);\n};\n\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\n  \n  return config.steps[currentIndex + 1];\n};\n\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex <= 0) return null;\n  \n  return config.steps[currentIndex - 1];\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,gBAAgB;QACd,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAGO,MAAM,2BAA2B,CAAC;IAEvC,kCAAkC;IAClC,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,mCAAmC,OAAO,IAAI;QAC1D,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,uCAAuC,OAAO,IAAI;QAC9D,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,2BAA2B;QACxC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,kCAAkC,YAAY,IAAI;gBAC9D,OAAO;YACT;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,qCAAqC;IACjD,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,UAAU,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAEzE,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO,CAAC;IAErB,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,GAAG;AACxC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,EAAE;AACjE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,IAAI,EAAE;AAClE;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { processApiResponse } from './authUtils';\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 15000, // Increased timeout for better reliability\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 15000, // Increased timeout for better reliability\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to auth client (only in development)\r\ncustomerAuthApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Customer Auth API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  // // Authentication endpoints (copied from working staff portal)\r\n  // async login(credentials: { email: string; password: string }) {\r\n  //   try {\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Starting login request with credentials:', { email: credentials.email });\r\n  //       console.log('CustomerAPI: API Base URL:', API_BASE_URL);\r\n  //       console.log('CustomerAPI: Full request URL:', `${API_BASE_URL}/auth/login`);\r\n  //       console.log('CustomerAPI: Request payload:', { email: credentials.email, password: '***' });\r\n  //     }\r\n      \r\n  //     // Make the request\r\n  //     const response = await customerAuthApiClient.post('/login', credentials);\r\n\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Raw response received:', {\r\n  //         status: response.status,\r\n  //         statusText: response.statusText,\r\n  //         headers: response.headers,\r\n  //         data: processApiResponse(response),\r\n  //         dataType: typeof processApiResponse(response),\r\n  //         dataKeys: processApiResponse(response) ? Object.keys(processApiResponse(response)) : 'NO_DATA'\r\n  //       });\r\n  //     }\r\n\r\n  //     // Check if the response has the expected structure\r\n  //     // Backend returns data wrapped in a response envelope: { success, message, data, timestamp, path, statusCode }\r\n  //     if (!processApiResponse(response) || !processApiResponse(response).data) {\r\n  //       console.error('CustomerAPI: Invalid response structure:', processApiResponse(response));\r\n  //       throw new Error('Invalid response from server');\r\n  //     }\r\n\r\n  //     const authData = processApiResponse(response).data;\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Extracted auth data:', {\r\n  //         authData: authData,\r\n  //         authDataType: typeof authData,\r\n  //         authDataKeys: authData ? Object.keys(authData) : 'NO_AUTH_DATA',\r\n  //         hasAccessToken: authData ? 'access_token' in authData : false,\r\n  //         hasUser: authData ? 'user' in authData : false,\r\n  //         accessTokenValue: authData?.access_token,\r\n  //         userValue: authData?.user\r\n  //       });\r\n  //     }\r\n\r\n  //     // Check if the auth data is empty (which indicates an error)\r\n  //     if (!authData || Object.keys(authData).length === 0) {\r\n  //       console.error('CustomerAPI: Empty auth data received');\r\n  //       throw new Error('Authentication failed - invalid credentials');\r\n  //     }\r\n\r\n  //     // Validate that we have the required fields\r\n  //     if (!authData.access_token || !authData.user) {\r\n  //       console.error('CustomerAPI: Missing required fields in response:', {\r\n  //         authData: authData,\r\n  //         hasToken: !!authData.access_token,\r\n  //         hasUser: !!authData.user,\r\n  //         tokenValue: authData.access_token,\r\n  //         userValue: authData.user,\r\n  //         allKeys: Object.keys(authData || {})\r\n  //       });\r\n  //       throw new Error('Authentication failed - incomplete response');\r\n  //     }\r\n\r\n  //     // Map backend field names to frontend expected format\r\n  //     // Backend response structure: { access_token, user: { user_id, first_name, last_name, email, roles, ... } }\r\n  //     const mappedAuthData = {\r\n  //       access_token: authData.access_token,\r\n  //       user: {\r\n  //         id: authData.user.user_id,\r\n  //         firstName: authData.user.first_name,\r\n  //         lastName: authData.user.last_name,\r\n  //         email: authData.user.email,\r\n  //         roles: authData.user.roles || [],\r\n  //         isAdmin: (authData.user.roles || []).includes('administrator'),\r\n  //         profileImage: authData.user.profile_image,\r\n  //         createdAt: authData.user.created_at || new Date().toISOString(),\r\n  //         lastLogin: authData.user.last_login,\r\n  //         organizationName: authData.user.organization_name,\r\n  //         two_factor_enabled: authData.user.two_factor_enabled\r\n  //       }\r\n  //     };\r\n\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Mapped auth data:', mappedAuthData);\r\n  //     }\r\n  //     return mappedAuthData;\r\n  //   } catch (error) {\r\n  //     const isAxiosError = error && typeof error === 'object' && 'isAxiosError' in error;\r\n  //     const axiosError = isAxiosError ? error as AxiosError : null;\r\n      \r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.error('CustomerAPI: Login error details:', {\r\n  //         error: error,\r\n  //         errorType: typeof error,\r\n  //         errorConstructor: error?.constructor?.name,\r\n  //         errorMessage: error instanceof Error ? error.message : String(error),\r\n  //         errorStack: error instanceof Error ? error.stack : undefined,\r\n  //         isAxiosError: isAxiosError,\r\n  //         responseStatus: axiosError?.response?.status,\r\n  //         responseStatusText: axiosError?.response?.statusText,\r\n  //         responseData: axiosError?.response?.data,\r\n  //         requestURL: axiosError?.config?.url,\r\n  //         requestMethod: axiosError?.config?.method,\r\n  //         requestData: axiosError?.config?.data,\r\n  //         requestHeaders: axiosError?.config?.headers\r\n  //       });\r\n  //     }\r\n  //     throw error;\r\n  //   }\r\n  // }\r\n\r\n  // async register(userData: {\r\n  //   firstName: string;\r\n  //   lastName: string;\r\n  //   email: string;\r\n  //   password: string;\r\n  //   organizationName?: string;\r\n  // }) {\r\n  //   // Map frontend field names to backend expected format\r\n  //   const backendUserData = {\r\n  //     first_name: userData.firstName,\r\n  //     last_name: userData.lastName,\r\n  //     email: userData.email,\r\n  //     password: userData.password,\r\n  //     organization_name: userData.organizationName\r\n  //   };\r\n\r\n  //   const response = await customerAuthApiClient.post('/register', backendUserData);\r\n\r\n  //   // Handle response structure consistently with login\r\n  //   if (processApiResponse(response)?.data) {\r\n  //     const authData = processApiResponse(response).data;\r\n      \r\n  //     // Map backend field names to frontend expected format\r\n  //     const mappedAuthData = {\r\n  //       access_token: authData.access_token,\r\n  //       user: {\r\n  //         id: authData.user.user_id,\r\n  //         firstName: authData.user.first_name,\r\n  //         lastName: authData.user.last_name,\r\n  //         email: authData.user.email,\r\n  //         roles: authData.user.roles || [],\r\n  //         isAdmin: (authData.user.roles || []).includes('administrator'),\r\n  //         profileImage: authData.user.profile_image,\r\n  //         createdAt: authData.user.created_at || new Date().toISOString(),\r\n  //         lastLogin: authData.user.last_login,\r\n  //         organizationName: authData.user.organization_name\r\n  //       }\r\n  //     };\r\n      \r\n  //     return mappedAuthData;\r\n  //   }\r\n\r\n  //   // If no nested data property, return direct response\r\n  //   return processApiResponse(response);\r\n  // }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (processApiResponse(response)?.data) {\r\n      const authData = processApiResponse(response).data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {\r\n    const response = await customerAuthApiClient.post('/setup-2fa', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return processApiResponse(response);\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const response = await this.api.put('/address/edit', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Payment endpoints\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n// Export types\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  errors?: string[] | { [key: string]: string[] } | string;\r\n}\r\n\r\nexport interface PaginatedResponse<T = unknown> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport interface License {\r\n  id: string;\r\n  licenseNumber: string;\r\n  type: string;\r\n  status: 'active' | 'expired' | 'suspended' | 'pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Application {\r\n  id: string;\r\n  applicationNumber: string;\r\n  type: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  organizationName?: string;\r\n  roles: string[];\r\n  isAdmin: boolean;\r\n  profileImage?: string;\r\n  createdAt: string;\r\n  lastLogin?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface ProfileUpdateData {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  organizationName?: string;\r\n  profileImage?: string;\r\n}\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  address_origin: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface LicenseApplicationData {\r\n  type: string;\r\n  organizationName: string;\r\n  description?: string;\r\n  contactEmail?: string;\r\n  contactPhone?: string;\r\n  businessAddress?: string;\r\n  businessType?: string;\r\n  requestedStartDate?: string;\r\n  additionalDocuments?: string[];\r\n  notes?: string;\r\n}\r\n\r\nexport interface PaymentCreateData {\r\n  amount: number;\r\n  currency: string;\r\n  dueDate: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface TenderPaymentData {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface ComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  attachments?: File[];\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,sBAAsB,YAAY,CAAC,OAAO,CAAC,GAAG,CAC5C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,8BAA8B;YACxC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oCAAoC;IAClD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yCAAyC;QACzC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,iEAAiE;IACjE,kEAAkE;IAClE,UAAU;IACV,oDAAoD;IACpD,4GAA4G;IAC5G,iEAAiE;IACjE,qFAAqF;IACrF,qGAAqG;IACrG,QAAQ;IAER,0BAA0B;IAC1B,gFAAgF;IAEhF,oDAAoD;IACpD,6DAA6D;IAC7D,mCAAmC;IACnC,2CAA2C;IAC3C,qCAAqC;IACrC,8CAA8C;IAC9C,yDAAyD;IACzD,yGAAyG;IACzG,YAAY;IACZ,QAAQ;IAER,0DAA0D;IAC1D,sHAAsH;IACtH,iFAAiF;IACjF,iGAAiG;IACjG,yDAAyD;IACzD,QAAQ;IAER,0DAA0D;IAC1D,oDAAoD;IACpD,2DAA2D;IAC3D,8BAA8B;IAC9B,yCAAyC;IACzC,2EAA2E;IAC3E,yEAAyE;IACzE,0DAA0D;IAC1D,oDAAoD;IACpD,oCAAoC;IACpC,YAAY;IACZ,QAAQ;IAER,oEAAoE;IACpE,6DAA6D;IAC7D,gEAAgE;IAChE,wEAAwE;IACxE,QAAQ;IAER,mDAAmD;IACnD,sDAAsD;IACtD,6EAA6E;IAC7E,8BAA8B;IAC9B,6CAA6C;IAC7C,oCAAoC;IACpC,6CAA6C;IAC7C,oCAAoC;IACpC,+CAA+C;IAC/C,YAAY;IACZ,wEAAwE;IACxE,QAAQ;IAER,6DAA6D;IAC7D,mHAAmH;IACnH,+BAA+B;IAC/B,6CAA6C;IAC7C,gBAAgB;IAChB,qCAAqC;IACrC,+CAA+C;IAC/C,6CAA6C;IAC7C,sCAAsC;IACtC,4CAA4C;IAC5C,0EAA0E;IAC1E,qDAAqD;IACrD,2EAA2E;IAC3E,+CAA+C;IAC/C,6DAA6D;IAC7D,+DAA+D;IAC/D,UAAU;IACV,SAAS;IAET,oDAAoD;IACpD,uEAAuE;IACvE,QAAQ;IACR,6BAA6B;IAC7B,sBAAsB;IACtB,0FAA0F;IAC1F,oEAAoE;IAEpE,oDAAoD;IACpD,6DAA6D;IAC7D,wBAAwB;IACxB,mCAAmC;IACnC,sDAAsD;IACtD,gFAAgF;IAChF,wEAAwE;IACxE,sCAAsC;IACtC,wDAAwD;IACxD,gEAAgE;IAChE,oDAAoD;IACpD,+CAA+C;IAC/C,qDAAqD;IACrD,iDAAiD;IACjD,sDAAsD;IACtD,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,MAAM;IACN,IAAI;IAEJ,6BAA6B;IAC7B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,sBAAsB;IACtB,+BAA+B;IAC/B,OAAO;IACP,2DAA2D;IAC3D,8BAA8B;IAC9B,sCAAsC;IACtC,oCAAoC;IACpC,6BAA6B;IAC7B,mCAAmC;IACnC,mDAAmD;IACnD,OAAO;IAEP,qFAAqF;IAErF,yDAAyD;IACzD,8CAA8C;IAC9C,0DAA0D;IAE1D,6DAA6D;IAC7D,+BAA+B;IAC/B,6CAA6C;IAC7C,gBAAgB;IAChB,qCAAqC;IACrC,+CAA+C;IAC/C,6CAA6C;IAC7C,sCAAsC;IACtC,4CAA4C;IAC5C,0EAA0E;IAC1E,qDAAqD;IACrD,2EAA2E;IAC3E,+CAA+C;IAC/C,4DAA4D;IAC5D,UAAU;IACV,SAAS;IAET,6BAA6B;IAC7B,MAAM;IAEN,0DAA0D;IAC1D,yCAAyC;IACzC,IAAI;IAEJ,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;YACtC,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAElD,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,IAA+C,EAAE;QACxE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAyD,EAAE;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FileUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useRef } from 'react';\r\n\r\ninterface FileUploadProps {\r\n  id: string;\r\n  label: string;\r\n  accept?: string;\r\n  maxSize?: number; // in MB\r\n  required?: boolean;\r\n  value?: File | null;\r\n  onChange: (file: File | null) => void;\r\n  description?: string;\r\n  className?: string;\r\n}\r\n\r\nconst FileUpload: React.FC<FileUploadProps> = ({\r\n  id,\r\n  label,\r\n  accept = '.pdf',\r\n  maxSize = 10,\r\n  required = false,\r\n  value,\r\n  onChange,\r\n  description,\r\n  className = ''\r\n}) => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0] || null;\r\n    \r\n    if (file) {\r\n      // Check file size\r\n      if (file.size > maxSize * 1024 * 1024) {\r\n        alert(`File size must be less than ${maxSize}MB`);\r\n        return;\r\n      }\r\n      \r\n      // Check file type\r\n      if (accept && !accept.split(',').some(type => file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {\r\n        alert(`File type must be: ${accept}`);\r\n        return;\r\n      }\r\n    }\r\n    \r\n    onChange(file);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleRemove = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label htmlFor={id} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div \r\n        onClick={handleClick}\r\n        className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\"\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={accept}\r\n          onChange={handleFileChange}\r\n          className=\"hidden\"\r\n          id={id}\r\n          required={required}\r\n        />\r\n        \r\n        {value ? (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-file-text-line text-3xl text-green-500\"></i>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-green-600 dark:text-green-400\">\r\n              {value.name}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {(value.size / 1024 / 1024).toFixed(2)} MB\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleRemove}\r\n              className=\"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors\"\r\n            >\r\n              <i className=\"ri-delete-bin-line mr-1\"></i>\r\n              Remove\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-upload-cloud-2-line text-3xl text-gray-400\"></i>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Click to upload {label.toLowerCase()}\r\n            </p>\r\n            {description && (\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                {description}\r\n              </p>\r\n            )}\r\n            <div className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\">\r\n              <i className=\"ri-folder-upload-line mr-2\"></i>\r\n              Choose File\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {description && !value && (\r\n        <p className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\r\n          {description}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,MAAM,aAAwC,CAAC,EAC7C,EAAE,EACF,KAAK,EACL,SAAS,MAAM,EACf,UAAU,EAAE,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QAExC,IAAI,MAAM;YACR,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;gBACrC,MAAM,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;gBAChD;YACF;YAEA,kBAAkB;YAClB,IAAI,UAAU,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,OAAO;gBAC7G,MAAM,CAAC,mBAAmB,EAAE,QAAQ;gBACpC;YACF;QACF;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;wBACV,IAAI;wBACJ,UAAU;;;;;;oBAGX,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;0CACV,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAEzC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA8B;;;;;;;;;;;;6CAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;;oCAA2C;oCACrC,MAAM,WAAW;;;;;;;4BAEnC,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;oCAAiC;;;;;;;;;;;;;;;;;;;YAOrD,eAAe,CAAC,uBACf,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 2952, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FormField.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  required?: boolean;\r\n  error?: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  description?: string;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  children,\r\n  className = '',\r\n  description\r\n}) => {\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      {description && (\r\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">\r\n          {description}\r\n        </p>\r\n      )}\r\n      \r\n      {children}\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,YAAsC,CAAC,EAC3C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,EACZ;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;YAGrD,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ;YAEA,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 3021, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface Step {\r\n  id: number;\r\n  label: string;\r\n  completed?: boolean;\r\n}\r\n\r\ninterface ProgressIndicatorProps {\r\n  steps: Step[];\r\n  currentStep: number;\r\n  className?: string;\r\n}\r\n\r\nconst ProgressIndicator: React.FC<ProgressIndicatorProps> = ({\r\n  steps,\r\n  currentStep,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={className}>\r\n      <div className=\"flex items-center justify-between\">\r\n        {steps.map((step, index) => (\r\n          <div key={step.id} className=\"flex items-center\">\r\n            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${\r\n              step.id === currentStep\r\n                ? 'bg-primary text-white'\r\n                : step.id < currentStep || step.completed\r\n                ? 'bg-green-500 text-white'\r\n                : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'\r\n            }`}>\r\n              {step.id < currentStep || step.completed ? (\r\n                <i className=\"ri-check-line\"></i>\r\n              ) : (\r\n                step.id\r\n              )}\r\n            </div>\r\n            {index < steps.length - 1 && (\r\n              <div className={`w-12 h-0.5 mx-2 transition-colors ${\r\n                step.id < currentStep || step.completed ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'\r\n              }`}></div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n        {steps.map((step) => (\r\n          <span key={step.id} className=\"text-center max-w-20 truncate\">\r\n            {step.label}\r\n          </span>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgressIndicator;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAgBA,MAAM,oBAAsD,CAAC,EAC3D,KAAK,EACL,WAAW,EACX,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAW,CAAC,4FAA4F,EAC3G,KAAK,EAAE,KAAK,cACR,0BACA,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,GACvC,4BACA,iEACJ;0CACC,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,iBACtC,8OAAC;oCAAE,WAAU;;;;;2CAEb,KAAK,EAAE;;;;;;4BAGV,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAW,CAAC,kCAAkC,EACjD,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,GAAG,iBAAiB,gCAC3D;;;;;;;uBAjBI,KAAK,EAAE;;;;;;;;;;0BAuBrB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAmB,WAAU;kCAC3B,KAAK,KAAK;uBADF,KAAK,EAAE;;;;;;;;;;;;;;;;AAO5B;uCAEe", "debugId": null}}, {"offset": {"line": 3097, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/CountryDropdown.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\n\r\n// Country list for nationality dropdown\r\nconst COUNTRIES = [\r\n  'Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Antigua and Barbuda', 'Argentina', 'Armenia', 'Australia', 'Austria',\r\n  'Azerbaijan', 'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan',\r\n  'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso', 'Burundi', 'Cabo Verde', 'Cambodia',\r\n  'Cameroon', 'Canada', 'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia', 'Comoros', 'Congo (Congo-Brazzaville)', 'Congo (Democratic Republic)',\r\n  'Costa Rica', 'Croatia', 'Cuba', 'Cyprus', 'Czechia (Czech Republic)', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic', 'Ecuador',\r\n  'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', '<PERSON><PERSON><PERSON><PERSON>', 'Ethiopia', 'Fiji', 'Finland', 'France',\r\n  'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau',\r\n  'Guyana', 'Haiti', 'Holy See', 'Honduras', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq',\r\n  'Ireland', 'Israel', 'Italy', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Kiribati', 'Kuwait',\r\n  'Kyrgyzstan', 'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania', 'Luxembourg',\r\n  'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands', 'Mauritania', 'Mauritius', 'Mexico',\r\n  'Micronesia', 'Moldova', 'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar (formerly Burma)', 'Namibia', 'Nauru',\r\n  'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria', 'North Korea', 'North Macedonia', 'Norway', 'Oman',\r\n  'Pakistan', 'Palau', 'Palestine State', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal',\r\n  'Qatar', 'Romania', 'Russia', 'Rwanda', 'Saint Kitts and Nevis', 'Saint Lucia', 'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe',\r\n  'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia', 'Solomon Islands', 'Somalia',\r\n  'South Africa', 'South Korea', 'South Sudan', 'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Sweden', 'Switzerland', 'Syria',\r\n  'Tajikistan', 'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tonga', 'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan',\r\n  'Tuvalu', 'Uganda', 'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States of America', 'Uruguay', 'Uzbekistan', 'Vanuatu', 'Venezuela',\r\n  'Vietnam', 'Yemen', 'Zambia', 'Zimbabwe'\r\n];\r\n\r\ninterface CountryDropdownProps {\r\n  label?: string;\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  id?: string;\r\n  name?: string;\r\n}\r\n\r\nconst CountryDropdown: React.FC<CountryDropdownProps> = ({ \r\n  label,\r\n  value, \r\n  onChange, \r\n  placeholder = \"Select or type country name\", \r\n  required = false,\r\n  className = \"\",\r\n  disabled = false,\r\n  error = \"\",\r\n  id,\r\n  name\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filteredCountries, setFilteredCountries] = useState(COUNTRIES);\r\n  const [activeIndex, setActiveIndex] = useState(-1);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const listboxId = `${id || 'country-dropdown'}-listbox`;\r\n\r\n  // Filter countries based on search term\r\n  useEffect(() => {\r\n    const filtered = COUNTRIES.filter(country =>\r\n      country.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n    setFilteredCountries(filtered);\r\n    setActiveIndex(-1); // Reset active index when filtering\r\n  }, [searchTerm]);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setIsOpen(false);\r\n        setActiveIndex(-1);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const inputValue = e.target.value;\r\n    setSearchTerm(inputValue);\r\n    onChange(inputValue);\r\n    if (!disabled) {\r\n      setIsOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleCountrySelect = (country: string) => {\r\n    onChange(country);\r\n    setSearchTerm(country);\r\n    setIsOpen(false);\r\n    setActiveIndex(-1);\r\n  };\r\n\r\n  const handleInputFocus = () => {\r\n    if (!disabled) {\r\n      setIsOpen(true);\r\n      setSearchTerm(value);\r\n    }\r\n  };\r\n\r\n  const handleInputBlur = () => {\r\n    // Delay hiding dropdown to allow for country selection\r\n    setTimeout(() => {\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n    }, 150);\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === 'Escape') {\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n      inputRef.current?.blur();\r\n    } else if (e.key === 'ArrowDown') {\r\n      e.preventDefault();\r\n      if (!isOpen) {\r\n        setIsOpen(true);\r\n      }\r\n      setActiveIndex(prev =>\r\n        prev < filteredCountries.length - 1 ? prev + 1 : 0\r\n      );\r\n    } else if (e.key === 'ArrowUp') {\r\n      e.preventDefault();\r\n      if (!isOpen) {\r\n        setIsOpen(true);\r\n      }\r\n      setActiveIndex(prev =>\r\n        prev > 0 ? prev - 1 : filteredCountries.length - 1\r\n      );\r\n    } else if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      if (isOpen && activeIndex >= 0 && filteredCountries[activeIndex]) {\r\n        handleCountrySelect(filteredCountries[activeIndex]);\r\n      }\r\n    } else if (e.key === 'Tab') {\r\n      // Allow tab to close dropdown and move to next element\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n    }\r\n  };\r\n\r\n  // Base input styling with proper text visibility\r\n  const baseInputClass = `w-full px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    className.includes('text-sm') ? 'py-1.5 text-sm' : 'py-2'\r\n  }`;\r\n  \r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  }`;\r\n\r\n  const labelClass = \"block mb-1 font-medium text-gray-700 dark:text-gray-200\";\r\n\r\n  return (\r\n    <div className={`relative ${className}`} ref={dropdownRef}>\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        id={id}\r\n        name={name}\r\n        value={isOpen ? searchTerm : value}\r\n        onChange={handleInputChange}\r\n        onFocus={handleInputFocus}\r\n        onBlur={handleInputBlur}\r\n        onKeyDown={handleKeyDown}\r\n        className={inputClass}\r\n        placeholder={placeholder}\r\n        required={required}\r\n        disabled={disabled}\r\n        autoComplete=\"country\"\r\n        aria-expanded={isOpen}\r\n        aria-haspopup=\"listbox\"\r\n        aria-controls={isOpen ? listboxId : undefined}\r\n        aria-activedescendant={activeIndex >= 0 && isOpen ? `${listboxId}-option-${activeIndex}` : undefined}\r\n        role=\"combobox\"\r\n        aria-autocomplete=\"list\"\r\n        aria-describedby={error ? `${id}-error` : undefined}\r\n      />\r\n      \r\n      {/* Dropdown icon */}\r\n      <div className={`absolute inset-y-0 right-0 top-7 flex items-center pr-3 pointer-events-none ${disabled ? 'opacity-50' : ''}`}>\r\n        <i className={`ri-arrow-down-s-line text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}></i>\r\n      </div>\r\n\r\n      {/* Dropdown list */}\r\n      {isOpen && !disabled && (\r\n        <div \r\n          id={listboxId}\r\n          className=\"absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto\"\r\n          role=\"listbox\"\r\n          aria-label=\"Country options\"\r\n        >\r\n          {filteredCountries.length > 0 ? (\r\n            filteredCountries.map((country, index) => (\r\n              <div\r\n                key={country}\r\n                id={`${listboxId}-option-${index}`}\r\n                className={`px-3 py-2 cursor-pointer text-sm transition-colors duration-150 ${\r\n                  index === activeIndex\r\n                    ? 'font-semibold text-red-600 dark:text-red-300 bg-gray-100 dark:bg-gray-800'\r\n                    : 'font-normal text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600'\r\n                }`}\r\n                onClick={() => handleCountrySelect(country)}\r\n                onMouseDown={(e) => e.preventDefault()} // Prevent input blur\r\n                onMouseEnter={() => setActiveIndex(index)}\r\n                role=\"option\"\r\n                aria-selected={value === country}\r\n              >\r\n                {country}\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div className=\"px-3 py-2 text-sm text-gray-500 dark:text-gray-400\" role=\"option\" aria-disabled=\"true\" aria-selected=\"false\">\r\n              No countries found\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <p id={`${id}-error`} className=\"mt-1 text-sm text-red-600 dark:text-red-400\" role=\"alert\">\r\n          {error}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CountryDropdown;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,wCAAwC;AACxC,MAAM,YAAY;IAChB;IAAe;IAAW;IAAW;IAAW;IAAU;IAAuB;IAAa;IAAW;IAAa;IACtH;IAAc;IAAW;IAAW;IAAc;IAAY;IAAW;IAAW;IAAU;IAAS;IACvG;IAAW;IAA0B;IAAY;IAAU;IAAU;IAAY;IAAgB;IAAW;IAAc;IAC1H;IAAY;IAAU;IAA4B;IAAQ;IAAS;IAAS;IAAY;IAAW;IAA6B;IAChI;IAAc;IAAW;IAAQ;IAAU;IAA4B;IAAW;IAAY;IAAY;IAAsB;IAChI;IAAS;IAAe;IAAqB;IAAW;IAAW;IAAY;IAAY;IAAQ;IAAW;IAC9G;IAAS;IAAU;IAAW;IAAW;IAAS;IAAU;IAAW;IAAa;IAAU;IAC9F;IAAU;IAAS;IAAY;IAAY;IAAW;IAAW;IAAS;IAAa;IAAQ;IAC/F;IAAW;IAAU;IAAS;IAAW;IAAS;IAAU;IAAc;IAAS;IAAY;IAC/F;IAAc;IAAQ;IAAU;IAAW;IAAW;IAAW;IAAS;IAAiB;IAAa;IACxG;IAAc;IAAU;IAAY;IAAY;IAAQ;IAAS;IAAoB;IAAc;IAAa;IAChH;IAAc;IAAW;IAAU;IAAY;IAAc;IAAW;IAAc;IAA4B;IAAW;IAC7H;IAAS;IAAe;IAAe;IAAa;IAAS;IAAW;IAAe;IAAmB;IAAU;IACpH;IAAY;IAAS;IAAmB;IAAU;IAAoB;IAAY;IAAQ;IAAe;IAAU;IACnH;IAAS;IAAW;IAAU;IAAU;IAAyB;IAAe;IAAoC;IAAS;IAAc;IAC3I;IAAgB;IAAW;IAAU;IAAc;IAAgB;IAAa;IAAY;IAAY;IAAmB;IAC3H;IAAgB;IAAe;IAAe;IAAS;IAAa;IAAS;IAAY;IAAU;IAAe;IAClH;IAAc;IAAY;IAAY;IAAe;IAAQ;IAAS;IAAuB;IAAW;IAAU;IAClH;IAAU;IAAU;IAAW;IAAwB;IAAkB;IAA4B;IAAW;IAAc;IAAW;IACzI;IAAW;IAAS;IAAU;CAC/B;AAeD,MAAM,kBAAkD,CAAC,EACvD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,cAAc,6BAA6B,EAC3C,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,QAAQ,EAAE,EACV,EAAE,EACF,IAAI,EACL;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,GAAG,MAAM,mBAAmB,QAAQ,CAAC;IAEvD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,UAChC,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEvD,qBAAqB;QACrB,eAAe,CAAC,IAAI,oCAAoC;IAC1D,GAAG;QAAC;KAAW;IAEf,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;gBACV,eAAe,CAAC;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK;QACjC,cAAc;QACd,SAAS;QACT,IAAI,CAAC,UAAU;YACb,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,SAAS;QACT,cAAc;QACd,UAAU;QACV,eAAe,CAAC;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;YACb,UAAU;YACV,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,uDAAuD;QACvD,WAAW;YACT,UAAU;YACV,eAAe,CAAC;QAClB,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,UAAU;YACV,eAAe,CAAC;YAChB,SAAS,OAAO,EAAE;QACpB,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;YAChC,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;gBACX,UAAU;YACZ;YACA,eAAe,CAAA,OACb,OAAO,kBAAkB,MAAM,GAAG,IAAI,OAAO,IAAI;QAErD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;YAC9B,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;gBACX,UAAU;YACZ;YACA,eAAe,CAAA,OACb,OAAO,IAAI,OAAO,IAAI,kBAAkB,MAAM,GAAG;QAErD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;YAC5B,EAAE,cAAc;YAChB,IAAI,UAAU,eAAe,KAAK,iBAAiB,CAAC,YAAY,EAAE;gBAChE,oBAAoB,iBAAiB,CAAC,YAAY;YACpD;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO;YAC1B,uDAAuD;YACvD,UAAU;YACV,eAAe,CAAC;QAClB;IACF;IAEA,iDAAiD;IACjD,MAAM,iBAAiB,CAAC,yPAAyP,EAC/Q,UAAU,QAAQ,CAAC,aAAa,mBAAmB,QACnD;IAEF,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,IACJ;IAEF,MAAM,aAAa;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;YAC3C,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGrD,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,IAAI;gBACJ,MAAM;gBACN,OAAO,SAAS,aAAa;gBAC7B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,cAAa;gBACb,iBAAe;gBACf,iBAAc;gBACd,iBAAe,SAAS,YAAY;gBACpC,yBAAuB,eAAe,KAAK,SAAS,GAAG,UAAU,QAAQ,EAAE,aAAa,GAAG;gBAC3F,MAAK;gBACL,qBAAkB;gBAClB,oBAAkB,QAAQ,GAAG,GAAG,MAAM,CAAC,GAAG;;;;;;0BAI5C,8OAAC;gBAAI,WAAW,CAAC,4EAA4E,EAAE,WAAW,eAAe,IAAI;0BAC3H,cAAA,8OAAC;oBAAE,WAAW,CAAC,qEAAqE,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;YAInH,UAAU,CAAC,0BACV,8OAAC;gBACC,IAAI;gBACJ,WAAU;gBACV,MAAK;gBACL,cAAW;0BAEV,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wBAEC,IAAI,GAAG,UAAU,QAAQ,EAAE,OAAO;wBAClC,WAAW,CAAC,gEAAgE,EAC1E,UAAU,cACN,8EACA,yFACJ;wBACF,SAAS,IAAM,oBAAoB;wBACnC,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,cAAc,IAAM,eAAe;wBACnC,MAAK;wBACL,iBAAe,UAAU;kCAExB;uBAbI;;;;8CAiBT,8OAAC;oBAAI,WAAU;oBAAqD,MAAK;oBAAS,iBAAc;oBAAO,iBAAc;8BAAQ;;;;;;;;;;;YAQlI,uBACC,8OAAC;gBAAE,IAAI,GAAG,GAAG,MAAM,CAAC;gBAAE,WAAU;gBAA8C,MAAK;0BAChF;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,+DAA+D;IAC/D,MAAM,iBAAiB,CAAC,kPAAkP,EACxQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 3589, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextArea.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  rows = 3,\r\n  ...props\r\n}, ref) => {\r\n  // Base textarea styling with proper text visibility for all modes\r\n  const baseTextAreaClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const textAreaClass = `${baseTextAreaClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <textarea\r\n        ref={ref}\r\n        className={textAreaClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        rows={rows}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextArea.displayName = 'TextArea';\r\n\r\nexport default TextArea;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAsC,CAAC,EAC/D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,GAAG,OACJ,EAAE;IACD,kEAAkE;IAClE,MAAM,oBAAoB,CAAC,2PAA2P,EACpR,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,EAC1C,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACL,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 3667, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,mMAAmM,EAC1N,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;YAIH,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 3752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FormInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport TextInput from './TextInput';\r\nimport TextArea from './TextArea';\r\nimport Select from './Select';\r\n\r\ninterface Option {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface FormInputProps {\r\n  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select';\r\n  label?: string | React.ReactNode;\r\n  id?: string;\r\n  name?: string;\r\n  value?: string;\r\n  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  helperText?: string;\r\n  options?: Option[];\r\n  rows?: number;\r\n  maxLength?: number;\r\n  variant?: 'default' | 'small';\r\n  className?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst FormInput: React.FC<FormInputProps> = ({\r\n  type = 'text',\r\n  children,\r\n  options,\r\n  rows,\r\n  ...props\r\n}) => {\r\n  switch (type) {\r\n    case 'textarea':\r\n      return <TextArea rows={rows} {...props} />;\r\n    \r\n    case 'select':\r\n      return (\r\n        <Select {...props}>\r\n          {options ? (\r\n            options.map((option) => (\r\n              <option key={option.value} value={option.value}>\r\n                {option.label}\r\n              </option>\r\n            ))\r\n          ) : (\r\n            children\r\n          )}\r\n        </Select>\r\n      );\r\n    \r\n    default:\r\n      return <TextInput type={type} {...props} />;\r\n  }\r\n};\r\n\r\nexport default FormInput;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgCA,MAAM,YAAsC,CAAC,EAC3C,OAAO,MAAM,EACb,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,GAAG,OACJ;IACC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,uIAAA,CAAA,UAAQ;gBAAC,MAAM;gBAAO,GAAG,KAAK;;;;;;QAExC,KAAK;YACH,qBACE,8OAAC,qIAAA,CAAA,UAAM;gBAAE,GAAG,KAAK;0BACd,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;QAKR;YACE,qBAAO,8OAAC,wIAAA,CAAA,UAAS;gBAAC,MAAM;gBAAO,GAAG,KAAK;;;;;;IAC3C;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/index.ts"], "sourcesContent": ["export { default as FileUpload } from './FileUpload';\r\nexport { default as FormField } from './FormField';\r\nexport { default as ProgressIndicator } from './ProgressIndicator';\r\nexport { default as CountryDropdown } from './CountryDropdown';\r\nexport { default as TextInput } from './TextInput';\r\nexport { default as TextArea } from './TextArea';\r\nexport { default as Select } from './Select';\r\nexport { default as FormInput } from './FormInput';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3868, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useAddressing.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { customerApi } from '@/lib/customer-api';\r\nimport { useState, useEffect } from 'react';\r\nimport { toast } from 'react-hot-toast';\r\nimport { useDebouncedCallback } from 'use-debounce';\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  address_origin: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\nexport const initialAddressData: CreateAddressData = {\r\n  address_type: 'postal',\r\n  address_origin: 'applicant',\r\n  address_line_1: '',\r\n  address_line_2: '',\r\n  postal_code: '',\r\n  country: '',\r\n  city: ''\r\n};\r\n\r\nexport interface Address {\r\n  address_id: string;\r\n  address_type: string;\r\n  address_origin: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n\r\n// Address service using customer API\r\nconst addressService = {\r\n  async createAddress(data: CreateAddressData): Promise<any> {\r\n    const response = await customerApi.createAddress(data);\r\n    return response.data;\r\n  },\r\n\r\n  async getAddress(id: string): Promise<any> {\r\n    const response = await customerApi.getAddress(id);\r\n    return response.data;\r\n  },\r\n\r\n  async editAddress(data: EditAddressData): Promise<any> {\r\n    const response = await customerApi.editAddress(data);\r\n    return response.data;\r\n  },\r\n\r\n  \r\n  async searchPostcodes(searchParams: SearchPostcodes): Promise<any> {\r\n    const response = await customerApi.searchPostcodes(searchParams);\r\n    return response.data;\r\n  },\r\n\r\n}\r\n\r\n\r\n// Actual hook\r\nexport const useAddresses = (initialSearchParams?: SearchPostcodes) => {\r\n  const [addresses, setAddresses] = useState<Address[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [searchParams, setSearchParams] = useState<SearchPostcodes>(initialSearchParams || {});\r\n\r\n  const [postcodeSuggestions, setPostcodeSuggestions] = useState<PostalCodeLookupResult[]>([]);\r\n  const [searching, setSearching] = useState(false);\r\n\r\n  // Fetch address list when searchParams change\r\n  useEffect(() => {\r\n    const fetchAddresses = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const response = await addressService.searchPostcodes(searchParams);\r\n        setAddresses(response.data || []);\r\n      } catch (err: any) {\r\n        console.error('Address fetch error:', err);\r\n        setError(err.message || 'Failed to fetch addresses');\r\n        toast.error('Failed to fetch addresses');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (Object.keys(searchParams).length > 0) {\r\n      fetchAddresses();\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Postcode suggestions (live lookup, debounced)\r\n  const debouncedSearchPostcodes = useDebouncedCallback(async (params: SearchPostcodes) => {\r\n    setSearching(true);\r\n    try {\r\n      const response = await customerApi.searchPostcodes(params);\r\n      setPostcodeSuggestions(response.data || []);\r\n    } catch (err) {\r\n      console.error('Postcode search failed:', err);\r\n    } finally {\r\n      setSearching(false);\r\n    }\r\n  }, 500); // debounce for 500ms\r\n\r\n  // Manual search trigger to update addresses based on params\r\n  const searchAddresses = (params: SearchPostcodes) => {\r\n    setSearchParams(params);\r\n  };\r\n\r\n  // Create new address\r\n  const createAddress = async (data: CreateAddressData) => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const newAddress = await addressService.createAddress(data);\r\n      setAddresses(prev => [newAddress, ...prev]);\r\n      toast.success('Address created successfully');\r\n      return newAddress;\r\n    } catch (err: any) {\r\n      console.error('Address create error:', err);\r\n      setError(err.message || 'Failed to create address');\r\n      toast.error('Failed to create address');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Edit existing address\r\n  const editAddress = async (data: EditAddressData) => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedAddress = await addressService.editAddress(data);\r\n      setAddresses(prev =>\r\n        prev.map(addr => (addr.address_id === data.address_id ? updatedAddress : addr))\r\n      );\r\n      toast.success('Address updated successfully');\r\n      return updatedAddress;\r\n    } catch (err: any) {\r\n      console.error('Address edit error:', err);\r\n      setError(err.message || 'Failed to update address');\r\n      toast.error('Failed to update address');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return {\r\n    // State\r\n    addresses,\r\n    postcodeSuggestions,\r\n    searching,\r\n    loading,\r\n    error,\r\n    searchParams,\r\n\r\n    // Setters / Triggers\r\n    setSearchParams,\r\n    debouncedSearchPostcodes,\r\n    searchAddresses,\r\n\r\n    // CRUD\r\n    createAddress,\r\n    editAddress,\r\n\r\n    // Raw service (if needed)\r\n    addressService,\r\n  };\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;AA6CO,MAAM,qBAAwC;IACnD,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,SAAS;IACT,MAAM;AACR;AAgBA,qCAAqC;AACrC,MAAM,iBAAiB;IACrB,MAAM,eAAc,IAAuB;QACzC,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,aAAa,CAAC;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,YAAW,EAAU;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAY,IAAqB;QACrC,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;QAC/C,OAAO,SAAS,IAAI;IACtB;IAGA,MAAM,iBAAgB,YAA6B;QACjD,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,eAAe,CAAC;QACnD,OAAO,SAAS,IAAI;IACtB;AAEF;AAIO,MAAM,eAAe,CAAC;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,uBAAuB,CAAC;IAE1F,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC3F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,WAAW;YACX,SAAS;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,eAAe,CAAC;gBACtD,aAAa,SAAS,IAAI,IAAI,EAAE;YAClC,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,SAAS,IAAI,OAAO,IAAI;gBACxB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,GAAG;YACxC;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,gDAAgD;IAChD,MAAM,2BAA2B,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;QAC3D,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,eAAe,CAAC;YACnD,uBAAuB,SAAS,IAAI,IAAI,EAAE;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF,GAAG,MAAM,qBAAqB;IAE9B,4DAA4D;IAC5D,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,qBAAqB;IACrB,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,aAAa,MAAM,eAAe,aAAa,CAAC;YACtD,aAAa,CAAA,OAAQ;oBAAC;uBAAe;iBAAK;YAC1C,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,OAAO,IAAI;YACxB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,iBAAiB,MAAM,eAAe,WAAW,CAAC;YACxD,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,OAAS,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG,iBAAiB;YAE3E,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,IAAI,OAAO,IAAI;YACxB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB;QACA;QACA;QAEA,OAAO;QACP;QACA;QAEA,0BAA0B;QAC1B;IACF;AACF", "debugId": null}}, {"offset": {"line": 4018, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ApplicantInfo.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { TextInput, CountryDropdown } from '@/components/forms';\r\nimport { ApplicantInfoData, ApplicationFormComponentProps } from './index';\r\nimport { useAddresses, initialAddressData, CreateAddressData } from '@/hooks/useAddressing';\r\n\r\ninterface ApplicantInfoProps extends ApplicationFormComponentProps {\r\n  data: ApplicantInfoData;\r\n  onChange: (data: ApplicantInfoData) => void;\r\n}\r\n\r\n\r\nconst ApplicantInfo: React.FC<ApplicantInfoProps> = ({\r\n  data,\r\n  onChange,\r\n  errors = {},\r\n  disabled = false\r\n}) => {\r\n  const handleInputChange = (field: keyof ApplicantInfoData, value: string) => {\r\n    onChange({\r\n      ...data,\r\n      [field]: value\r\n    });\r\n  };\r\n  \r\n  const { postcodeSuggestions, debouncedSearchPostcodes, createAddress } = useAddresses();\r\n  const [region, setRegion] = useState('');\r\n  const [addressData, setAddressData] = useState<CreateAddressData>(initialAddressData);\r\n  const [canAddNewEntry, setCanAddNewEntry] = useState(true);\r\n  const [submitting, setSubmitting] = useState(false);\r\n\r\n  // validate address completeness\r\n  useEffect(() => {\r\n    const isComplete =\r\n      addressData.address_line_1 &&\r\n      addressData.country &&\r\n      addressData.city &&\r\n      addressData.postal_code &&\r\n      addressData.address_origin &&\r\n      addressData.address_type;\r\n\r\n    setCanAddNewEntry(Boolean(isComplete));\r\n  }, [addressData]);\r\n\r\n  // ✅ Define save handler properly\r\n  const handleSavePostalAddress = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      console.log('Saving Postal Address:', addressData);\r\n      await createAddress(addressData); // You can enhance: pass origin/type here\r\n      setSubmitting(false);\r\n    } catch (err) {\r\n      console.error('Failed to save postal address:', err);\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleSavePhysicalAddress = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      console.log('Saving Postal Address:', addressData);\r\n      await createAddress(addressData); // You can enhance: pass origin/type here\r\n      setSubmitting(false);\r\n    } catch (err) {\r\n      console.error('Failed to save postal address:', err);\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // rest of your code, including JSX return, is already correct\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\r\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n          Applicant Information\r\n        </h2>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n          Please provide your personal and contact information\r\n        </p>\r\n      </div>\r\n\r\n      {/* Personal Information */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Personal Information\r\n        </h3>\r\n        \r\n        <div className=\"grid grid-cols-1 gap-6\">\r\n          <TextInput\r\n            label=\"Applicant Name\"\r\n            value={data.applicantName}\r\n            onChange={(e) => handleInputChange('applicantName', e.target.value)}\r\n            placeholder=\"Enter full name or company name\"\r\n            required\r\n            disabled={disabled}\r\n            error={errors.applicantName}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Address Information */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n        {/* Postal Address */}\r\n        <div className=\"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n            <i className=\"ri-mail-line mr-2\"></i>\r\n            Postal Address\r\n          </h3>\r\n          <div className=\"space-y-4\">\r\n            {/* Country Dropdown (Always Visible) */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Country *</label>\r\n              <CountryDropdown\r\n                value={addressData.country}\r\n                onChange={(value) => {\r\n                  setAddressData(prev => ({ ...prev, country: value }));\r\n                  if (value === 'Malawi') {\r\n                    setRegion('');\r\n                    setAddressData(prev => ({ ...prev, city: '', postal_code: '', address_line_1: '', address_line_2: '' }));\r\n                  }\r\n                }}\r\n                className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                required\r\n              />\r\n            </div>\r\n            {addressData.country === 'Malawi' ? (\r\n              <>\r\n                {/* Region Dropdown */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Region *</label>\r\n                  <select\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                    value={region || ''}\r\n                    onChange={(e) => {\r\n                      const selectedRegion = e.target.value;\r\n                      setRegion(selectedRegion);\r\n                      setAddressData(prev => ({\r\n                        ...prev,\r\n                        country: 'Malawi',\r\n                        city: '',\r\n                        postal_code: '',\r\n                        address_line_1: '',\r\n                        address_line_2: ''\r\n                      }));\r\n                      debouncedSearchPostcodes({ region: selectedRegion });\r\n                    }}\r\n                    required\r\n                  >\r\n                    <option value=\"\">Select a region</option>\r\n                    {['Northern', 'Central', 'Southern'].map(region => (\r\n                      <option key={region} value={region}>{region}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n\r\n                {/* District Dropdown */}\r\n                {region && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">District *</label>\r\n                    <select\r\n                      className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      value={addressData.city || ''}\r\n                      onChange={(e) => {\r\n                        const selectedDistrict = e.target.value;\r\n                        setAddressData(prev => ({\r\n                          ...prev,\r\n                          city: selectedDistrict,\r\n                          postal_code: '',\r\n                          address_line_1: '',\r\n                          address_line_2: ''\r\n                        }));\r\n                        debouncedSearchPostcodes({ region, district: selectedDistrict });\r\n                      }}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select a district</option>\r\n                      {[...new Set(postcodeSuggestions\r\n                        .filter(p => p.region === region)\r\n                        .map(p => p.district))].map(d => (\r\n                        <option key={d} value={d}>{d}</option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Location Dropdown */}\r\n                {addressData.city && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Location *</label>\r\n                    <select\r\n                      className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      value={addressData.postal_code || ''}\r\n                      onChange={(e) => {\r\n                        const selectedLocation = e.target.value;\r\n                        const match = postcodeSuggestions.find(p =>\r\n                          p.region === region &&\r\n                          p.district === addressData.city &&\r\n                          p.postal_code === selectedLocation\r\n                        );\r\n                        if (match) {\r\n                          setAddressData(prev => ({ ...prev, postal_code: match.postal_code }));\r\n                        }\r\n                      }}\r\n                      required\r\n                      disabled={!!addressData.postal_code}\r\n                    >\r\n                      <option value=\"\">Select a location</option>\r\n                      {postcodeSuggestions\r\n                        .filter(p =>\r\n                          p.region === region &&\r\n                          p.district === addressData.city)\r\n                        .map(loc => (\r\n                          <option key={loc.postal_code_id} value={loc.postal_code}>{loc.location}</option>\r\n                        ))}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Postal Code Display */}\r\n                {addressData.postal_code && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Postal Code *</label>\r\n                    <TextInput\r\n                      value={addressData.postal_code}\r\n                      readOnly\r\n                      disabled\r\n                      className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                    />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Address Line 1 & 2 */}\r\n                {addressData.postal_code && (\r\n                  <>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 1 *</label>\r\n                      <TextInput\r\n                        placeholder=\"e.g., P.O. Box or P/Bag\"\r\n                        value={addressData.address_line_1}\r\n                        onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}\r\n                        required\r\n                        className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 2</label>\r\n                      <TextInput\r\n                        placeholder=\"Optional details like Building Number, Block, etc.\"\r\n                        value={addressData.address_line_2}\r\n                        onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}\r\n                        className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      />\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </>\r\n            ) : (\r\n              <>\r\n                {/* Manual entry for non-Malawi countries */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Postal Code *</label>\r\n                  <TextInput\r\n                    value={addressData.postal_code}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, postal_code: e.target.value }))}\r\n                    required\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">City *</label>\r\n                  <TextInput\r\n                    value={addressData.city}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, city: e.target.value }))}\r\n                    required\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 1 *</label>\r\n                  <TextInput\r\n                    value={addressData.address_line_1}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}\r\n                    required\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 2</label>\r\n                  <TextInput\r\n                    value={addressData.address_line_2}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n              </>\r\n            )}\r\n            <div className=\"pt-4\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"px-4 py-2 bg-primary text-white rounded disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                disabled={!canAddNewEntry || submitting}\r\n                onClick={() => {\r\n                  console.log('Saving Postal Address...', addressData);\r\n                  handleSavePostalAddress();\r\n                }}\r\n              >\r\n                {submitting ? 'Saving...' : 'Save Postal Address'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n\r\n      </div>\r\n      {/* Contact Information */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Contact Information\r\n        </h3>\r\n        \r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <TextInput\r\n            type=\"tel\"\r\n            label=\"Telephone\"\r\n            value={data.telephone}\r\n            onChange={(e) => handleInputChange('telephone', e.target.value)}\r\n            placeholder=\"+265 123 456 789\"\r\n            required\r\n            disabled={disabled}\r\n            error={errors.telephone}\r\n            helperText=\"Include country code (e.g., +265)\"\r\n          />\r\n          \r\n          <TextInput\r\n            type=\"tel\"\r\n            label=\"Fax\"\r\n            value={data.fax}\r\n            onChange={(e) => handleInputChange('fax', e.target.value)}\r\n            placeholder=\"+265 123 456 789\"\r\n            disabled={disabled}\r\n            error={errors.fax}\r\n            helperText=\"Include country code (optional)\"\r\n          />\r\n        </div>\r\n        \r\n        <div className=\"grid grid-cols-1 gap-6\">\r\n          <TextInput\r\n            type=\"email\"\r\n            label=\"Email Address\"\r\n            value={data.email}\r\n            onChange={(e) => handleInputChange('email', e.target.value)}\r\n            placeholder=\"<EMAIL>\"\r\n            required\r\n            disabled={disabled}\r\n            error={errors.email}\r\n            helperText=\"We'll use this email for all communications\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Information Notice */}\r\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n        <div className=\"flex items-start\">\r\n          <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\r\n          <div>\r\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\r\n              Important Information\r\n            </h4>\r\n            <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\r\n              Please ensure all information is accurate and up-to-date. This information will be used \r\n              for official correspondence and license documentation.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicantInfo;\r\nfunction handleSubmit(arg0: () => void) {\r\n  throw new Error('Function not implemented.');\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAaA,MAAM,gBAA8C,CAAC,EACnD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAAgC;QACzD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,6HAAA,CAAA,qBAAkB;IACpF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aACJ,YAAY,cAAc,IAC1B,YAAY,OAAO,IACnB,YAAY,IAAI,IAChB,YAAY,WAAW,IACvB,YAAY,cAAc,IAC1B,YAAY,YAAY;QAE1B,kBAAkB,QAAQ;IAC5B,GAAG;QAAC;KAAY;IAEhB,iCAAiC;IACjC,MAAM,0BAA0B;QAC9B,IAAI;YACF,cAAc;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,MAAM,cAAc,cAAc,yCAAyC;YAC3E,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,cAAc;QAChB;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI;YACF,cAAc;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,MAAM,cAAc,cAAc,yCAAyC;YAC3E,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,cAAc;QAChB;IACF;IAEA,8DAA8D;IAG9D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gLAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,aAAa;4BACzB,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAClE,aAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,OAAO,OAAO,aAAa;;;;;;;;;;;;;;;;;0BAMjC,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAE,WAAU;;;;;;gCAAwB;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDACnF,8OAAC,4LAAA,CAAA,kBAAe;4CACd,OAAO,YAAY,OAAO;4CAC1B,UAAU,CAAC;gDACT,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS;oDAAM,CAAC;gDACnD,IAAI,UAAU,UAAU;oDACtB,UAAU;oDACV,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM;4DAAI,aAAa;4DAAI,gBAAgB;4DAAI,gBAAgB;wDAAG,CAAC;gDACxG;4CACF;4CACA,WAAU;4CACV,QAAQ;;;;;;;;;;;;gCAGX,YAAY,OAAO,KAAK,yBACvB;;sDAEE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC;oDACC,WAAU;oDACV,OAAO,UAAU;oDACjB,UAAU,CAAC;wDACT,MAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDACrC,UAAU;wDACV,eAAe,CAAA,OAAQ,CAAC;gEACtB,GAAG,IAAI;gEACP,SAAS;gEACT,MAAM;gEACN,aAAa;gEACb,gBAAgB;gEAChB,gBAAgB;4DAClB,CAAC;wDACD,yBAAyB;4DAAE,QAAQ;wDAAe;oDACpD;oDACA,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB;4DAAC;4DAAY;4DAAW;yDAAW,CAAC,GAAG,CAAC,CAAA,uBACvC,8OAAC;gEAAoB,OAAO;0EAAS;+DAAxB;;;;;;;;;;;;;;;;;wCAMlB,wBACC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC;oDACC,WAAU;oDACV,OAAO,YAAY,IAAI,IAAI;oDAC3B,UAAU,CAAC;wDACT,MAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACvC,eAAe,CAAA,OAAQ,CAAC;gEACtB,GAAG,IAAI;gEACP,MAAM;gEACN,aAAa;gEACb,gBAAgB;gEAChB,gBAAgB;4DAClB,CAAC;wDACD,yBAAyB;4DAAE;4DAAQ,UAAU;wDAAiB;oDAChE;oDACA,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB;+DAAI,IAAI,IAAI,oBACV,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QACzB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;yDAAG,CAAC,GAAG,CAAC,CAAA,kBAC5B,8OAAC;gEAAe,OAAO;0EAAI;+DAAd;;;;;;;;;;;;;;;;;wCAOpB,YAAY,IAAI,kBACf,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC;oDACC,WAAU;oDACV,OAAO,YAAY,WAAW,IAAI;oDAClC,UAAU,CAAC;wDACT,MAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACvC,MAAM,QAAQ,oBAAoB,IAAI,CAAC,CAAA,IACrC,EAAE,MAAM,KAAK,UACb,EAAE,QAAQ,KAAK,YAAY,IAAI,IAC/B,EAAE,WAAW,KAAK;wDAEpB,IAAI,OAAO;4DACT,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,aAAa,MAAM,WAAW;gEAAC,CAAC;wDACrE;oDACF;oDACA,QAAQ;oDACR,UAAU,CAAC,CAAC,YAAY,WAAW;;sEAEnC,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,oBACE,MAAM,CAAC,CAAA,IACN,EAAE,MAAM,KAAK,UACb,EAAE,QAAQ,KAAK,YAAY,IAAI,EAChC,GAAG,CAAC,CAAA,oBACH,8OAAC;gEAAgC,OAAO,IAAI,WAAW;0EAAG,IAAI,QAAQ;+DAAzD,IAAI,cAAc;;;;;;;;;;;;;;;;;wCAOxC,YAAY,WAAW,kBACtB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,WAAW;oDAC9B,QAAQ;oDACR,QAAQ;oDACR,WAAU;;;;;;;;;;;;wCAMf,YAAY,WAAW,kBACtB;;8DACE,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAkE;;;;;;sEACnF,8OAAC,gLAAA,CAAA,YAAS;4DACR,aAAY;4DACZ,OAAO,YAAY,cAAc;4DACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACpF,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAkE;;;;;;sEACnF,8OAAC,gLAAA,CAAA,YAAS;4DACR,aAAY;4DACZ,OAAO,YAAY,cAAc;4DACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACpF,WAAU;;;;;;;;;;;;;;;iEAOpB;;sDAEE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,WAAW;oDAC9B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACjF,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,IAAI;oDACvB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC1E,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,cAAc;oDACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACpF,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,cAAc;oDACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACpF,WAAU;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,UAAU,CAAC,kBAAkB;wCAC7B,SAAS;4CACP,QAAQ,GAAG,CAAC,4BAA4B;4CACxC;wCACF;kDAEC,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gLAAA,CAAA,YAAS;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO,KAAK,SAAS;gCACrB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC9D,aAAY;gCACZ,QAAQ;gCACR,UAAU;gCACV,OAAO,OAAO,SAAS;gCACvB,YAAW;;;;;;0CAGb,8OAAC,gLAAA,CAAA,YAAS;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO,KAAK,GAAG;gCACf,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;gCACxD,aAAY;gCACZ,UAAU;gCACV,OAAO,OAAO,GAAG;gCACjB,YAAW;;;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gLAAA,CAAA,YAAS;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO,KAAK,KAAK;4BACjB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4BAC1D,aAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,OAAO,OAAO,KAAK;4BACnB,YAAW;;;;;;;;;;;;;;;;;0BAMjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;uCAEe;AACf,SAAS,aAAa,IAAgB;IACpC,MAAM,IAAI,MAAM;AAClB", "debugId": null}}, {"offset": {"line": 4781, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/CompanyProfile.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TextInput, CountryDropdown, TextArea } from '@/components/forms';\nimport { CompanyProfileData, ShareholderData, DirectorData, ApplicationFormComponentProps } from './index';\n\ninterface CompanyProfileProps extends ApplicationFormComponentProps {\n  data: CompanyProfileData;\n  onChange: (data: CompanyProfileData) => void;\n}\n\nconst CompanyProfile: React.FC<CompanyProfileProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false\n}) => {\n  const handleInputChange = (field: keyof CompanyProfileData, value: any) => {\n    onChange({\n      ...data,\n      [field]: value\n    });\n  };\n\n  const addShareholder = () => {\n    const newShareholder: ShareholderData = {\n      name: '',\n      nationality: '',\n      address: '',\n      shareholding: ''\n    };\n    handleInputChange('shareholders', [...data.shareholders, newShareholder]);\n  };\n\n  const updateShareholder = (index: number, field: keyof ShareholderData, value: string) => {\n    const updatedShareholders = data.shareholders.map((shareholder, i) =>\n      i === index ? { ...shareholder, [field]: value } : shareholder\n    );\n    handleInputChange('shareholders', updatedShareholders);\n  };\n\n  const removeShareholder = (index: number) => {\n    const updatedShareholders = data.shareholders.filter((_, i) => i !== index);\n    handleInputChange('shareholders', updatedShareholders);\n  };\n\n  const addDirector = () => {\n    const newDirector: DirectorData = {\n      name: '',\n      nationality: '',\n      address: ''\n    };\n    handleInputChange('directors', [...data.directors, newDirector]);\n  };\n\n  const updateDirector = (index: number, field: keyof DirectorData, value: string) => {\n    const updatedDirectors = data.directors.map((director, i) =>\n      i === index ? { ...director, [field]: value } : director\n    );\n    handleInputChange('directors', updatedDirectors);\n  };\n\n  const removeDirector = (index: number) => {\n    const updatedDirectors = data.directors.filter((_, i) => i !== index);\n    handleInputChange('directors', updatedDirectors);\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Company Profile\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide details about your company structure and ownership\n        </p>\n      </div>\n\n      {/* Company Registration Details */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Company Registration Details\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <TextInput\n            label=\"Business Registration Number\"\n            value={data.businessRegistrationNo}\n            onChange={(e) => handleInputChange('businessRegistrationNo', e.target.value)}\n            placeholder=\"Enter registration number\"\n            required\n            disabled={disabled}\n            error={errors.businessRegistrationNo}\n          />\n          \n          <TextInput\n            label=\"TPIN (Tax Payer Identification Number)\"\n            value={data.tpin}\n            onChange={(e) => handleInputChange('tpin', e.target.value)}\n            placeholder=\"Enter TPIN\"\n            required\n            disabled={disabled}\n            error={errors.tpin}\n          />\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <TextInput\n            type=\"date\"\n            label=\"Date of Incorporation\"\n            value={data.dateOfIncorporation}\n            onChange={(e) => handleInputChange('dateOfIncorporation', e.target.value)}\n            required\n            disabled={disabled}\n            error={errors.dateOfIncorporation}\n          />\n          \n          <TextInput\n            label=\"Place of Incorporation\"\n            value={data.placeOfIncorporation}\n            onChange={(e) => handleInputChange('placeOfIncorporation', e.target.value)}\n            placeholder=\"Enter place of incorporation\"\n            required\n            disabled={disabled}\n            error={errors.placeOfIncorporation}\n          />\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <TextInput\n            label=\"Website\"\n            value={data.website}\n            onChange={(e) => handleInputChange('website', e.target.value)}\n            placeholder=\"https://www.example.com\"\n            disabled={disabled}\n            error={errors.website}\n          />\n          \n          <TextInput\n            label=\"Foreign Ownership Percentage\"\n            value={data.foreignOwnership}\n            onChange={(e) => handleInputChange('foreignOwnership', e.target.value)}\n            placeholder=\"0-100%\"\n            disabled={disabled}\n            error={errors.foreignOwnership}\n            helperText=\"Enter percentage of foreign ownership\"\n          />\n        </div>\n      </div>\n\n      {/* Shareholders */}\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Shareholders\n          </h3>\n          <button\n            type=\"button\"\n            onClick={addShareholder}\n            disabled={disabled}\n            className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50\"\n          >\n            <i className=\"ri-add-line mr-1\"></i>\n            Add Shareholder\n          </button>\n        </div>\n        \n        {data.shareholders.map((shareholder, index) => (\n          <div key={index} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100\">\n                Shareholder {index + 1}\n              </h4>\n              {data.shareholders.length > 1 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeShareholder(index)}\n                  disabled={disabled}\n                  className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  <i className=\"ri-delete-bin-line\"></i>\n                </button>\n              )}\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <TextInput\n                label=\"Name\"\n                value={shareholder.name}\n                onChange={(e) => updateShareholder(index, 'name', e.target.value)}\n                placeholder=\"Enter shareholder name\"\n                required\n                disabled={disabled}\n              />\n              \n              <CountryDropdown\n                label=\"Nationality\"\n                value={shareholder.nationality}\n                onChange={(value) => updateShareholder(index, 'nationality', value)}\n                required\n                disabled={disabled}\n              />\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n              <TextArea\n                label=\"Address\"\n                value={shareholder.address}\n                onChange={(e) => updateShareholder(index, 'address', e.target.value)}\n                placeholder=\"Enter address\"\n                rows={2}\n                required\n                disabled={disabled}\n              />\n              \n              <TextInput\n                label=\"Shareholding Percentage\"\n                value={shareholder.shareholding}\n                onChange={(e) => updateShareholder(index, 'shareholding', e.target.value)}\n                placeholder=\"0-100%\"\n                required\n                disabled={disabled}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Directors */}\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Directors\n          </h3>\n          <button\n            type=\"button\"\n            onClick={addDirector}\n            disabled={disabled}\n            className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50\"\n          >\n            <i className=\"ri-add-line mr-1\"></i>\n            Add Director\n          </button>\n        </div>\n        \n        {data.directors.map((director, index) => (\n          <div key={index} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100\">\n                Director {index + 1}\n              </h4>\n              {data.directors.length > 1 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeDirector(index)}\n                  disabled={disabled}\n                  className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  <i className=\"ri-delete-bin-line\"></i>\n                </button>\n              )}\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <TextInput\n                label=\"Name\"\n                value={director.name}\n                onChange={(e) => updateDirector(index, 'name', e.target.value)}\n                placeholder=\"Enter director name\"\n                required\n                disabled={disabled}\n              />\n              \n              <CountryDropdown\n                label=\"Nationality\"\n                value={director.nationality}\n                onChange={(value) => updateDirector(index, 'nationality', value)}\n                required\n                disabled={disabled}\n              />\n            </div>\n            \n            <div className=\"grid grid-cols-1 gap-4 mt-4\">\n              <TextArea\n                label=\"Address\"\n                value={director.address}\n                onChange={(e) => updateDirector(index, 'address', e.target.value)}\n                placeholder=\"Enter address\"\n                rows={2}\n                required\n                disabled={disabled}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default CompanyProfile;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAAiC;QAC1D,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,iBAAkC;YACtC,MAAM;YACN,aAAa;YACb,SAAS;YACT,cAAc;QAChB;QACA,kBAAkB,gBAAgB;eAAI,KAAK,YAAY;YAAE;SAAe;IAC1E;IAEA,MAAM,oBAAoB,CAAC,OAAe,OAA8B;QACtE,MAAM,sBAAsB,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,IAC9D,MAAM,QAAQ;gBAAE,GAAG,WAAW;gBAAE,CAAC,MAAM,EAAE;YAAM,IAAI;QAErD,kBAAkB,gBAAgB;IACpC;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,sBAAsB,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrE,kBAAkB,gBAAgB;IACpC;IAEA,MAAM,cAAc;QAClB,MAAM,cAA4B;YAChC,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA,kBAAkB,aAAa;eAAI,KAAK,SAAS;YAAE;SAAY;IACjE;IAEA,MAAM,iBAAiB,CAAC,OAAe,OAA2B;QAChE,MAAM,mBAAmB,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,IACrD,MAAM,QAAQ;gBAAE,GAAG,QAAQ;gBAAE,CAAC,MAAM,EAAE;YAAM,IAAI;QAElD,kBAAkB,aAAa;IACjC;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC/D,kBAAkB,aAAa;IACjC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,KAAK,sBAAsB;gCAClC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;gCAC3E,aAAY;gCACZ,QAAQ;gCACR,UAAU;gCACV,OAAO,OAAO,sBAAsB;;;;;;0CAGtC,8OAAC,gLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,KAAK,IAAI;gCAChB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,aAAY;gCACZ,QAAQ;gCACR,UAAU;gCACV,OAAO,OAAO,IAAI;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gLAAA,CAAA,YAAS;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO,KAAK,mBAAmB;gCAC/B,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;gCACxE,QAAQ;gCACR,UAAU;gCACV,OAAO,OAAO,mBAAmB;;;;;;0CAGnC,8OAAC,gLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,KAAK,oBAAoB;gCAChC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;gCACzE,aAAY;gCACZ,QAAQ;gCACR,UAAU;gCACV,OAAO,OAAO,oBAAoB;;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,KAAK,OAAO;gCACnB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC5D,aAAY;gCACZ,UAAU;gCACV,OAAO,OAAO,OAAO;;;;;;0CAGvB,8OAAC,gLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,KAAK,gBAAgB;gCAC5B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACrE,aAAY;gCACZ,UAAU;gCACV,OAAO,OAAO,gBAAgB;gCAC9B,YAAW;;;;;;;;;;;;;;;;;;0BAMjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;oBAKvC,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACnC,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAuD;gDACtD,QAAQ;;;;;;;wCAEtB,KAAK,YAAY,CAAC,MAAM,GAAG,mBAC1B,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,UAAU;4CACV,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gLAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,YAAY,IAAI;4CACvB,UAAU,CAAC,IAAM,kBAAkB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;4CACZ,QAAQ;4CACR,UAAU;;;;;;sDAGZ,8OAAC,4LAAA,CAAA,kBAAe;4CACd,OAAM;4CACN,OAAO,YAAY,WAAW;4CAC9B,UAAU,CAAC,QAAU,kBAAkB,OAAO,eAAe;4CAC7D,QAAQ;4CACR,UAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,OAAO,YAAY,OAAO;4CAC1B,UAAU,CAAC,IAAM,kBAAkB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4CACnE,aAAY;4CACZ,MAAM;4CACN,QAAQ;4CACR,UAAU;;;;;;sDAGZ,8OAAC,gLAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,YAAY,YAAY;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CACxE,aAAY;4CACZ,QAAQ;4CACR,UAAU;;;;;;;;;;;;;2BArDN;;;;;;;;;;;0BA6Dd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;oBAKvC,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC7B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAuD;gDACzD,QAAQ;;;;;;;wCAEnB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,UAAU;4CACV,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gLAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,eAAe,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAC7D,aAAY;4CACZ,QAAQ;4CACR,UAAU;;;;;;sDAGZ,8OAAC,4LAAA,CAAA,kBAAe;4CACd,OAAM;4CACN,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,QAAU,eAAe,OAAO,eAAe;4CAC1D,QAAQ;4CACR,UAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8KAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,eAAe,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;wCAChE,aAAY;wCACZ,MAAM;wCACN,QAAQ;wCACR,UAAU;;;;;;;;;;;;2BA5CN;;;;;;;;;;;;;;;;;AAoDpB;uCAEe", "debugId": null}}, {"offset": {"line": 5309, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/Management.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TextInput, TextArea } from '@/components/forms';\nimport { ManagementData, ManagementTeamMember, ApplicationFormComponentProps } from './index';\n\ninterface ManagementProps extends ApplicationFormComponentProps {\n  data: ManagementData;\n  onChange: (data: ManagementData) => void;\n}\n\nconst Management: React.FC<ManagementProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false\n}) => {\n  const handleInputChange = (field: keyof ManagementData, value: any) => {\n    onChange({\n      ...data,\n      [field]: value\n    });\n  };\n\n  const addManagementMember = () => {\n    const newMember: ManagementTeamMember = {\n      name: '',\n      position: '',\n      qualifications: '',\n      experience: ''\n    };\n    handleInputChange('managementTeam', [...data.managementTeam, newMember]);\n  };\n\n  const updateManagementMember = (index: number, field: keyof ManagementTeamMember, value: string) => {\n    const updatedTeam = data.managementTeam.map((member, i) =>\n      i === index ? { ...member, [field]: value } : member\n    );\n    handleInputChange('managementTeam', updatedTeam);\n  };\n\n  const removeManagementMember = (index: number) => {\n    const updatedTeam = data.managementTeam.filter((_, i) => i !== index);\n    handleInputChange('managementTeam', updatedTeam);\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Management Team\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide information about your management team and organizational structure\n        </p>\n      </div>\n\n      {/* Management Team Members */}\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Key Management Personnel\n          </h3>\n          <button\n            type=\"button\"\n            onClick={addManagementMember}\n            disabled={disabled}\n            className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50\"\n          >\n            <i className=\"ri-add-line mr-1\"></i>\n            Add Team Member\n          </button>\n        </div>\n        \n        {data.managementTeam.map((member, index) => (\n          <div key={index} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100\">\n                Team Member {index + 1}\n              </h4>\n              {data.managementTeam.length > 1 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeManagementMember(index)}\n                  disabled={disabled}\n                  className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  <i className=\"ri-delete-bin-line\"></i>\n                </button>\n              )}\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <TextInput\n                label=\"Full Name\"\n                value={member.name}\n                onChange={(e) => updateManagementMember(index, 'name', e.target.value)}\n                placeholder=\"Enter full name\"\n                required\n                disabled={disabled}\n              />\n              \n              <TextInput\n                label=\"Position/Title\"\n                value={member.position}\n                onChange={(e) => updateManagementMember(index, 'position', e.target.value)}\n                placeholder=\"e.g., Chief Executive Officer, General Manager\"\n                required\n                disabled={disabled}\n              />\n            </div>\n            \n            <div className=\"grid grid-cols-1 gap-4 mt-4\">\n              <TextArea\n                label=\"Qualifications\"\n                value={member.qualifications}\n                onChange={(e) => updateManagementMember(index, 'qualifications', e.target.value)}\n                placeholder=\"List educational qualifications, certifications, and professional credentials\"\n                rows={3}\n                required\n                disabled={disabled}\n                helperText=\"Include degrees, certifications, and relevant professional qualifications\"\n              />\n              \n              <TextArea\n                label=\"Professional Experience\"\n                value={member.experience}\n                onChange={(e) => updateManagementMember(index, 'experience', e.target.value)}\n                placeholder=\"Describe relevant work experience, previous roles, and industry expertise\"\n                rows={4}\n                required\n                disabled={disabled}\n                helperText=\"Include years of experience, previous companies, and relevant achievements\"\n              />\n            </div>\n          </div>\n        ))}\n        \n        {data.managementTeam.length === 0 && (\n          <div className=\"text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg\">\n            <i className=\"ri-team-line text-4xl text-gray-400 dark:text-gray-500 mb-2\"></i>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-4\">No management team members added yet</p>\n            <button\n              type=\"button\"\n              onClick={addManagementMember}\n              disabled={disabled}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50\"\n            >\n              <i className=\"ri-add-line mr-2\"></i>\n              Add First Team Member\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Organizational Structure */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Organizational Structure\n        </h3>\n        \n        <TextArea\n          label=\"Organizational Structure Description\"\n          value={data.organizationalStructure}\n          onChange={(e) => handleInputChange('organizationalStructure', e.target.value)}\n          placeholder=\"Describe your company's organizational structure, reporting lines, and departmental divisions\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.organizationalStructure}\n          helperText=\"Include information about departments, reporting relationships, and decision-making processes\"\n        />\n      </div>\n\n      {/* Key Personnel Summary */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Key Personnel Summary\n        </h3>\n        \n        <TextArea\n          label=\"Key Personnel Overview\"\n          value={data.keyPersonnel}\n          onChange={(e) => handleInputChange('keyPersonnel', e.target.value)}\n          placeholder=\"Provide a summary of key personnel roles and responsibilities within the organization\"\n          rows={4}\n          required\n          disabled={disabled}\n          error={errors.keyPersonnel}\n          helperText=\"Highlight the most critical roles and how they contribute to business operations\"\n        />\n      </div>\n\n      {/* Information Notice */}\n      <div className=\"bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800\">\n        <div className=\"flex items-start\">\n          <i className=\"ri-information-line text-amber-600 dark:text-amber-400 text-lg mr-3 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-amber-900 dark:text-amber-100 mb-1\">\n              Management Team Requirements\n            </h4>\n            <p className=\"text-amber-700 dark:text-amber-300 text-sm\">\n              Please ensure that your management team has the necessary qualifications and experience \n              relevant to the license type you are applying for. Include all key decision-makers and \n              personnel who will be responsible for compliance and operations.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Management;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAHA;;;AAWA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAA6B;QACtD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAkC;YACtC,MAAM;YACN,UAAU;YACV,gBAAgB;YAChB,YAAY;QACd;QACA,kBAAkB,kBAAkB;eAAI,KAAK,cAAc;YAAE;SAAU;IACzE;IAEA,MAAM,yBAAyB,CAAC,OAAe,OAAmC;QAChF,MAAM,cAAc,KAAK,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,IACnD,MAAM,QAAQ;gBAAE,GAAG,MAAM;gBAAE,CAAC,MAAM,EAAE;YAAM,IAAI;QAEhD,kBAAkB,kBAAkB;IACtC;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,KAAK,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC/D,kBAAkB,kBAAkB;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;oBAKvC,KAAK,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAChC,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAuD;gDACtD,QAAQ;;;;;;;wCAEtB,KAAK,cAAc,CAAC,MAAM,GAAG,mBAC5B,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,uBAAuB;4CACtC,UAAU;4CACV,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gLAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,OAAO,IAAI;4CAClB,UAAU,CAAC,IAAM,uBAAuB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,QAAQ;4CACR,UAAU;;;;;;sDAGZ,8OAAC,gLAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,OAAO,QAAQ;4CACtB,UAAU,CAAC,IAAM,uBAAuB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;4CACzE,aAAY;4CACZ,QAAQ;4CACR,UAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,OAAO,OAAO,cAAc;4CAC5B,UAAU,CAAC,IAAM,uBAAuB,OAAO,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CAC/E,aAAY;4CACZ,MAAM;4CACN,QAAQ;4CACR,UAAU;4CACV,YAAW;;;;;;sDAGb,8OAAC,8KAAA,CAAA,WAAQ;4CACP,OAAM;4CACN,OAAO,OAAO,UAAU;4CACxB,UAAU,CAAC,IAAM,uBAAuB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC3E,aAAY;4CACZ,MAAM;4CACN,QAAQ;4CACR,UAAU;4CACV,YAAW;;;;;;;;;;;;;2BAzDP;;;;;oBA+DX,KAAK,cAAc,CAAC,MAAM,KAAK,mBAC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CACrD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,uBAAuB;wBACnC,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,EAAE,MAAM,CAAC,KAAK;wBAC5E,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,uBAAuB;wBACrC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,YAAY;wBACxB,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBACjE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,YAAY;wBAC1B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtE;uCAEe", "debugId": null}}, {"offset": {"line": 5708, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ProfessionalServices.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { TextArea } from '@/components/forms';\r\nimport { ProfessionalServicesData, ApplicationFormComponentProps } from './index';\r\n\r\ninterface ProfessionalServicesProps extends ApplicationFormComponentProps {\r\n  data: ProfessionalServicesData;\r\n  onChange: (data: ProfessionalServicesData) => void;\r\n}\r\n\r\nconst ProfessionalServices: React.FC<ProfessionalServicesProps> = ({\r\n  data,\r\n  onChange,\r\n  errors = {},\r\n  disabled = false\r\n}) => {\r\n  const handleInputChange = (field: keyof ProfessionalServicesData, value: string) => {\r\n    onChange({\r\n      ...data,\r\n      [field]: value\r\n    });\r\n  };\r\n\r\n  // Check if all required fields are filled\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\r\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n          Professional Services\r\n        </h2>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n          Provide information about external consultants, service providers, and support arrangements\r\n        </p>\r\n      </div>\r\n\r\n      {/* Consultants */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          External Consultants\r\n        </h3>\r\n        \r\n        <TextArea\r\n          label=\"Consultant Information\"\r\n          value={data.consultants}\r\n          onChange={(e) => handleInputChange('consultants', e.target.value)}\r\n          placeholder=\"List any external consultants, their areas of expertise, and their role in your operations\"\r\n          rows={5}\r\n          required\r\n          disabled={disabled}\r\n          error={errors.consultants}\r\n          helperText=\"Include consultant names, companies, specializations, and how they will support your business. This field is required.\"\r\n        />\r\n      </div>\r\n\r\n      {/* Service Providers */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Service Providers\r\n        </h3>\r\n        \r\n        <TextArea\r\n          label=\"Third-Party Service Providers\"\r\n          value={data.serviceProviders}\r\n          onChange={(e) => handleInputChange('serviceProviders', e.target.value)}\r\n          placeholder=\"Describe any third-party service providers, vendors, or partners that will support your operations\"\r\n          rows={5}\r\n          required\r\n          disabled={disabled}\r\n          error={errors.serviceProviders}\r\n          helperText=\"Include details about outsourced services, vendor relationships, and partnership agreements. This field is required.\"\r\n        />\r\n      </div>\r\n\r\n      {/* Technical Support */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Technical Support Arrangements\r\n        </h3>\r\n        \r\n        <TextArea\r\n          label=\"Technical Support\"\r\n          value={data.technicalSupport}\r\n          onChange={(e) => handleInputChange('technicalSupport', e.target.value)}\r\n          placeholder=\"Describe your technical support arrangements, including internal capabilities and external support contracts\"\r\n          rows={5}\r\n          required\r\n          disabled={disabled}\r\n          error={errors.technicalSupport}\r\n          helperText=\"Include information about technical expertise, support contracts, and emergency response capabilities\"\r\n        />\r\n      </div>\r\n\r\n      {/* Maintenance Arrangements */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Maintenance and Support\r\n        </h3>\r\n        \r\n        <TextArea\r\n          label=\"Maintenance Arrangements\"\r\n          value={data.maintenanceArrangements}\r\n          onChange={(e) => handleInputChange('maintenanceArrangements', e.target.value)}\r\n          placeholder=\"Detail your maintenance arrangements for equipment, systems, and infrastructure\"\r\n          rows={5}\r\n          required\r\n          disabled={disabled}\r\n          error={errors.maintenanceArrangements}\r\n          helperText=\"Include preventive maintenance schedules, service level agreements, and backup arrangements\"\r\n        />\r\n      </div>\r\n\r\n      {/* Professional Services Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n          <div className=\"flex items-start\">\r\n            <i className=\"ri-user-star-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\r\n            <div>\r\n              <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\r\n                Consultant Guidelines\r\n              </h4>\r\n              <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\r\n                Include consultant qualifications, experience in your industry, and specific \r\n                areas where they will provide expertise.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\r\n          <div className=\"flex items-start\">\r\n            <i className=\"ri-tools-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5\"></i>\r\n            <div>\r\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-1\">\r\n                Technical Support\r\n              </h4>\r\n              <p className=\"text-green-700 dark:text-green-300 text-sm\">\r\n                Demonstrate adequate technical support capabilities to ensure reliable \r\n                service delivery and compliance.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Information Notice */}\r\n      <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800\">\r\n        <div className=\"flex items-start\">\r\n          <i className=\"ri-information-line text-purple-600 dark:text-purple-400 text-lg mr-3 mt-0.5\"></i>\r\n          <div>\r\n            <h4 className=\"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2\">\r\n              Professional Services Requirements\r\n            </h4>\r\n            <ul className=\"text-purple-700 dark:text-purple-300 text-sm space-y-1\">\r\n              <li>• Provide detailed information about all external professional relationships</li>\r\n              <li>• Include service level agreements and contract terms where applicable</li>\r\n              <li>• Demonstrate adequate technical and maintenance support capabilities</li>\r\n              <li>• Show how professional services align with your business objectives</li>\r\n              <li>• Include backup arrangements for critical services</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfessionalServices;\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAWA,MAAM,uBAA4D,CAAC,EACjE,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAAuC;QAChE,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,0CAA0C;IAE1C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,WAAW;wBACvB,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,WAAW;wBACzB,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,gBAAgB;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wBACrE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,gBAAgB;wBAC9B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,gBAAgB;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wBACrE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,gBAAgB;wBAC9B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,uBAAuB;wBACnC,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,EAAE,MAAM,CAAC,KAAK;wBAC5E,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,uBAAuB;wBACrC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAQ9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 6081, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/BusinessInfo.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TextArea } from '@/components/forms';\nimport { BusinessInfoData, ApplicationFormComponentProps } from './index';\n\ninterface BusinessInfoProps extends ApplicationFormComponentProps {\n  data: BusinessInfoData;\n  onChange: (data: BusinessInfoData) => void;\n}\n\nconst BusinessInfo: React.FC<BusinessInfoProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false\n}) => {\n  const handleInputChange = (field: keyof BusinessInfoData, value: string) => {\n    onChange({\n      ...data,\n      [field]: value\n    });\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Business Information\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide detailed information about your business operations, facilities, and model\n        </p>\n      </div>\n\n      {/* Business Description */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Business Description\n        </h3>\n        \n        <TextArea\n          label=\"Business Description\"\n          value={data.businessDescription}\n          onChange={(e) => handleInputChange('businessDescription', e.target.value)}\n          placeholder=\"Provide a comprehensive description of your business, its purpose, and core activities\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.businessDescription}\n          helperText=\"Include your company's mission, vision, and primary business objectives\"\n        />\n      </div>\n\n      {/* Operational Areas */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Operational Areas\n        </h3>\n        \n        <TextArea\n          label=\"Areas of Operation\"\n          value={data.operationalAreas}\n          onChange={(e) => handleInputChange('operationalAreas', e.target.value)}\n          placeholder=\"Describe the geographical areas where you plan to operate and provide services\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.operationalAreas}\n          helperText=\"Include specific regions, cities, or districts where services will be offered\"\n        />\n      </div>\n\n      {/* Facilities */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Facilities and Infrastructure\n        </h3>\n        \n        <TextArea\n          label=\"Facilities Description\"\n          value={data.facilities}\n          onChange={(e) => handleInputChange('facilities', e.target.value)}\n          placeholder=\"Detail your physical facilities, office locations, warehouses, and infrastructure\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.facilities}\n          helperText=\"Include addresses, sizes, ownership status (owned/leased), and facility purposes\"\n        />\n      </div>\n\n      {/* Equipment */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Equipment and Technology\n        </h3>\n        \n        <TextArea\n          label=\"Equipment and Technology\"\n          value={data.equipment}\n          onChange={(e) => handleInputChange('equipment', e.target.value)}\n          placeholder=\"List major equipment, technology systems, and technical infrastructure\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.equipment}\n          helperText=\"Include specifications, capacity, and how equipment supports your service delivery\"\n        />\n      </div>\n\n      {/* Business Model */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Business Model\n        </h3>\n        \n        <TextArea\n          label=\"Business Model Description\"\n          value={data.businessModel}\n          onChange={(e) => handleInputChange('businessModel', e.target.value)}\n          placeholder=\"Explain your business model, revenue streams, and how you plan to generate income\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.businessModel}\n          helperText=\"Include pricing strategy, customer segments, and value proposition\"\n        />\n      </div>\n\n      {/* Business Information Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-building-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\n                Facilities\n              </h4>\n              <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\n                Provide complete details about all business locations and infrastructure.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-settings-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-1\">\n                Equipment\n              </h4>\n              <p className=\"text-green-700 dark:text-green-300 text-sm\">\n                List all major equipment and technology that will be used in operations.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-lightbulb-line text-purple-600 dark:text-purple-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-purple-900 dark:text-purple-100 mb-1\">\n                Business Model\n              </h4>\n              <p className=\"text-purple-700 dark:text-purple-300 text-sm\">\n                Clearly explain how your business will operate and generate revenue.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Information Notice */}\n      <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800\">\n        <div className=\"flex items-start\">\n          <i className=\"ri-information-line text-orange-600 dark:text-orange-400 text-lg mr-3 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-orange-900 dark:text-orange-100 mb-2\">\n              Business Information Requirements\n            </h4>\n            <ul className=\"text-orange-700 dark:text-orange-300 text-sm space-y-1\">\n              <li>• Provide comprehensive details about all aspects of your business operations</li>\n              <li>• Include specific locations, addresses, and facility specifications</li>\n              <li>• List all major equipment with technical specifications where relevant</li>\n              <li>• Explain how your business model aligns with the license requirements</li>\n              <li>• Demonstrate operational readiness and capacity to deliver services</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BusinessInfo;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAWA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAA+B;QACxD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,mBAAmB;wBAC/B,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;wBACxE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,mBAAmB;wBACjC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,gBAAgB;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wBACrE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,gBAAgB;wBAC9B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,UAAU;wBACtB,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,UAAU;wBACxB,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,SAAS;wBACrB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,SAAS;wBACvB,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,aAAa;wBACzB,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAClE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,aAAa;wBAC3B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 6532, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ServiceScope.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TextArea } from '@/components/forms';\nimport { ServiceScopeData, ApplicationFormComponentProps } from './index';\n\ninterface ServiceScopeProps extends ApplicationFormComponentProps {\n  data: ServiceScopeData;\n  onChange: (data: ServiceScopeData) => void;\n}\n\nconst ServiceScope: React.FC<ServiceScopeProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false\n}) => {\n  const handleInputChange = (field: keyof ServiceScopeData, value: string) => {\n    onChange({\n      ...data,\n      [field]: value\n    });\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Service Scope\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Define the scope and nature of services you plan to provide under this license\n        </p>\n      </div>\n\n      {/* Services Offered */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Services Offered\n        </h3>\n        \n        <TextArea\n          label=\"Detailed Service Description\"\n          value={data.servicesOffered}\n          onChange={(e) => handleInputChange('servicesOffered', e.target.value)}\n          placeholder=\"Provide a comprehensive description of all services you plan to offer under this license\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.servicesOffered}\n          helperText=\"Include specific service types, features, and any specialized offerings\"\n        />\n      </div>\n\n      {/* Target Market */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Target Market\n        </h3>\n        \n        <TextArea\n          label=\"Target Market Analysis\"\n          value={data.targetMarket}\n          onChange={(e) => handleInputChange('targetMarket', e.target.value)}\n          placeholder=\"Describe your target market, customer segments, and market demographics\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.targetMarket}\n          helperText=\"Include customer types (individuals, businesses, government), market size, and customer needs\"\n        />\n      </div>\n\n      {/* Geographic Coverage */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Geographic Coverage\n        </h3>\n        \n        <TextArea\n          label=\"Service Coverage Area\"\n          value={data.geographicCoverage}\n          onChange={(e) => handleInputChange('geographicCoverage', e.target.value)}\n          placeholder=\"Define the geographic areas where you will provide services\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.geographicCoverage}\n          helperText=\"Include specific regions, cities, districts, or coverage boundaries\"\n        />\n      </div>\n\n      {/* Service Standards */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Service Standards and Quality\n        </h3>\n        \n        <TextArea\n          label=\"Service Standards\"\n          value={data.serviceStandards}\n          onChange={(e) => handleInputChange('serviceStandards', e.target.value)}\n          placeholder=\"Describe the service standards, quality measures, and performance indicators you will maintain\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.serviceStandards}\n          helperText=\"Include quality metrics, service level agreements, and compliance standards\"\n        />\n      </div>\n\n      {/* Service Scope Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-service-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\n                Service Definition\n              </h4>\n              <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\n                Clearly define all services you plan to offer and ensure they align with \n                the license category requirements.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-map-pin-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-1\">\n                Coverage Area\n              </h4>\n              <p className=\"text-green-700 dark:text-green-300 text-sm\">\n                Specify exact geographic boundaries and ensure you have the capacity \n                to serve the proposed coverage area.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-group-line text-purple-600 dark:text-purple-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-purple-900 dark:text-purple-100 mb-1\">\n                Target Market\n              </h4>\n              <p className=\"text-purple-700 dark:text-purple-300 text-sm\">\n                Identify your target customers and demonstrate understanding of \n                their needs and market dynamics.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-star-line text-orange-600 dark:text-orange-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-orange-900 dark:text-orange-100 mb-1\">\n                Quality Standards\n              </h4>\n              <p className=\"text-orange-700 dark:text-orange-300 text-sm\">\n                Define measurable quality standards and show commitment to \n                maintaining high service levels.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Information Notice */}\n      <div className=\"bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 border border-indigo-200 dark:border-indigo-800\">\n        <div className=\"flex items-start\">\n          <i className=\"ri-information-line text-indigo-600 dark:text-indigo-400 text-lg mr-3 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-indigo-900 dark:text-indigo-100 mb-2\">\n              Service Scope Guidelines\n            </h4>\n            <ul className=\"text-indigo-700 dark:text-indigo-300 text-sm space-y-1\">\n              <li>• Ensure all proposed services fall within the license category scope</li>\n              <li>• Provide realistic geographic coverage based on your operational capacity</li>\n              <li>• Define measurable service standards and quality metrics</li>\n              <li>• Demonstrate understanding of your target market and customer needs</li>\n              <li>• Show how your services will benefit the community or market</li>\n              <li>• Include any innovative or unique aspects of your service offering</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ServiceScope;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAWA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAA+B;QACxD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,eAAe;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBACpE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,eAAe;wBAC7B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,YAAY;wBACxB,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBACjE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,YAAY;wBAC1B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,kBAAkB;wBAC9B,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;wBACvE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,kBAAkB;wBAChC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,gBAAgB;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wBACrE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,gBAAgB;wBAC9B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAQ9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;kCAQhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;kCAQlE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 7005, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/BusinessPlan.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TextArea } from '@/components/forms';\nimport { BusinessPlanData, ApplicationFormComponentProps } from './index';\n\ninterface BusinessPlanProps extends ApplicationFormComponentProps {\n  data: BusinessPlanData;\n  onChange: (data: BusinessPlanData) => void;\n}\n\nconst BusinessPlan: React.FC<BusinessPlanProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false\n}) => {\n  const handleInputChange = (field: keyof BusinessPlanData, value: string) => {\n    onChange({\n      ...data,\n      [field]: value\n    });\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Business Plan\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide comprehensive business planning information including financial projections and strategic analysis\n        </p>\n      </div>\n\n      {/* Market Analysis */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Market Analysis\n        </h3>\n        \n        <TextArea\n          label=\"Market Analysis and Research\"\n          value={data.marketAnalysis}\n          onChange={(e) => handleInputChange('marketAnalysis', e.target.value)}\n          placeholder=\"Provide detailed market analysis including market size, trends, competition, and opportunities\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.marketAnalysis}\n          helperText=\"Include market research data, competitor analysis, and market positioning strategy\"\n        />\n      </div>\n\n      {/* Financial Projections */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Financial Projections\n        </h3>\n        \n        <TextArea\n          label=\"Financial Projections and Cash Flow\"\n          value={data.financialProjections}\n          onChange={(e) => handleInputChange('financialProjections', e.target.value)}\n          placeholder=\"Provide detailed financial projections for at least 3 years including revenue, expenses, and cash flow\"\n          rows={7}\n          required\n          disabled={disabled}\n          error={errors.financialProjections}\n          helperText=\"Include revenue forecasts, operating expenses, capital requirements, and break-even analysis\"\n        />\n      </div>\n\n      {/* Competitive Advantage */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Competitive Advantage\n        </h3>\n        \n        <TextArea\n          label=\"Competitive Advantage and Differentiation\"\n          value={data.competitiveAdvantage}\n          onChange={(e) => handleInputChange('competitiveAdvantage', e.target.value)}\n          placeholder=\"Describe your competitive advantages, unique value proposition, and how you differentiate from competitors\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.competitiveAdvantage}\n          helperText=\"Include unique features, cost advantages, technology, or market positioning that sets you apart\"\n        />\n      </div>\n\n      {/* Risk Assessment */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Risk Assessment and Mitigation\n        </h3>\n        \n        <TextArea\n          label=\"Risk Assessment\"\n          value={data.riskAssessment}\n          onChange={(e) => handleInputChange('riskAssessment', e.target.value)}\n          placeholder=\"Identify potential business risks and describe your mitigation strategies\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.riskAssessment}\n          helperText=\"Include operational, financial, regulatory, and market risks with corresponding mitigation plans\"\n        />\n      </div>\n\n      {/* Implementation Timeline */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Implementation Timeline\n        </h3>\n        \n        <TextArea\n          label=\"Implementation Timeline and Milestones\"\n          value={data.implementationTimeline}\n          onChange={(e) => handleInputChange('implementationTimeline', e.target.value)}\n          placeholder=\"Provide a detailed timeline for business implementation including key milestones and deliverables\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.implementationTimeline}\n          helperText=\"Include phases, timelines, resource requirements, and critical success factors\"\n        />\n      </div>\n\n      {/* Business Plan Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-line-chart-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\n                Financial Planning\n              </h4>\n              <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\n                Provide realistic financial projections with supporting assumptions and methodology.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-trophy-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-1\">\n                Competitive Edge\n              </h4>\n              <p className=\"text-green-700 dark:text-green-300 text-sm\">\n                Clearly articulate what makes your business unique and sustainable.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-shield-check-line text-red-600 dark:text-red-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-red-900 dark:text-red-100 mb-1\">\n                Risk Management\n              </h4>\n              <p className=\"text-red-700 dark:text-red-300 text-sm\">\n                Demonstrate thorough risk analysis and practical mitigation strategies.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Information Notice */}\n      <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800\">\n        <div className=\"flex items-start\">\n          <i className=\"ri-information-line text-yellow-600 dark:text-yellow-400 text-lg mr-3 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n              Business Plan Requirements\n            </h4>\n            <ul className=\"text-yellow-700 dark:text-yellow-300 text-sm space-y-1\">\n              <li>• Provide detailed financial projections for at least 3 years</li>\n              <li>• Include comprehensive market research and analysis</li>\n              <li>• Demonstrate clear understanding of competitive landscape</li>\n              <li>• Show realistic implementation timeline with achievable milestones</li>\n              <li>• Address potential risks and provide mitigation strategies</li>\n              <li>• Ensure financial projections align with proposed service scope</li>\n              <li>• Include supporting documentation and data sources where applicable</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BusinessPlan;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAWA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAA+B;QACxD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,cAAc;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACnE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,cAAc;wBAC5B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,oBAAoB;wBAChC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;wBACzE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,oBAAoB;wBAClC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,oBAAoB;wBAChC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;wBACzE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,oBAAoB;wBAClC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,cAAc;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACnE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,cAAc;wBAC5B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,sBAAsB;wBAClC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;wBAC3E,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,sBAAsB;wBACpC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0D;;;;;;sDAGxE,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 7470, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/LegalHistory.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TextArea } from '@/components/forms';\nimport { LegalHistoryData, ApplicationFormComponentProps } from './index';\n\ninterface LegalHistoryProps extends ApplicationFormComponentProps {\n  data: LegalHistoryData;\n  onChange: (data: LegalHistoryData) => void;\n}\n\nconst LegalHistory: React.FC<LegalHistoryProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false\n}) => {\n  const handleInputChange = (field: keyof LegalHistoryData, value: string) => {\n    onChange({\n      ...data,\n      [field]: value\n    });\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Legal History and Compliance\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide complete information about legal history, compliance record, and any regulatory issues\n        </p>\n      </div>\n\n      {/* Previous Violations */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Previous Violations\n        </h3>\n        \n        <TextArea\n          label=\"Previous Regulatory Violations\"\n          value={data.previousViolations}\n          onChange={(e) => handleInputChange('previousViolations', e.target.value)}\n          placeholder=\"List any previous regulatory violations, penalties, or sanctions. If none, please state 'None' or 'Not Applicable'\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.previousViolations}\n          helperText=\"Include details of violations, dates, penalties imposed, and corrective actions taken\"\n        />\n      </div>\n\n      {/* Court Cases */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Legal Proceedings\n        </h3>\n        \n        <TextArea\n          label=\"Court Cases and Legal Proceedings\"\n          value={data.courtCases}\n          onChange={(e) => handleInputChange('courtCases', e.target.value)}\n          placeholder=\"Describe any ongoing or past court cases, legal disputes, or proceedings involving the company or key personnel. If none, please state 'None'\"\n          rows={5}\n          required\n          disabled={disabled}\n          error={errors.courtCases}\n          helperText=\"Include case details, current status, outcomes, and any impact on business operations\"\n        />\n      </div>\n\n      {/* Regulatory History */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Regulatory History\n        </h3>\n        \n        <TextArea\n          label=\"Regulatory History and Interactions\"\n          value={data.regulatoryHistory}\n          onChange={(e) => handleInputChange('regulatoryHistory', e.target.value)}\n          placeholder=\"Provide details of your history with regulatory bodies, including previous licenses, applications, and regulatory interactions\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.regulatoryHistory}\n          helperText=\"Include previous licenses held, application outcomes, regulatory inspections, and compliance assessments\"\n        />\n      </div>\n\n      {/* Compliance Record */}\n      <div className=\"space-y-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Compliance Record\n        </h3>\n        \n        <TextArea\n          label=\"Compliance Record and Measures\"\n          value={data.complianceRecord}\n          onChange={(e) => handleInputChange('complianceRecord', e.target.value)}\n          placeholder=\"Describe your compliance record, internal compliance measures, and commitment to regulatory adherence\"\n          rows={6}\n          required\n          disabled={disabled}\n          error={errors.complianceRecord}\n          helperText=\"Include compliance policies, training programs, monitoring systems, and commitment to ongoing compliance\"\n        />\n      </div>\n\n      {/* Legal History Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-alert-line text-red-600 dark:text-red-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-red-900 dark:text-red-100 mb-1\">\n                Full Disclosure Required\n              </h4>\n              <p className=\"text-red-700 dark:text-red-300 text-sm\">\n                Complete and honest disclosure of all legal issues is mandatory. \n                Failure to disclose may result in application rejection.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-shield-check-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-1\">\n                Compliance Commitment\n              </h4>\n              <p className=\"text-green-700 dark:text-green-300 text-sm\">\n                Demonstrate your commitment to regulatory compliance and \n                continuous improvement in compliance practices.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Declaration Section */}\n      <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Legal Declaration\n        </h4>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p className=\"text-sm text-gray-700 dark:text-gray-300\">\n              I hereby declare that all information provided in this legal history section is true, \n              complete, and accurate to the best of my knowledge.\n            </p>\n          </div>\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p className=\"text-sm text-gray-700 dark:text-gray-300\">\n              I understand that providing false or misleading information may result in the \n              rejection of this application or revocation of any license granted.\n            </p>\n          </div>\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p className=\"text-sm text-gray-700 dark:text-gray-300\">\n              I commit to maintaining compliance with all applicable laws and regulations \n              if this license is granted.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Information Notice */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n        <div className=\"flex items-start\">\n          <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2\">\n              Legal History Guidelines\n            </h4>\n            <ul className=\"text-blue-700 dark:text-blue-300 text-sm space-y-1\">\n              <li>• Provide complete and honest disclosure of all legal matters</li>\n              <li>• Include details of any violations, even if resolved or minor</li>\n              <li>• Explain circumstances and corrective actions taken</li>\n              <li>• If no legal issues exist, clearly state \"None\" or \"Not Applicable\"</li>\n              <li>• Include legal matters involving key personnel and directors</li>\n              <li>• Demonstrate commitment to future compliance</li>\n              <li>• Attach supporting documentation where relevant</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LegalHistory;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAWA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAA+B;QACxD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,kBAAkB;wBAC9B,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;wBACvE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,kBAAkB;wBAChC,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,UAAU;wBACtB,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,UAAU;wBACxB,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,iBAAiB;wBAC7B,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,iBAAiB;wBAC/B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC,8KAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,OAAO,KAAK,gBAAgB;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wBACrE,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO,OAAO,gBAAgB;wBAC9B,YAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0D;;;;;;sDAGxE,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;;;;;;kCAQ5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAK1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAK1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;0BAS9D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 7954, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ReviewSubmit.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { ApplicationFormData, ApplicationFormComponentProps } from './index';\n\ninterface ReviewSubmitProps extends ApplicationFormComponentProps {\n  data: ApplicationFormData;\n  onSubmit: () => void;\n  isSubmitting?: boolean;\n}\n\nconst ReviewSubmit: React.FC<ReviewSubmitProps> = ({\n  data,\n  onSubmit,\n  disabled = false,\n  isSubmitting = false\n}) => {\n  const sections = [\n    {\n      title: 'Applicant Information',\n      icon: 'ri-user-line',\n      data: data.applicantInfo,\n      fields: [\n        { label: 'Applicant Name', value: data.applicantInfo.applicantName },\n        { label: 'Email', value: data.applicantInfo.email },\n        { label: 'Telephone', value: data.applicantInfo.telephone },\n        { label: 'Physical Address', value: `${data.applicantInfo.physicalStreet}, ${data.applicantInfo.physicalCity}, ${data.applicantInfo.physicalCountry}` }\n      ]\n    },\n    {\n      title: 'Company Profile',\n      icon: 'ri-building-line',\n      data: data.companyProfile,\n      fields: [\n        { label: 'Business Registration No.', value: data.companyProfile.businessRegistrationNo },\n        { label: 'TPIN', value: data.companyProfile.tpin },\n        { label: 'Date of Incorporation', value: data.companyProfile.dateOfIncorporation },\n        { label: 'Shareholders', value: `${data.companyProfile.shareholders.length} shareholders` },\n        { label: 'Directors', value: `${data.companyProfile.directors.length} directors` }\n      ]\n    },\n    {\n      title: 'Management Team',\n      icon: 'ri-team-line',\n      data: data.management,\n      fields: [\n        { label: 'Management Team Members', value: `${data.management.managementTeam.length} members` },\n        { label: 'Organizational Structure', value: data.management.organizationalStructure ? 'Provided' : 'Not provided' }\n      ]\n    },\n    {\n      title: 'Professional Services',\n      icon: 'ri-service-line',\n      data: data.professionalServices,\n      fields: [\n        { label: 'Technical Support', value: data.professionalServices.technicalSupport ? 'Provided' : 'Not provided' },\n        { label: 'Maintenance Arrangements', value: data.professionalServices.maintenanceArrangements ? 'Provided' : 'Not provided' }\n      ]\n    },\n    {\n      title: 'Business Information',\n      icon: 'ri-briefcase-line',\n      data: data.businessInfo,\n      fields: [\n        { label: 'Business Description', value: data.businessInfo.businessDescription ? 'Provided' : 'Not provided' },\n        { label: 'Business Model', value: data.businessInfo.businessModel ? 'Provided' : 'Not provided' }\n      ]\n    },\n    {\n      title: 'Service Scope',\n      icon: 'ri-global-line',\n      data: data.serviceScope,\n      fields: [\n        { label: 'Services Offered', value: data.serviceScope.servicesOffered ? 'Provided' : 'Not provided' },\n        { label: 'Geographic Coverage', value: data.serviceScope.geographicCoverage ? 'Provided' : 'Not provided' }\n      ]\n    },\n    {\n      title: 'Business Plan',\n      icon: 'ri-line-chart-line',\n      data: data.businessPlan,\n      fields: [\n        { label: 'Market Analysis', value: data.businessPlan.marketAnalysis ? 'Provided' : 'Not provided' },\n        { label: 'Financial Projections', value: data.businessPlan.financialProjections ? 'Provided' : 'Not provided' }\n      ]\n    },\n    {\n      title: 'Legal History',\n      icon: 'ri-shield-check-line',\n      data: data.legalHistory,\n      fields: [\n        { label: 'Previous Violations', value: data.legalHistory.previousViolations ? 'Disclosed' : 'Not disclosed' },\n        { label: 'Compliance Record', value: data.legalHistory.complianceRecord ? 'Provided' : 'Not provided' }\n      ]\n    }\n  ];\n\n  const getCompletionStatus = (sectionData: any) => {\n    if (!sectionData) return 'incomplete';\n    \n    const values = Object.values(sectionData);\n    const hasRequiredFields = values.some(value => {\n      if (Array.isArray(value)) return value.length > 0;\n      return value && String(value).trim() !== '';\n    });\n    \n    return hasRequiredFields ? 'complete' : 'incomplete';\n  };\n\n  const allSectionsComplete = sections.every(section => getCompletionStatus(section.data) === 'complete');\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Review & Submit Application\n        </h2>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Please review all information before submitting your application\n        </p>\n      </div>\n\n      {/* Application Summary */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {sections.map((section, index) => {\n          const status = getCompletionStatus(section.data);\n          return (\n            <div\n              key={index}\n              className={`border rounded-lg p-4 ${\n                status === 'complete'\n                  ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'\n                  : 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20'\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center\">\n                  <i className={`${section.icon} text-lg mr-2 ${\n                    status === 'complete' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'\n                  }`}></i>\n                  <h3 className={`font-medium ${\n                    status === 'complete' ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'\n                  }`}>\n                    {section.title}\n                  </h3>\n                </div>\n                <span className={`text-xs px-2 py-1 rounded-full ${\n                  status === 'complete'\n                    ? 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200'\n                    : 'bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200'\n                }`}>\n                  {status === 'complete' ? 'Complete' : 'Incomplete'}\n                </span>\n              </div>\n              \n              <div className=\"space-y-1\">\n                {section.fields.map((field, fieldIndex) => (\n                  <div key={fieldIndex} className=\"flex justify-between text-sm\">\n                    <span className={status === 'complete' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>\n                      {field.label}:\n                    </span>\n                    <span className={`font-medium ${status === 'complete' ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>\n                      {field.value || 'Not provided'}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Submission Status */}\n      <div className={`rounded-lg p-4 border ${\n        allSectionsComplete\n          ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'\n          : 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20'\n      }`}>\n        <div className=\"flex items-start\">\n          <i className={`text-lg mr-3 mt-0.5 ${\n            allSectionsComplete\n              ? 'ri-checkbox-circle-line text-green-600 dark:text-green-400'\n              : 'ri-error-warning-line text-yellow-600 dark:text-yellow-400'\n          }`}></i>\n          <div>\n            <h4 className={`text-sm font-medium mb-1 ${\n              allSectionsComplete\n                ? 'text-green-900 dark:text-green-100'\n                : 'text-yellow-900 dark:text-yellow-100'\n            }`}>\n              {allSectionsComplete ? 'Application Ready for Submission' : 'Application Incomplete'}\n            </h4>\n            <p className={`text-sm ${\n              allSectionsComplete\n                ? 'text-green-700 dark:text-green-300'\n                : 'text-yellow-700 dark:text-yellow-300'\n            }`}>\n              {allSectionsComplete\n                ? 'All required sections have been completed. You can now submit your application.'\n                : 'Please complete all required sections before submitting your application.'\n              }\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Terms and Conditions */}\n      <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Terms and Conditions\n        </h4>\n        <div className=\"space-y-3 text-sm text-gray-700 dark:text-gray-300\">\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p>\n              I certify that all information provided in this application is true, complete, and accurate.\n            </p>\n          </div>\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p>\n              I understand that providing false or misleading information may result in the rejection of this application.\n            </p>\n          </div>\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p>\n              I agree to comply with all applicable laws, regulations, and license conditions if this application is approved.\n            </p>\n          </div>\n          <div className=\"flex items-start\">\n            <i className=\"ri-checkbox-line text-primary text-lg mr-3 mt-0.5\"></i>\n            <p>\n              I authorize MACRA to verify the information provided and conduct necessary background checks.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Submit Button */}\n      <div className=\"flex justify-center pt-6\">\n        <button\n          type=\"button\"\n          onClick={onSubmit}\n          disabled={disabled || !allSectionsComplete || isSubmitting}\n          className={`px-8 py-3 rounded-lg font-medium text-white transition-colors duration-200 ${\n            allSectionsComplete && !disabled && !isSubmitting\n              ? 'bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'\n              : 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'\n          }`}\n        >\n          {isSubmitting ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Submitting Application...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-send-plane-line mr-2\"></i>\n              Submit Application\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Information Notice */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n        <div className=\"flex items-start\">\n          <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\n              After Submission\n            </h4>\n            <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\n              Once submitted, your application will be reviewed by MACRA. You will receive email notifications \n              about the status of your application. The review process may take several weeks depending on \n              the license type and complexity of your application.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewSubmit;\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,QAAQ,EACR,WAAW,KAAK,EAChB,eAAe,KAAK,EACrB;IACC,MAAM,WAAW;QACf;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,aAAa;YACxB,QAAQ;gBACN;oBAAE,OAAO;oBAAkB,OAAO,KAAK,aAAa,CAAC,aAAa;gBAAC;gBACnE;oBAAE,OAAO;oBAAS,OAAO,KAAK,aAAa,CAAC,KAAK;gBAAC;gBAClD;oBAAE,OAAO;oBAAa,OAAO,KAAK,aAAa,CAAC,SAAS;gBAAC;gBAC1D;oBAAE,OAAO;oBAAoB,OAAO,GAAG,KAAK,aAAa,CAAC,cAAc,CAAC,EAAE,EAAE,KAAK,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,aAAa,CAAC,eAAe,EAAE;gBAAC;aACvJ;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,cAAc;YACzB,QAAQ;gBACN;oBAAE,OAAO;oBAA6B,OAAO,KAAK,cAAc,CAAC,sBAAsB;gBAAC;gBACxF;oBAAE,OAAO;oBAAQ,OAAO,KAAK,cAAc,CAAC,IAAI;gBAAC;gBACjD;oBAAE,OAAO;oBAAyB,OAAO,KAAK,cAAc,CAAC,mBAAmB;gBAAC;gBACjF;oBAAE,OAAO;oBAAgB,OAAO,GAAG,KAAK,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;gBAAC;gBAC1F;oBAAE,OAAO;oBAAa,OAAO,GAAG,KAAK,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;gBAAC;aAClF;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,UAAU;YACrB,QAAQ;gBACN;oBAAE,OAAO;oBAA2B,OAAO,GAAG,KAAK,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAAC;gBAC9F;oBAAE,OAAO;oBAA4B,OAAO,KAAK,UAAU,CAAC,uBAAuB,GAAG,aAAa;gBAAe;aACnH;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,oBAAoB;YAC/B,QAAQ;gBACN;oBAAE,OAAO;oBAAqB,OAAO,KAAK,oBAAoB,CAAC,gBAAgB,GAAG,aAAa;gBAAe;gBAC9G;oBAAE,OAAO;oBAA4B,OAAO,KAAK,oBAAoB,CAAC,uBAAuB,GAAG,aAAa;gBAAe;aAC7H;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,YAAY;YACvB,QAAQ;gBACN;oBAAE,OAAO;oBAAwB,OAAO,KAAK,YAAY,CAAC,mBAAmB,GAAG,aAAa;gBAAe;gBAC5G;oBAAE,OAAO;oBAAkB,OAAO,KAAK,YAAY,CAAC,aAAa,GAAG,aAAa;gBAAe;aACjG;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,YAAY;YACvB,QAAQ;gBACN;oBAAE,OAAO;oBAAoB,OAAO,KAAK,YAAY,CAAC,eAAe,GAAG,aAAa;gBAAe;gBACpG;oBAAE,OAAO;oBAAuB,OAAO,KAAK,YAAY,CAAC,kBAAkB,GAAG,aAAa;gBAAe;aAC3G;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,YAAY;YACvB,QAAQ;gBACN;oBAAE,OAAO;oBAAmB,OAAO,KAAK,YAAY,CAAC,cAAc,GAAG,aAAa;gBAAe;gBAClG;oBAAE,OAAO;oBAAyB,OAAO,KAAK,YAAY,CAAC,oBAAoB,GAAG,aAAa;gBAAe;aAC/G;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,KAAK,YAAY;YACvB,QAAQ;gBACN;oBAAE,OAAO;oBAAuB,OAAO,KAAK,YAAY,CAAC,kBAAkB,GAAG,cAAc;gBAAgB;gBAC5G;oBAAE,OAAO;oBAAqB,OAAO,KAAK,YAAY,CAAC,gBAAgB,GAAG,aAAa;gBAAe;aACvG;QACH;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,aAAa,OAAO;QAEzB,MAAM,SAAS,OAAO,MAAM,CAAC;QAC7B,MAAM,oBAAoB,OAAO,IAAI,CAAC,CAAA;YACpC,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,MAAM,MAAM,GAAG;YAChD,OAAO,SAAS,OAAO,OAAO,IAAI,OAAO;QAC3C;QAEA,OAAO,oBAAoB,aAAa;IAC1C;IAEA,MAAM,sBAAsB,SAAS,KAAK,CAAC,CAAA,UAAW,oBAAoB,QAAQ,IAAI,MAAM;IAE5F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oBACtB,MAAM,SAAS,oBAAoB,QAAQ,IAAI;oBAC/C,qBACE,8OAAC;wBAEC,WAAW,CAAC,sBAAsB,EAChC,WAAW,aACP,4EACA,mEACJ;;0CAEF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAW,GAAG,QAAQ,IAAI,CAAC,cAAc,EAC1C,WAAW,aAAa,uCAAuC,kCAC/D;;;;;;0DACF,8OAAC;gDAAG,WAAW,CAAC,YAAY,EAC1B,WAAW,aAAa,uCAAuC,kCAC/D;0DACC,QAAQ,KAAK;;;;;;;;;;;;kDAGlB,8OAAC;wCAAK,WAAW,CAAC,+BAA+B,EAC/C,WAAW,aACP,sEACA,6DACJ;kDACC,WAAW,aAAa,aAAa;;;;;;;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC1B,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAK,WAAW,WAAW,aAAa,uCAAuC;;oDAC7E,MAAM,KAAK;oDAAC;;;;;;;0DAEf,8OAAC;gDAAK,WAAW,CAAC,YAAY,EAAE,WAAW,aAAa,uCAAuC,kCAAkC;0DAC9H,MAAM,KAAK,IAAI;;;;;;;uCALV;;;;;;;;;;;uBA7BT;;;;;gBAyCX;;;;;;0BAIF,8OAAC;gBAAI,WAAW,CAAC,sBAAsB,EACrC,sBACI,4EACA,+EACJ;0BACA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAW,CAAC,oBAAoB,EACjC,sBACI,+DACA,8DACJ;;;;;;sCACF,8OAAC;;8CACC,8OAAC;oCAAG,WAAW,CAAC,yBAAyB,EACvC,sBACI,uCACA,wCACJ;8CACC,sBAAsB,qCAAqC;;;;;;8CAE9D,8OAAC;oCAAE,WAAW,CAAC,QAAQ,EACrB,sBACI,uCACA,wCACJ;8CACC,sBACG,oFACA;;;;;;;;;;;;;;;;;;;;;;;0BAQZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;kDAAE;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;kDAAE;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;kDAAE;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;kDACb,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS;oBACT,UAAU,YAAY,CAAC,uBAAuB;oBAC9C,WAAW,CAAC,2EAA2E,EACrF,uBAAuB,CAAC,YAAY,CAAC,eACjC,uGACA,mDACJ;8BAED,6BACC;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAA8B;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE;uCAEe", "debugId": null}}, {"offset": {"line": 8502, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationProgress.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams, usePathname } from 'next/navigation';\nimport { getLicenseTypeStepConfig, StepConfig } from '@/config/licenseTypeStepConfig';\nimport { CustomerApiService } from '@/lib/customer-api';\n\ninterface ApplicationProgressProps {\n  className?: string;\n}\n\nconst ApplicationProgress: React.FC<ApplicationProgressProps> = ({ className = '' }) => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const pathname = usePathname();\n\n  // Create customer API service instance\n  const customerApi = new CustomerApiService();\n\n  // Get query parameters\n  const licenseCategoryId = searchParams.get('license_category_id');\n  const applicationId = searchParams.get('application_id');\n\n  // State\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // Get current step from pathname\n  const getCurrentStepIndex = () => {\n    const pathSegments = pathname.split('/');\n    const currentStepId = pathSegments[pathSegments.length - 1];\n    return applicationSteps.findIndex(step => step.id === currentStepId);\n  };\n\n  const currentStepIndex = getCurrentStepIndex();\n\n  // Load data\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        if (!licenseCategoryId) {\n          setLoading(false);\n          return;\n        }\n\n        // Load license category\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\n        const licenseType = await customerApi.getLicenseType(category.license_type_id);\n\n        if (!category || !licenseType) {\n          setLoading(false);\n          return;\n        }\n\n        const config = getLicenseTypeStepConfig(licenseType.code);\n        if (config) {\n          setApplicationSteps(config.steps);\n        }\n\n        setLoading(false);\n      } catch (err: any) {\n        console.error('Error loading application steps:', err);\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [licenseCategoryId, applicationId]);\n\n  // Navigation handlers\n  const handleStepClick = (stepIndex: number) => {\n    // Prevent navigation to future steps if not editing an existing application\n    if (!applicationId && stepIndex > currentStepIndex) {\n      return;\n    }\n\n    const step = applicationSteps[stepIndex];\n    const params = new URLSearchParams();\n    params.set('license_category_id', licenseCategoryId!);\n    if (applicationId) {\n      params.set('application_id', applicationId);\n    }\n    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);\n  };\n\n  // Don't render if still loading or no data\n  if (loading || !applicationSteps.length) {\n    return null;\n  }\n\n  return (\n    <div className={`mb-8 ${className}`}>\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\n        <h3 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Application Progress ({currentStepIndex + 1} of {applicationSteps.length})\n        </h3>\n        <div className=\"space-y-2\">\n          {applicationSteps.map((step, index) => {\n            const isAccessible = applicationId || index <= currentStepIndex;\n            return (\n              <div\n                key={step.id}\n                className={`flex items-center p-2 rounded-md transition-colors ${\n                  isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'\n                } ${\n                  index === currentStepIndex\n                    ? 'bg-primary/10 border border-primary/20'\n                    : index < currentStepIndex\n                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'\n                    : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'\n                }`}\n                onClick={() => isAccessible && handleStepClick(index)}\n              >\n                <div\n                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${\n                    index === currentStepIndex\n                      ? 'bg-primary text-white'\n                      : index < currentStepIndex\n                      ? 'bg-green-500 text-white'\n                      : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'\n                  }`}\n                >\n                  {index < currentStepIndex ? (\n                    <i className=\"ri-check-line\"></i>\n                  ) : (\n                    index + 1\n                  )}\n                </div>\n                <div className=\"flex-1\">\n                  <div className={`text-sm font-medium ${\n                    index === currentStepIndex\n                      ? 'text-primary'\n                      : index < currentStepIndex\n                      ? 'text-green-700 dark:text-green-300'\n                      : 'text-gray-600 dark:text-gray-400'\n                  }`}>\n                    {step.name}\n                  </div>\n                  {step.description && (\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      {step.description}\n                    </div>\n                  )}\n                </div>\n                {step.required && (\n                  <span className=\"text-xs text-red-500 ml-2\">*</span>\n                )}\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ApplicationProgress;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,sBAA0D,CAAC,EAAE,YAAY,EAAE,EAAE;IACjF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,cAAc,IAAI,6HAAA,CAAA,qBAAkB;IAE1C,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,MAAM,sBAAsB;QAC1B,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAC3D,OAAO,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxD;IAEA,MAAM,mBAAmB;IAEzB,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,IAAI,CAAC,mBAAmB;oBACtB,WAAW;oBACX;gBACF;gBAEA,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAE7E,IAAI,CAAC,YAAY,CAAC,aAAa;oBAC7B,WAAW;oBACX;gBACF;gBAEA,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,IAAI;gBACxD,IAAI,QAAQ;oBACV,oBAAoB,OAAO,KAAK;gBAClC;gBAEA,WAAW;YACb,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAmB;KAAc;IAErC,sBAAsB;IACtB,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QACxC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAClC,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC5E;IAEA,2CAA2C;IAC3C,IAAI,WAAW,CAAC,iBAAiB,MAAM,EAAE;QACvC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;wBAA4D;wBACjD,mBAAmB;wBAAE;wBAAK,iBAAiB,MAAM;wBAAC;;;;;;;8BAE3E,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;wBAC3B,MAAM,eAAe,iBAAiB,SAAS;wBAC/C,qBACE,8OAAC;4BAEC,WAAW,CAAC,mDAAmD,EAC7D,eAAe,mBAAmB,gCACnC,CAAC,EACA,UAAU,mBACN,2CACA,QAAQ,mBACR,mFACA,8EACJ;4BACF,SAAS,IAAM,gBAAgB,gBAAgB;;8CAE/C,8OAAC;oCACC,WAAW,CAAC,+EAA+E,EACzF,UAAU,mBACN,0BACA,QAAQ,mBACR,4BACA,iEACJ;8CAED,QAAQ,iCACP,8OAAC;wCAAE,WAAU;;;;;+CAEb,QAAQ;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,mBACN,iBACA,QAAQ,mBACR,uCACA,oCACJ;sDACC,KAAK,IAAI;;;;;;wCAEX,KAAK,WAAW,kBACf,8OAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;gCAItB,KAAK,QAAQ,kBACZ,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;2BA5CzC,KAAK,EAAE;;;;;oBAgDlB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 8688, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/index.ts"], "sourcesContent": ["// Application Form Components\r\nexport { default as ApplicantInfo } from './ApplicantInfo';\r\nexport { default as CompanyProfile } from './CompanyProfile';\r\nexport { default as Management } from './Management';\r\nexport { default as ProfessionalServices } from './ProfessionalServices';\r\nexport { default as BusinessInfo } from './BusinessInfo';\r\nexport { default as ServiceScope } from './ServiceScope';\r\nexport { default as BusinessPlan } from './BusinessPlan';\r\nexport { default as LegalHistory } from './LegalHistory';\r\nexport { default as ReviewSubmit } from './ReviewSubmit';\r\n\r\n// Application Progress Component\r\nexport { default as ApplicationProgress } from './ApplicationProgress';\r\n\r\n// Types for application form data\r\nexport interface ApplicantInfoData {\r\n  applicantName: string;\r\n  postalPoBox: string;\r\n  postalCity: string;\r\n  postalCountry: string;\r\n  physicalStreet: string;\r\n  physicalCity: string;\r\n  physicalCountry: string;\r\n  telephone: string;\r\n  fax: string;\r\n  email: string;\r\n}\r\n\r\nexport interface ShareholderData {\r\n  name: string;\r\n  nationality: string;\r\n  address: string;\r\n  shareholding: string;\r\n}\r\n\r\nexport interface DirectorData {\r\n  name: string;\r\n  nationality: string;\r\n  address: string;\r\n}\r\n\r\nexport interface CompanyProfileData {\r\n  shareholders: ShareholderData[];\r\n  directors: DirectorData[];\r\n  foreignOwnership: string;\r\n  businessRegistrationNo: string;\r\n  tpin: string;\r\n  website: string;\r\n  dateOfIncorporation: string;\r\n  placeOfIncorporation: string;\r\n}\r\n\r\nexport interface ManagementTeamMember {\r\n  name: string;\r\n  position: string;\r\n  qualifications: string;\r\n  experience: string;\r\n}\r\n\r\nexport interface ManagementData {\r\n  managementTeam: ManagementTeamMember[];\r\n  organizationalStructure: string;\r\n  keyPersonnel: string;\r\n}\r\n\r\nexport interface ProfessionalServicesData {\r\n  consultants: string;\r\n  serviceProviders: string;\r\n  technicalSupport: string;\r\n  maintenanceArrangements: string;\r\n}\r\n\r\nexport interface BusinessInfoData {\r\n  businessDescription: string;\r\n  operationalAreas: string;\r\n  facilities: string;\r\n  equipment: string;\r\n  businessModel: string;\r\n}\r\n\r\nexport interface ServiceScopeData {\r\n  servicesOffered: string;\r\n  targetMarket: string;\r\n  geographicCoverage: string;\r\n  serviceStandards: string;\r\n}\r\n\r\nexport interface BusinessPlanData {\r\n  marketAnalysis: string;\r\n  financialProjections: string;\r\n  competitiveAdvantage: string;\r\n  riskAssessment: string;\r\n  implementationTimeline: string;\r\n}\r\n\r\nexport interface LegalHistoryData {\r\n  previousViolations: string;\r\n  courtCases: string;\r\n  regulatoryHistory: string;\r\n  complianceRecord: string;\r\n}\r\n\r\nexport interface ApplicationFormData {\r\n  applicantInfo: ApplicantInfoData;\r\n  companyProfile: CompanyProfileData;\r\n  management: ManagementData;\r\n  professionalServices: ProfessionalServicesData;\r\n  businessInfo: BusinessInfoData;\r\n  serviceScope: ServiceScopeData;\r\n  businessPlan: BusinessPlanData;\r\n  legalHistory: LegalHistoryData;\r\n}\r\n\r\n// Component props interfaces\r\nexport interface ApplicationFormComponentProps {\r\n  data: any;\r\n  onChange: (data: any) => void;\r\n  errors?: Record<string, string>;\r\n  disabled?: boolean;\r\n}\r\n"], "names": [], "mappings": "AAAA,8BAA8B;;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,iCAAiC;AACjC", "debugId": null}}, {"offset": {"line": 8745, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/applicant-info/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport TextInput from '@/components/common/TextInput';\r\nimport Select from '@/components/common/Select';\r\nimport { LicenseCategory } from '@/services/licenseCategoryService';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport { applicantService } from '@/services/applicantService';\r\nimport { applicationProgressService } from '@/services/applicationProgressService';\r\nimport { validateSection } from '@/utils/formValidation';\r\nimport { getLicenseTypeStepConfig, StepConfig, LicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\nimport { LicenseType } from '@/services/licenseTypeService';\r\nimport { ApplicationProgress } from '@/components/applications';\r\n\r\nconst ApplicantInfoPage: React.FC = () => {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { isAuthenticated, loading: authLoading } = useAuth();\r\n\r\n  // Create customer API service instance\r\n  const customerApi = new CustomerApiService();\r\n\r\n  // Get query parameters\r\n  const licenseCategoryId = searchParams.get('license_category_id');\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State\r\n  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);\r\n  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);\r\n\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Form data state - aligned with Applicant interface\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    business_registration_number: '',\r\n    tpin: '',\r\n    website: '',\r\n    email: '',\r\n    phone: '',\r\n    fax: '',\r\n    level_of_insurance_cover: '',\r\n    date_incorporation: '',\r\n    place_incorporation: '',\r\n    // Address fields (will be saved to address table via address_id)\r\n    postal_address: '',\r\n    physical_address: '',\r\n    city: '',\r\n    district: '',\r\n    postal_code: ''\r\n  });\r\n\r\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\r\n  const [applicationCreated, setApplicationCreated] = useState(false);\r\n  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);\r\n\r\n  const currentStepIndex = applicationSteps.findIndex(step => step.id === 'applicant-info');\r\n\r\n  // Form handling functions\r\n  const handleFormChange = (field: string, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n    setHasUnsavedChanges(true);\r\n\r\n    // Clear validation error for this field\r\n    if (validationErrors[field]) {\r\n      setValidationErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[field];\r\n        return newErrors;\r\n      });\r\n    }\r\n  };\r\n\r\n  // Save function\r\n  const handleSave = async () => {\r\n    setIsSaving(true);\r\n    setValidationErrors({});\r\n\r\n    try {\r\n      // Validate form data\r\n      const validation = validateSection(formData as Record<string, any>, 'applicantInfo');\r\n      if (!validation.isValid) {\r\n        setValidationErrors(validation.errors || {});\r\n        setIsSaving(false);\r\n        return;\r\n      }\r\n\r\n      let currentApplicationId = applicationId;\r\n\r\n      // If no application exists, create one\r\n      if (!currentApplicationId) {\r\n        console.log('Creating new application...');\r\n\r\n        // Create applicant data matching the Applicant interface\r\n        const applicantData = {\r\n          name: formData.name,\r\n          business_registration_number: formData.business_registration_number,\r\n          tpin: formData.tpin,\r\n          website: formData.website,\r\n          email: formData.email,\r\n          phone: formData.phone,\r\n          fax: formData.fax,\r\n          level_of_insurance_cover: formData.level_of_insurance_cover,\r\n          date_incorporation: formData.date_incorporation,\r\n          place_incorporation: formData.place_incorporation,\r\n          // Address fields\r\n          postal_address: formData.postal_address,\r\n          physical_address: formData.physical_address,\r\n          city: formData.city,\r\n          district: formData.district,\r\n          postal_code: formData.postal_code\r\n        };\r\n\r\n        const applicant = await applicantService.createApplicant(applicantData);\r\n        console.log('Applicant created:', applicant);\r\n\r\n        // Then create application\r\n        const applicationNumber = `APP-${Date.now()}-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;\r\n        const applicationData = {\r\n          application_number: applicationNumber,\r\n          license_category_id: licenseCategoryId!,\r\n          applicant_id: applicant.applicant_id,\r\n          status: 'draft' as any\r\n        };\r\n\r\n        const application = await applicationService.createApplication(applicationData);\r\n\r\n        currentApplicationId = application.application_id;\r\n        setCreatedApplicationId(currentApplicationId);\r\n        setApplicationCreated(true);\r\n      } else {\r\n        // Update existing applicant data\r\n        const applicantData = {\r\n          name: formData.name,\r\n          business_registration_number: formData.business_registration_number,\r\n          tpin: formData.tpin,\r\n          website: formData.website,\r\n          email: formData.email,\r\n          phone: formData.phone,\r\n          fax: formData.fax,\r\n          level_of_insurance_cover: formData.level_of_insurance_cover,\r\n          date_incorporation: formData.date_incorporation,\r\n          place_incorporation: formData.place_incorporation,\r\n          // Address fields\r\n          postal_address: formData.postal_address,\r\n          physical_address: formData.physical_address,\r\n          city: formData.city,\r\n          district: formData.district,\r\n          postal_code: formData.postal_code\r\n        };\r\n\r\n        // Get application to find applicant_id\r\n        const application = await applicationService.getApplication(currentApplicationId);\r\n        if (application.applicant_id) {\r\n          await applicantService.updateApplicant(application.applicant_id, applicantData);\r\n        }\r\n      }\r\n      // Save form data\r\n      setHasUnsavedChanges(false);\r\n\r\n    } catch (error) {\r\n      setValidationErrors({ save: 'Failed to save application. Please try again.' });\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // Authentication check\r\n  useEffect(() => {\r\n    if (!authLoading && !isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n      return;\r\n    }\r\n  }, [isAuthenticated, authLoading, router]);\r\n\r\n  // Load data\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        if (!licenseCategoryId) {\r\n          setError('License category ID is required');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        // Load license category\r\n        const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n        const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n\r\n        if (!category) {\r\n          setError('License category not found');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        setLicenseCategory(category);\r\n        setLicenseType(licenseType);\r\n\r\n        if (!category.license_type_id) {\r\n          setError('License category is missing license type information');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        const config = getLicenseTypeStepConfig(licenseType.code);\r\n\r\n        if (!config) {\r\n          setError(`No configuration found for license type: ${category.license_type_id}`);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        setLicenseTypeConfig(config);\r\n        setApplicationSteps(config.steps);\r\n\r\n        console.log('Data loading completed successfully');\r\n        setLoading(false);\r\n      } catch (err: any) {\r\n        console.error('Error loading data:', err);\r\n        console.error('Error details:', {\r\n          message: err.message,\r\n          response: err.response?.data,\r\n          status: err.response?.status\r\n        });\r\n\r\n        // Provide more specific error message\r\n        let errorMessage = 'Failed to load application data';\r\n        if (err.response?.status === 404) {\r\n          errorMessage = `License category not found (ID: ${licenseCategoryId}). Please go back to the applications page and select a valid license category.`;\r\n        } else if (err.response?.status === 401) {\r\n          errorMessage = 'You are not authorized to access this license category. Please log in again.';\r\n        } else if (err.response?.status === 500) {\r\n          errorMessage = 'Server error occurred. Please try again later or contact support.';\r\n        } else if (err.message) {\r\n          errorMessage = `Error: ${err.message}`;\r\n        }\r\n\r\n        setError(errorMessage);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (isAuthenticated && !authLoading) {\r\n      loadData();\r\n    }\r\n  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);\r\n\r\n  // Navigation handlers for the ApplicantInfo component\r\n  const handleNext = () => {\r\n    const nextStep = applicationSteps[currentStepIndex + 1];\r\n    if (nextStep) {\r\n      const params = new URLSearchParams();\r\n      params.set('license_category_id', licenseCategoryId!);\r\n      if (applicationId) {\r\n        params.set('application_id', applicationId);\r\n      }\r\n      router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    router.push('/customer/applications');\r\n  };\r\n\r\n  const handleStepComplete = (stepId: string, data?: any) => {\r\n    console.log('Step completed:', stepId, data);\r\n    // If data contains applicationId, update the URL\r\n    if (data?.applicationId) {\r\n      const params = new URLSearchParams(window.location.search);\r\n      params.set('application_id', data.applicationId);\r\n      const newUrl = `${window.location.pathname}?${params.toString()}`;\r\n      window.history.replaceState({}, '', newUrl);\r\n    }\r\n  };\r\n\r\n  const handleStepError = (stepId: string, errors: any) => {\r\n    console.error('Step error:', stepId, errors);\r\n    setError(typeof errors === 'string' ? errors : 'An error occurred while processing the step');\r\n  };\r\n\r\n  const handleStepClick = (stepIndex: number) => {\r\n    // Prevent navigation to future steps if not editing an existing application\r\n    if (!applicationId && stepIndex > currentStepIndex) {\r\n      setError('Please complete the current step before proceeding to the next step');\r\n      return;\r\n    }\r\n\r\n    const step = applicationSteps[stepIndex];\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId!);\r\n    if (applicationId) {\r\n      params.set('application_id', applicationId);\r\n    }\r\n    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);\r\n  };\r\n\r\n  const handleBackToApplications = () => {\r\n    router.push('/customer/applications');\r\n  };\r\n  // Loading state\r\n  if (authLoading || loading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application form...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"mb-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500\"></i>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n              Error Loading Application\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\r\n              {error}\r\n            </p>\r\n            <button\r\n              onClick={() => router.push('/customer/applications')}\r\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              <i className=\"ri-arrow-left-line mr-2\"></i>\r\n              Back to Applications\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  const currentStep = applicationSteps[currentStepIndex];\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <button\r\n            onClick={handleBackToApplications}\r\n            className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4\"\r\n          >\r\n            <i className=\"ri-arrow-left-line mr-1\"></i>\r\n            Back to Applications\r\n          </button>\r\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\r\n            {licenseCategory?.name} Application\r\n          </h1>\r\n          <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\r\n            {applicationId ? 'Edit your application' : 'Complete your license application'}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Progress Steps - Vertical Layout for Scalability */}\r\n        <ApplicationProgress />\r\n\r\n        {/* Current Step Content */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              {currentStep.name}\r\n            </h2>\r\n          </div>\r\n\r\n          <div className=\"p-6\">\r\n            {/* Header */}\r\n            <div className=\"mb-6\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                Applicant Information\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n                Please provide your personal information. This will create your application record.\r\n              </p>\r\n              {!applicationId && (\r\n                <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\r\n                  <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                    <i className=\"ri-information-line mr-1\"></i>\r\n                    Your application will be created when you save this step.\r\n                  </p>\r\n                </div>\r\n              )}\r\n              {applicationCreated && (\r\n                <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\r\n                  <p className=\"text-sm text-green-700 dark:text-green-300\">\r\n                    <i className=\"ri-check-line mr-1\"></i>\r\n                    Application created: {createdApplicationId?.slice(0, 8)}...\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Business Information */}\r\n              <div className=\"md:col-span-2\">\r\n                <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Business Information</h4>\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <TextInput\r\n                  label=\"Business/Organization Name\"\r\n                  value={formData.name || ''}\r\n                  onChange={(e) => handleFormChange('name', e.target.value)}\r\n                  placeholder=\"Enter the full legal name of your business or organization\"\r\n                  required\r\n                  error={validationErrors.name}\r\n                />\r\n              </div>\r\n\r\n              <TextInput\r\n                label=\"Business Registration Number\"\r\n                value={formData.business_registration_number || ''}\r\n                onChange={(e) => handleFormChange('business_registration_number', e.target.value)}\r\n                placeholder=\"e.g., BN123456789\"\r\n                required\r\n                error={validationErrors.business_registration_number}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"TPIN (Tax Payer Identification Number)\"\r\n                value={formData.tpin || ''}\r\n                onChange={(e) => handleFormChange('tpin', e.target.value)}\r\n                placeholder=\"e.g., 12-345-678-9\"\r\n                required\r\n                error={validationErrors.tpin}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Website\"\r\n                type=\"url\"\r\n                value={formData.website || ''}\r\n                onChange={(e) => handleFormChange('website', e.target.value)}\r\n                placeholder=\"https://www.example.com\"\r\n                error={validationErrors.website}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Date of Incorporation\"\r\n                type=\"date\"\r\n                value={formData.date_incorporation || ''}\r\n                onChange={(e) => handleFormChange('date_incorporation', e.target.value)}\r\n                required\r\n                error={validationErrors.date_incorporation}\r\n              />\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <TextInput\r\n                  label=\"Place of Incorporation\"\r\n                  value={formData.place_incorporation || ''}\r\n                  onChange={(e) => handleFormChange('place_incorporation', e.target.value)}\r\n                  placeholder=\"e.g., Blantyre, Malawi\"\r\n                  required\r\n                  error={validationErrors.place_incorporation}\r\n                />\r\n              </div>\r\n\r\n              {/* Contact Information */}\r\n              <div className=\"md:col-span-2\">\r\n                <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6\">Contact Information</h4>\r\n              </div>\r\n\r\n              <TextInput\r\n                label=\"Email Address\"\r\n                type=\"email\"\r\n                value={formData.email || ''}\r\n                onChange={(e) => handleFormChange('email', e.target.value)}\r\n                placeholder=\"<EMAIL>\"\r\n                required\r\n                error={validationErrors.email}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Phone Number\"\r\n                value={formData.phone || ''}\r\n                onChange={(e) => handleFormChange('phone', e.target.value)}\r\n                placeholder=\"+265 1 234 567\"\r\n                required\r\n                error={validationErrors.phone}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Fax Number\"\r\n                value={formData.fax || ''}\r\n                onChange={(e) => handleFormChange('fax', e.target.value)}\r\n                placeholder=\"+265 1 234 568\"\r\n                error={validationErrors.fax}\r\n              />\r\n\r\n              <TextInput\r\n                label=\"Level of Insurance Cover\"\r\n                value={formData.level_of_insurance_cover || ''}\r\n                onChange={(e) => handleFormChange('level_of_insurance_cover', e.target.value)}\r\n                placeholder=\"e.g., $1,000,000 USD\"\r\n                error={validationErrors.level_of_insurance_cover}\r\n              />\r\n            </div>\r\n\r\n            {/* Address Information */}\r\n            <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\r\n              <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n                Address Information\r\n              </h4>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div className=\"md:col-span-2\">\r\n                  <TextInput\r\n                    label=\"Postal Address\"\r\n                    value={formData.postal_address || ''}\r\n                    onChange={(e) => handleFormChange('postal_address', e.target.value)}\r\n                    placeholder=\"P.O. Box 123\"\r\n                    required\r\n                    error={validationErrors.postal_address}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"md:col-span-2\">\r\n                  <TextInput\r\n                    label=\"Physical Address\"\r\n                    value={formData.physical_address || ''}\r\n                    onChange={(e) => handleFormChange('physical_address', e.target.value)}\r\n                    placeholder=\"Street address\"\r\n                    required\r\n                    error={validationErrors.physical_address}\r\n                  />\r\n                </div>\r\n\r\n                <TextInput\r\n                  label=\"City\"\r\n                  value={formData.city || ''}\r\n                  onChange={(e) => handleFormChange('city', e.target.value)}\r\n                  required\r\n                  error={validationErrors.city}\r\n                />\r\n\r\n                <Select\r\n                  label=\"District\"\r\n                  value={formData.district || ''}\r\n                  onChange={(value) => handleFormChange('district', value)}\r\n                  options={[\r\n                    { value: 'Blantyre', label: 'Blantyre' },\r\n                    { value: 'Lilongwe', label: 'Lilongwe' },\r\n                    { value: 'Mzuzu', label: 'Mzuzu' },\r\n                    { value: 'Zomba', label: 'Zomba' },\r\n                    { value: 'Kasungu', label: 'Kasungu' },\r\n                    { value: 'Mangochi', label: 'Mangochi' },\r\n                    { value: 'Salima', label: 'Salima' },\r\n                    { value: 'Karonga', label: 'Karonga' },\r\n                    { value: 'Other', label: 'Other' }\r\n                  ]}\r\n                  required\r\n                  error={validationErrors.district}\r\n                />\r\n\r\n                <TextInput\r\n                  label=\"Postal Code\"\r\n                  value={formData.postal_code || ''}\r\n                  onChange={(e) => handleFormChange('postal_code', e.target.value)}\r\n                  error={validationErrors.postal_code}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Error Display */}\r\n            {validationErrors.save && (\r\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\r\n                  <div>\r\n                    <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\r\n                      Error Saving Application\r\n                    </h3>\r\n                    <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\r\n                      {validationErrors.save}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Success Message */}\r\n            {applicationCreated && !applicationId && (\r\n              <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\r\n                <div className=\"flex items-center\">\r\n                  <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3\"></i>\r\n                  <div>\r\n                    <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\r\n                      Application Created Successfully!\r\n                    </h3>\r\n                    <p className=\"text-green-700 dark:text-green-300 text-sm mt-1\">\r\n                      Your application has been created. You can now continue to the next step.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n              {applicationCreated && !applicationId ? (\r\n                <>\r\n                  {/* Save Changes Button (for edit mode) */}\r\n                  <button\r\n                    onClick={handleSave}\r\n                    disabled={isSaving}\r\n                    className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    {isSaving ? (\r\n                      <>\r\n                        <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                        Saving...\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <i className=\"ri-save-line mr-2\"></i>\r\n                        Save Changes\r\n                      </>\r\n                    )}\r\n                  </button>\r\n\r\n                  {/* Continue to Next Step Button */}\r\n                  <button\r\n                    onClick={() => {\r\n                      const nextStep = applicationSteps[currentStepIndex + 1];\r\n                      if (nextStep) {\r\n                        const params = new URLSearchParams();\r\n                        params.set('license_category_id', licenseCategoryId!);\r\n                        if (createdApplicationId) {\r\n                          params.set('application_id', createdApplicationId);\r\n                        }\r\n                        router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);\r\n                      }\r\n                    }}\r\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                  >\r\n                    <i className=\"ri-arrow-right-line mr-2\"></i>\r\n                    Continue to Company Profile\r\n                  </button>\r\n                </>\r\n              ) : (\r\n                /* Create/Save Application Button */\r\n                <button\r\n                  onClick={handleSave}\r\n                  disabled={isSaving}\r\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {isSaving ? (\r\n                    <>\r\n                      <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                      {applicationId ? 'Saving...' : 'Creating Application...'}\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <i className=\"ri-save-line mr-2\"></i>\r\n                      {applicationId ? 'Save Changes' : 'Create Application'}\r\n                    </>\r\n                  )}\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n\r\n        </div>\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default ApplicantInfoPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAhBA;;;;;;;;;;;;;;AAkBA,MAAM,oBAA8B;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,uCAAuC;IACvC,MAAM,cAAc,IAAI,6HAAA,CAAA,qBAAkB;IAE1C,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAGnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,qDAAqD;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,8BAA8B;QAC9B,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,KAAK;QACL,0BAA0B;QAC1B,oBAAoB;QACpB,qBAAqB;QACrB,iEAAiE;QACjE,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,MAAM,mBAAmB,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAExE,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC,OAAe;QACvC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,qBAAqB;QAErB,wCAAwC;QACxC,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,aAAa;QACjB,YAAY;QACZ,oBAAoB,CAAC;QAErB,IAAI;YACF,qBAAqB;YACrB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,UAAiC;YACpE,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,oBAAoB,WAAW,MAAM,IAAI,CAAC;gBAC1C,YAAY;gBACZ;YACF;YAEA,IAAI,uBAAuB;YAE3B,uCAAuC;YACvC,IAAI,CAAC,sBAAsB;gBACzB,QAAQ,GAAG,CAAC;gBAEZ,yDAAyD;gBACzD,MAAM,gBAAgB;oBACpB,MAAM,SAAS,IAAI;oBACnB,8BAA8B,SAAS,4BAA4B;oBACnE,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,KAAK,SAAS,GAAG;oBACjB,0BAA0B,SAAS,wBAAwB;oBAC3D,oBAAoB,SAAS,kBAAkB;oBAC/C,qBAAqB,SAAS,mBAAmB;oBACjD,iBAAiB;oBACjB,gBAAgB,SAAS,cAAc;oBACvC,kBAAkB,SAAS,gBAAgB;oBAC3C,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;gBACnC;gBAEA,MAAM,YAAY,MAAM,mIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;gBACzD,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,0BAA0B;gBAC1B,MAAM,oBAAoB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW,IAAI;gBAC1G,MAAM,kBAAkB;oBACtB,oBAAoB;oBACpB,qBAAqB;oBACrB,cAAc,UAAU,YAAY;oBACpC,QAAQ;gBACV;gBAEA,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;gBAE/D,uBAAuB,YAAY,cAAc;gBACjD,wBAAwB;gBACxB,sBAAsB;YACxB,OAAO;gBACL,iCAAiC;gBACjC,MAAM,gBAAgB;oBACpB,MAAM,SAAS,IAAI;oBACnB,8BAA8B,SAAS,4BAA4B;oBACnE,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,KAAK,SAAS,GAAG;oBACjB,0BAA0B,SAAS,wBAAwB;oBAC3D,oBAAoB,SAAS,kBAAkB;oBAC/C,qBAAqB,SAAS,mBAAmB;oBACjD,iBAAiB;oBACjB,gBAAgB,SAAS,cAAc;oBACvC,kBAAkB,SAAS,gBAAgB;oBAC3C,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;gBACnC;gBAEA,uCAAuC;gBACvC,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBAC5D,IAAI,YAAY,YAAY,EAAE;oBAC5B,MAAM,mIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,YAAY,YAAY,EAAE;gBACnE;YACF;YACA,iBAAiB;YACjB,qBAAqB;QAEvB,EAAE,OAAO,OAAO;YACd,oBAAoB;gBAAE,MAAM;YAAgD;QAC9E,SAAU;YACR,YAAY;QACd;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,IAAI,CAAC,mBAAmB;oBACtB,SAAS;oBACT,WAAW;oBACX;gBACF;gBACA,wBAAwB;gBACxB,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAE7E,IAAI,CAAC,UAAU;oBACb,SAAS;oBACT,WAAW;oBACX;gBACF;gBAEA,mBAAmB;gBACnB,eAAe;gBAEf,IAAI,CAAC,SAAS,eAAe,EAAE;oBAC7B,SAAS;oBACT,WAAW;oBACX;gBACF;gBAEA,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,IAAI;gBAExD,IAAI,CAAC,QAAQ;oBACX,SAAS,CAAC,yCAAyC,EAAE,SAAS,eAAe,EAAE;oBAC/E,WAAW;oBACX;gBACF;gBACA,qBAAqB;gBACrB,oBAAoB,OAAO,KAAK;gBAEhC,QAAQ,GAAG,CAAC;gBACZ,WAAW;YACb,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,QAAQ,KAAK,CAAC,kBAAkB;oBAC9B,SAAS,IAAI,OAAO;oBACpB,UAAU,IAAI,QAAQ,EAAE;oBACxB,QAAQ,IAAI,QAAQ,EAAE;gBACxB;gBAEA,sCAAsC;gBACtC,IAAI,eAAe;gBACnB,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBAChC,eAAe,CAAC,gCAAgC,EAAE,kBAAkB,+EAA+E,CAAC;gBACtJ,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,eAAe;gBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,eAAe;gBACjB,OAAO,IAAI,IAAI,OAAO,EAAE;oBACtB,eAAe,CAAC,OAAO,EAAE,IAAI,OAAO,EAAE;gBACxC;gBAEA,SAAS;gBACT,WAAW;YACb;QACF;QAEA,IAAI,mBAAmB,CAAC,aAAa;YACnC;QACF;IACF,GAAG;QAAC;QAAmB;QAAe;QAAiB;KAAY;IAEnE,sDAAsD;IACtD,MAAM,aAAa;QACjB,MAAM,WAAW,gBAAgB,CAAC,mBAAmB,EAAE;QACvD,IAAI,UAAU;YACZ,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,uBAAuB;YAClC,IAAI,eAAe;gBACjB,OAAO,GAAG,CAAC,kBAAkB;YAC/B;YACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;QAChF;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,QAAQ,GAAG,CAAC,mBAAmB,QAAQ;QACvC,iDAAiD;QACjD,IAAI,MAAM,eAAe;YACvB,MAAM,SAAS,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YACzD,OAAO,GAAG,CAAC,kBAAkB,KAAK,aAAa;YAC/C,MAAM,SAAS,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;YACjE,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;QACtC;IACF;IAEA,MAAM,kBAAkB,CAAC,QAAgB;QACvC,QAAQ,KAAK,CAAC,eAAe,QAAQ;QACrC,SAAS,OAAO,WAAW,WAAW,SAAS;IACjD;IAEA,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD,SAAS;YACT;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QACxC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAClC,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC5E;IAEA,MAAM,2BAA2B;QAC/B,OAAO,IAAI,CAAC;IACd;IACA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,8OAAC;4BAAE,WAAU;sCACV;;;;;;sCAEH,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;IAOvD;IAEA,MAAM,cAAc,gBAAgB,CAAC,iBAAiB;IAEtD,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;sCAG7C,8OAAC;4BAAG,WAAU;;gCACX,iBAAiB;gCAAK;;;;;;;sCAEzB,8OAAC;4BAAE,WAAU;sCACV,gBAAgB,0BAA0B;;;;;;;;;;;;8BAK/C,8OAAC,2MAAA,CAAA,sBAAmB;;;;;8BAGpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,YAAY,IAAI;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;wCAG5D,CAAC,+BACA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;;;;;wCAKjD,oCACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAE,WAAU;;;;;;oDAAyB;oDAChB,sBAAsB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;8CAMhE,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;;;;;;sDAG5E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;gDACR,OAAM;gDACN,OAAO,SAAS,IAAI,IAAI;gDACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACxD,aAAY;gDACZ,QAAQ;gDACR,OAAO,iBAAiB,IAAI;;;;;;;;;;;sDAIhC,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,4BAA4B,IAAI;4CAChD,UAAU,CAAC,IAAM,iBAAiB,gCAAgC,EAAE,MAAM,CAAC,KAAK;4CAChF,aAAY;4CACZ,QAAQ;4CACR,OAAO,iBAAiB,4BAA4B;;;;;;sDAGtD,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,IAAI,IAAI;4CACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACxD,aAAY;4CACZ,QAAQ;4CACR,OAAO,iBAAiB,IAAI;;;;;;sDAG9B,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,OAAO,IAAI;4CAC3B,UAAU,CAAC,IAAM,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC3D,aAAY;4CACZ,OAAO,iBAAiB,OAAO;;;;;;sDAGjC,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,kBAAkB,IAAI;4CACtC,UAAU,CAAC,IAAM,iBAAiB,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACtE,QAAQ;4CACR,OAAO,iBAAiB,kBAAkB;;;;;;sDAG5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;gDACR,OAAM;gDACN,OAAO,SAAS,mBAAmB,IAAI;gDACvC,UAAU,CAAC,IAAM,iBAAiB,uBAAuB,EAAE,MAAM,CAAC,KAAK;gDACvE,aAAY;gDACZ,QAAQ;gDACR,OAAO,iBAAiB,mBAAmB;;;;;;;;;;;sDAK/C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAiE;;;;;;;;;;;sDAGjF,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,KAAK,IAAI;4CACzB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4CACzD,aAAY;4CACZ,QAAQ;4CACR,OAAO,iBAAiB,KAAK;;;;;;sDAG/B,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,KAAK,IAAI;4CACzB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4CACzD,aAAY;4CACZ,QAAQ;4CACR,OAAO,iBAAiB,KAAK;;;;;;sDAG/B,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,GAAG,IAAI;4CACvB,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;4CACvD,aAAY;4CACZ,OAAO,iBAAiB,GAAG;;;;;;sDAG7B,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,wBAAwB,IAAI;4CAC5C,UAAU,CAAC,IAAM,iBAAiB,4BAA4B,EAAE,MAAM,CAAC,KAAK;4CAC5E,aAAY;4CACZ,OAAO,iBAAiB,wBAAwB;;;;;;;;;;;;8CAKpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAI1E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;wDACR,OAAM;wDACN,OAAO,SAAS,cAAc,IAAI;wDAClC,UAAU,CAAC,IAAM,iBAAiB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAClE,aAAY;wDACZ,QAAQ;wDACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;8DAI1C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;wDACR,OAAM;wDACN,OAAO,SAAS,gBAAgB,IAAI;wDACpC,UAAU,CAAC,IAAM,iBAAiB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACpE,aAAY;wDACZ,QAAQ;wDACR,OAAO,iBAAiB,gBAAgB;;;;;;;;;;;8DAI5C,8OAAC,yIAAA,CAAA,UAAS;oDACR,OAAM;oDACN,OAAO,SAAS,IAAI,IAAI;oDACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACxD,QAAQ;oDACR,OAAO,iBAAiB,IAAI;;;;;;8DAG9B,8OAAC,sIAAA,CAAA,UAAM;oDACL,OAAM;oDACN,OAAO,SAAS,QAAQ,IAAI;oDAC5B,UAAU,CAAC,QAAU,iBAAiB,YAAY;oDAClD,SAAS;wDACP;4DAAE,OAAO;4DAAY,OAAO;wDAAW;wDACvC;4DAAE,OAAO;4DAAY,OAAO;wDAAW;wDACvC;4DAAE,OAAO;4DAAS,OAAO;wDAAQ;wDACjC;4DAAE,OAAO;4DAAS,OAAO;wDAAQ;wDACjC;4DAAE,OAAO;4DAAW,OAAO;wDAAU;wDACrC;4DAAE,OAAO;4DAAY,OAAO;wDAAW;wDACvC;4DAAE,OAAO;4DAAU,OAAO;wDAAS;wDACnC;4DAAE,OAAO;4DAAW,OAAO;wDAAU;wDACrC;4DAAE,OAAO;4DAAS,OAAO;wDAAQ;qDAClC;oDACD,QAAQ;oDACR,OAAO,iBAAiB,QAAQ;;;;;;8DAGlC,8OAAC,yIAAA,CAAA,UAAS;oDACR,OAAM;oDACN,OAAO,SAAS,WAAW,IAAI;oDAC/B,UAAU,CAAC,IAAM,iBAAiB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC/D,OAAO,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;gCAMxC,iBAAiB,IAAI,kBACpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqD;;;;;;kEAGnE,8OAAC;wDAAE,WAAU;kEACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;gCAQ/B,sBAAsB,CAAC,+BACtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyD;;;;;;kEAGvE,8OAAC;wDAAE,WAAU;kEAAkD;;;;;;;;;;;;;;;;;;;;;;;8CASvE,8OAAC;oCAAI,WAAU;8CACZ,sBAAsB,CAAC,8BACtB;;0DAEE,8OAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,yBACC;;sEACE,8OAAC;4DAAE,WAAU;;;;;;wDAAyC;;iFAIxD;;sEACE,8OAAC;4DAAE,WAAU;;;;;;wDAAwB;;;;;;;;0DAO3C,8OAAC;gDACC,SAAS;oDACP,MAAM,WAAW,gBAAgB,CAAC,mBAAmB,EAAE;oDACvD,IAAI,UAAU;wDACZ,MAAM,SAAS,IAAI;wDACnB,OAAO,GAAG,CAAC,uBAAuB;wDAClC,IAAI,sBAAsB;4DACxB,OAAO,GAAG,CAAC,kBAAkB;wDAC/B;wDACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;oDAChF;gDACF;gDACA,WAAU;;kEAEV,8OAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;uDAKhD,kCAAkC,iBAClC,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,yBACC;;8DACE,8OAAC;oDAAE,WAAU;;;;;;gDACZ,gBAAgB,cAAc;;yEAGjC;;8DACE,8OAAC;oDAAE,WAAU;;;;;;gDACZ,gBAAgB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaxD;uCAEe", "debugId": null}}]}