"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicantsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const applicants_service_1 = require("./applicants.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_applicant_dto_1 = require("../dto/applicant/create-applicant.dto");
const update_applicant_dto_1 = require("../dto/applicant/update-applicant.dto");
const applicant_entity_1 = require("../entities/applicant.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let ApplicantsController = class ApplicantsController {
    applicantsService;
    constructor(applicantsService) {
        this.applicantsService = applicantsService;
    }
    async create(createApplicantDto, req) {
        return this.applicantsService.create(createApplicantDto, req.user.userId);
    }
    async findAll(query) {
        const result = await this.applicantsService.findAll(query);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async search(searchTerm) {
        return this.applicantsService.search(searchTerm);
    }
    async findByBusinessRegistrationNumber(businessRegistrationNumber) {
        return this.applicantsService.findByBusinessRegistrationNumber(businessRegistrationNumber);
    }
    async findByTpin(tpin) {
        return this.applicantsService.findByTpin(tpin);
    }
    async findOne(id) {
        return this.applicantsService.findOne(id);
    }
    async update(id, updateApplicantDto, req) {
        return this.applicantsService.update(id, updateApplicantDto, req.user.userId);
    }
    async remove(id) {
        await this.applicantsService.remove(id);
        return { message: 'Applicant deleted successfully' };
    }
};
exports.ApplicantsController = ApplicantsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new applicant' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Applicant created successfully',
        type: applicant_entity_1.Applicants,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Created new applicant',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_applicant_dto_1.CreateApplicantDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all applicants with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applicants retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Viewed applicants list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search applicants' }),
    (0, swagger_1.ApiQuery)({ name: 'q', description: 'Search term' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search results retrieved successfully',
        type: [applicant_entity_1.Applicants],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Searched applicants',
    }),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('by-business-registration/:businessRegistrationNumber'),
    (0, swagger_1.ApiOperation)({ summary: 'Get applicant by business registration number' }),
    (0, swagger_1.ApiParam)({ name: 'businessRegistrationNumber', description: 'Business registration number' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applicant retrieved successfully',
        type: applicant_entity_1.Applicants,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Viewed applicant by business registration number',
    }),
    __param(0, (0, common_1.Param)('businessRegistrationNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "findByBusinessRegistrationNumber", null);
__decorate([
    (0, common_1.Get)('by-tpin/:tpin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get applicant by TPIN' }),
    (0, swagger_1.ApiParam)({ name: 'tpin', description: 'Tax Payer Identification Number' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applicant retrieved successfully',
        type: applicant_entity_1.Applicants,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Viewed applicant by TPIN',
    }),
    __param(0, (0, common_1.Param)('tpin')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "findByTpin", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get applicant by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Applicant UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applicant retrieved successfully',
        type: applicant_entity_1.Applicants,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Viewed applicant details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update applicant' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Applicant UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applicant updated successfully',
        type: applicant_entity_1.Applicants,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Updated applicant',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_applicant_dto_1.UpdateApplicantDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete applicant' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Applicant UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Applicant deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Applicant',
        description: 'Deleted applicant',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicantsController.prototype, "remove", null);
exports.ApplicantsController = ApplicantsController = __decorate([
    (0, swagger_1.ApiTags)('applicants'),
    (0, common_1.Controller)('applicants'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [applicants_service_1.ApplicantsService])
], ApplicantsController);
//# sourceMappingURL=applicants.controller.js.map