{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NxTYcanOzavf6GTL+lK1iSKVRUpUBVljI2zCk3fePC0=", "__NEXT_PREVIEW_MODE_ID": "34cbbc2680911d506deda650e3c5996c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "68c2a047c3b1cd7d3210292d55d1df48164786e43ac5f467cc18d9daf108099b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c917e28a4d2e77bb9e701448e04a1521647538054b1cb58397a0c61c55e40fd6"}}}, "sortedMiddleware": ["/"], "functions": {}}