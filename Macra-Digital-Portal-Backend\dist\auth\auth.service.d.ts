import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../entities/user.entity';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto } from '../dto/auth/forgot-password.dto';
import { RequestTwoFactorDto, TwoFactorDto } from '../dto/auth/two-factor.dto';
import { MailerService } from '@nestjs-modules/mailer';
export interface JwtPayload {
    email: string;
    sub: string;
    roles?: string[];
}
export interface AuthResponse {
    access_token: string;
    user: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        two_factor_enabled: boolean;
        roles?: string[];
    };
}
export declare class AuthService {
    private usersService;
    private jwtService;
    private mailerService;
    private readonly logger;
    constructor(usersService: UsersService, jwtService: JwtService, mailerService: MailerService);
    validateUser(email: string, password: string): Promise<User | null>;
    login(loginDto: LoginDto): Promise<AuthResponse>;
    register(registerDto: RegisterDto): Promise<AuthResponse>;
    validateJwtPayload(payload: JwtPayload): Promise<User | null>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{
        message: string;
    }>;
    setupTwoFactorAuth(requestTwoFactorDto: RequestTwoFactorDto): Promise<{
        otpAuthUrl: string;
        qrCodeDataUrl: string;
        secret: string;
        message: string;
    }>;
    generateTwoFactorCode(userId: string, action: string): Promise<{
        message: string;
        otpAuthUrl: string;
        hashedToken: string;
        secret: string;
    }>;
    verifyTwoFactorCode(twoFactorDto: TwoFactorDto): Promise<AuthResponse | {
        message: string;
    }>;
}
