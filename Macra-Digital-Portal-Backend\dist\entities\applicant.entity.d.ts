import { User } from './user.entity';
import { Address } from './address.entity';
export declare class Applicants {
    applicant_id: string;
    name: string;
    business_registration_number: string;
    tpin: string;
    website: string;
    email: string;
    phone: string;
    fax?: string;
    level_of_insurance_cover?: string;
    physical_address_id?: string;
    postal_address_id?: string;
    contact_id?: string;
    date_incorporation: Date;
    place_incorporation: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    physical_address?: Address;
    postal_address?: Address;
    creator: User;
    updater?: User;
    generateId(): void;
}
