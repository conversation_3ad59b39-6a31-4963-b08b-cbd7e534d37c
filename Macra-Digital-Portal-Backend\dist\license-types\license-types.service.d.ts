import { Repository } from 'typeorm';
import { LicenseTypes } from '../entities/license-types.entity';
import { CreateLicenseTypeDto } from '../dto/license-types/create-license-type.dto';
import { UpdateLicenseTypeDto } from '../dto/license-types/update-license-type.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class LicenseTypesService {
    private licenseTypesRepository;
    constructor(licenseTypesRepository: Repository<LicenseTypes>);
    findAll(query: PaginateQuery): Promise<PaginatedResult<LicenseTypes>>;
    findOne(id: string): Promise<LicenseTypes>;
    create(createLicenseTypeDto: CreateLicenseTypeDto, userId: string): Promise<LicenseTypes>;
    update(id: string, updateLicenseTypeDto: UpdateLicenseTypeDto, userId: string): Promise<LicenseTypes>;
    remove(id: string): Promise<void>;
}
