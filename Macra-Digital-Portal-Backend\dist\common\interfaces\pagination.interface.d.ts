export interface PaginateQuery {
    page?: number;
    limit?: number;
    sortBy?: string[];
    searchBy?: string[];
    search?: string;
    filter?: Record<string, string | string[]>;
    select?: string[];
}
export interface PaginateConfig<T> {
    sortableColumns: (keyof T)[];
    searchableColumns?: (keyof T)[];
    defaultSortBy?: [keyof T, 'ASC' | 'DESC'][];
    defaultLimit?: number;
    maxLimit?: number;
    filterableColumns?: Record<keyof T, any>;
}
export interface PaginatedResult<T> {
    data: T[];
    meta: {
        itemsPerPage: number;
        totalItems: number;
        currentPage: number;
        totalPages: number;
        sortBy: [string, string][];
        searchBy: string[];
        search: string;
        filter: Record<string, string | string[]>;
    };
    links: {
        first: string;
        previous: string;
        current: string;
        next: string;
        last: string;
    };
}
export interface PaginateOptions {
    page: number;
    limit: number;
    sortBy?: [string, 'ASC' | 'DESC'][];
    searchBy?: string[];
    search?: string;
    filter?: Record<string, string | string[]>;
}
export declare class PaginationTransformer {
    static transform<T>(nestjsPaginateResult: any): PaginatedResult<T>;
}
