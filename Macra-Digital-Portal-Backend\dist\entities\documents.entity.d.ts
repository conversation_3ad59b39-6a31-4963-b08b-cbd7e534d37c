import { User } from './user.entity';
import { Applications } from './applications.entity';
export declare enum DocumentType {
    CERTIFICATE_INCORPORATION = "certificate_incorporation",
    MEMORANDUM_ASSOCIATION = "memorandum_association",
    SHAREHOLDING_STRUCTURE = "shareholding_structure",
    BUSINESS_PLAN = "business_plan",
    FINANCIAL_STATEMENTS = "financial_statements",
    TECHNICAL_PROPOSAL = "technical_proposal",
    COVERAGE_PLAN = "coverage_plan",
    NETWORK_DIAGRAM = "network_diagram",
    EQUIPMENT_SPECIFICATIONS = "equipment_specifications",
    INSURANCE_CERTIFICATE = "insurance_certificate",
    TAX_CLEARANCE = "tax_clearance",
    AUDITED_ACCOUNTS = "audited_accounts",
    BANK_STATEMENT = "bank_statement",
    CV_DOCUMENT = "cv_document",
    OTHER = "other"
}
export declare class Documents {
    document_id: string;
    application_id?: string;
    document_type: DocumentType;
    file_name: string;
    entity_type: string;
    entity_id: string;
    file_path: string;
    file_size: number;
    mime_type: string;
    is_required: boolean;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application?: Applications;
    creator: User;
    updater?: User;
    generateId(): void;
}
