"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseTypesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const license_types_service_1 = require("./license-types.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_license_type_dto_1 = require("../dto/license-types/create-license-type.dto");
const update_license_type_dto_1 = require("../dto/license-types/update-license-type.dto");
const nestjs_paginate_1 = require("nestjs-paginate");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let LicenseTypesController = class LicenseTypesController {
    licenseTypesService;
    constructor(licenseTypesService) {
        this.licenseTypesService = licenseTypesService;
    }
    async findAll(query) {
        return this.licenseTypesService.findAll(query);
    }
    async findOne(id) {
        return this.licenseTypesService.findOne(id);
    }
    async create(createLicenseTypeDto, req) {
        return this.licenseTypesService.create(createLicenseTypeDto, req.user.userId);
    }
    async update(id, updateLicenseTypeDto, req) {
        return this.licenseTypesService.update(id, updateLicenseTypeDto, req.user.userId);
    }
    async remove(id) {
        await this.licenseTypesService.remove(id);
        return { message: 'License type deleted successfully' };
    }
};
exports.LicenseTypesController = LicenseTypesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all license types' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of license types retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseType',
        description: 'Viewed license types list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LicenseTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get license type by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License type retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License type not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseType',
        description: 'Viewed license type details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new license type' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'License type created successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'License type with this name already exists',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseType',
        description: 'Created new license type',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_license_type_dto_1.CreateLicenseTypeDto, Object]),
    __metadata("design:returntype", Promise)
], LicenseTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update license type' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License type updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License type not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'License type with this name already exists',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseType',
        description: 'Updated license type',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_license_type_dto_1.UpdateLicenseTypeDto, Object]),
    __metadata("design:returntype", Promise)
], LicenseTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete license type' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License type deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License type not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseType',
        description: 'Deleted license type',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseTypesController.prototype, "remove", null);
exports.LicenseTypesController = LicenseTypesController = __decorate([
    (0, swagger_1.ApiTags)('License Types'),
    (0, common_1.Controller)('license-types'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [license_types_service_1.LicenseTypesService])
], LicenseTypesController);
//# sourceMappingURL=license-types.controller.js.map