// Application Form Components
export { default as ApplicantInfo } from './ApplicantInfo';
export { default as CompanyProfile } from './CompanyProfile';
export { default as Management } from './Management';
export { default as ProfessionalServices } from './ProfessionalServices';
export { default as BusinessInfo } from './BusinessInfo';
export { default as ServiceScope } from './ServiceScope';
export { default as BusinessPlan } from './BusinessPlan';
export { default as LegalHistory } from './LegalHistory';
export { default as ReviewSubmit } from './ReviewSubmit';

// Types for application form data
export interface ApplicantInfoData {
  applicantName: string;
  postalPoBox: string;
  postalCity: string;
  postalCountry: string;
  physicalStreet: string;
  physicalCity: string;
  physicalCountry: string;
  telephone: string;
  fax: string;
  email: string;
}

export interface ShareholderData {
  name: string;
  nationality: string;
  address: string;
  shareholding: string;
}

export interface DirectorData {
  name: string;
  nationality: string;
  address: string;
}

export interface CompanyProfileData {
  shareholders: ShareholderData[];
  directors: DirectorData[];
  foreignOwnership: string;
  businessRegistrationNo: string;
  tpin: string;
  website: string;
  dateOfIncorporation: string;
  placeOfIncorporation: string;
}

export interface ManagementTeamMember {
  name: string;
  position: string;
  qualifications: string;
  experience: string;
}

export interface ManagementData {
  managementTeam: ManagementTeamMember[];
  organizationalStructure: string;
  keyPersonnel: string;
}

export interface ProfessionalServicesData {
  consultants: string;
  serviceProviders: string;
  technicalSupport: string;
  maintenanceArrangements: string;
}

export interface BusinessInfoData {
  businessDescription: string;
  operationalAreas: string;
  facilities: string;
  equipment: string;
  businessModel: string;
}

export interface ServiceScopeData {
  servicesOffered: string;
  targetMarket: string;
  geographicCoverage: string;
  serviceStandards: string;
}

export interface BusinessPlanData {
  marketAnalysis: string;
  financialProjections: string;
  competitiveAdvantage: string;
  riskAssessment: string;
  implementationTimeline: string;
}

export interface LegalHistoryData {
  previousViolations: string;
  courtCases: string;
  regulatoryHistory: string;
  complianceRecord: string;
}

export interface ApplicationFormData {
  applicantInfo: ApplicantInfoData;
  companyProfile: CompanyProfileData;
  management: ManagementData;
  professionalServices: ProfessionalServicesData;
  businessInfo: BusinessInfoData;
  serviceScope: ServiceScopeData;
  businessPlan: BusinessPlanData;
  legalHistory: LegalHistoryData;
}

// Component props interfaces
export interface ApplicationFormComponentProps {
  data: any;
  onChange: (data: any) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
}
